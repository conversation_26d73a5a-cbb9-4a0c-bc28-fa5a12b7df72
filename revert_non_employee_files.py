#!/usr/bin/env python3
"""
Script to revert only non-Employee related child table changes.
Keep only Employee-related child table fixes for a clean PR.
"""

import subprocess
import os

def main():
    """Revert non-Employee related files"""
    
    # Employee-related child tables to KEEP (don't revert these)
    employee_files_to_keep = [
        'csf_tz/csf_tz/doctype/employee_salary_component_limit/employee_salary_component_limit.json',
        'csf_tz/csf_tz/doctype/employee_ot_component/employee_ot_component.json',
        'csf_tz/csf_tz/doctype/employee_piecework_additional_salary/employee_piecework_additional_salary.json',
        'csf_tz/csf_tz/doctype/single_piecework_employees/single_piecework_employees.json',
        'csf_tz/csf_tz/doctype/email_employee_salary_slip/email_employee_salary_slip.json',
        'csf_tz/csf_tz/doctype/salary_slip_ot_component/salary_slip_ot_component.json'
    ]
    
    # All modified files from git status
    all_modified_files = [
        'csf_tz/after_sales_services/doctype/machine_strip_request_item/machine_strip_request_item.json',
        'csf_tz/after_sales_services/doctype/pre_delivery_inspection_details/pre_delivery_inspection_details.json',
        'csf_tz/after_sales_services/doctype/pre_delivery_inspection_reading/pre_delivery_inspection_reading.json',
        'csf_tz/after_sales_services/doctype/reference_payment_table/reference_payment_table.json',
        'csf_tz/after_sales_services/doctype/requested_funds_accounts_table/requested_funds_accounts_table.json',
        'csf_tz/after_sales_services/doctype/requested_funds_details/requested_funds_details.json',
        'csf_tz/clearing_and_forwarding/doctype/bond_history_table/bond_history_table.json',
        'csf_tz/clearing_and_forwarding/doctype/bond_reference_table/bond_reference_table.json',
        'csf_tz/clearing_and_forwarding/doctype/border_procedure_table/border_procedure_table.json',
        'csf_tz/clearing_and_forwarding/doctype/border_processing_vehicle_details/border_processing_vehicle_details.json',
        'csf_tz/clearing_and_forwarding/doctype/cargo_details/cargo_details.json',
        'csf_tz/clearing_and_forwarding/doctype/container_detail/container_detail.json',
        'csf_tz/clearing_and_forwarding/doctype/container_file_closing_information/container_file_closing_information.json',
        'csf_tz/clearing_and_forwarding/doctype/container_issue_detail/container_issue_detail.json',
        'csf_tz/clearing_and_forwarding/doctype/expenses/expenses.json',
        'csf_tz/clearing_and_forwarding/doctype/import_border_procedure_table/import_border_procedure_table.json',
        'csf_tz/clearing_and_forwarding/doctype/mandatory_attachment_table/mandatory_attachment_table.json',
        'csf_tz/clearing_and_forwarding/doctype/packing_list/packing_list.json',
        'csf_tz/clearing_and_forwarding/doctype/permits_table/permits_table.json',
        'csf_tz/clearing_and_forwarding/doctype/reporting_status_table/reporting_status_table.json',
        'csf_tz/clearing_and_forwarding/doctype/required_permit/required_permit.json',
        'csf_tz/csf_tz/doctype/bank_clearance_pro_detail/bank_clearance_pro_detail.json',
        'csf_tz/csf_tz/doctype/bom_additional_costs/bom_additional_costs.json',
        'csf_tz/csf_tz/doctype/csf_tz_bank_charges_detail/csf_tz_bank_charges_detail.json',
        'csf_tz/csf_tz/doctype/delivery_exchange_item_details/delivery_exchange_item_details.json',
        'csf_tz/csf_tz/doctype/delivery_exchange_non_stock_item_details/delivery_exchange_non_stock_item_details.json',
        'csf_tz/csf_tz/doctype/document_attachment/document_attachment.json',
        'csf_tz/csf_tz/doctype/efd_z_report_invoice/efd_z_report_invoice.json',
        'csf_tz/csf_tz/doctype/file_attachment/file_attachment.json',
        'csf_tz/csf_tz/doctype/inter_company_material_request_details/inter_company_material_request_details.json',
        'csf_tz/csf_tz/doctype/inter_company_stock_transfer_details/inter_company_stock_transfer_details.json',
        'csf_tz/csf_tz/doctype/inv_err_detail/inv_err_detail.json',
        'csf_tz/csf_tz/doctype/parking_bill_details/parking_bill_details.json',
        'csf_tz/csf_tz/doctype/parking_bill_items/parking_bill_items.json',
        'csf_tz/csf_tz/doctype/payment_reconciliation_pro_invoice/payment_reconciliation_pro_invoice.json',
        'csf_tz/csf_tz/doctype/payment_reconciliation_pro_payment/payment_reconciliation_pro_payment.json',
        'csf_tz/csf_tz/doctype/piecework_payment_allocation/piecework_payment_allocation.json',
        'csf_tz/csf_tz/doctype/possible_root_cause/possible_root_cause.json',
        'csf_tz/csf_tz/doctype/price_change_request_detail/price_change_request_detail.json',
        'csf_tz/csf_tz/doctype/repack_template_detail/repack_template_detail.json',
        'csf_tz/csf_tz/doctype/reporting_currency_settings_rate/reporting_currency_settings_rate.json',
        'csf_tz/csf_tz/doctype/root_cause_prevention_strategy/root_cause_prevention_strategy.json',
        'csf_tz/csf_tz/doctype/special_closing_balance_detail/special_closing_balance_detail.json',
        'csf_tz/csf_tz/doctype/sql_process_detail/sql_process_detail.json',
        'csf_tz/csf_tz/doctype/station_members/station_members.json',
        'csf_tz/csf_tz/doctype/tra_tax_inv_item/tra_tax_inv_item.json',
        'csf_tz/csf_tz/doctype/tz_insurance_company_detail/tz_insurance_company_detail.json',
        'csf_tz/csf_tz/doctype/tz_insurance_policy_holder_detail/tz_insurance_policy_holder_detail.json',
        'csf_tz/csf_tz/doctype/tz_insurance_vehicle_detail/tz_insurance_vehicle_detail.json',
        'csf_tz/csf_tz/doctype/vehicle_consignment_detail/vehicle_consignment_detail.json',
        'csf_tz/csf_tz/doctype/work_order_consignment_detail/work_order_consignment_detail.json',
        'csf_tz/fleet_management/doctype/air_system_checklist/air_system_checklist.json',
        'csf_tz/fleet_management/doctype/air_system_details/air_system_details.json',
        'csf_tz/fleet_management/doctype/brake_checklist/brake_checklist.json',
        'csf_tz/fleet_management/doctype/brake_system_details/brake_system_details.json',
        'csf_tz/fleet_management/doctype/electrical_checklist/electrical_checklist.json',
        'csf_tz/fleet_management/doctype/electrical_details/electrical_details.json',
        'csf_tz/fleet_management/doctype/electronics_checklist/electronics_checklist.json',
        'csf_tz/fleet_management/doctype/electronics_details/electronics_details.json',
        'csf_tz/fleet_management/doctype/engine_checklist/engine_checklist.json',
        'csf_tz/fleet_management/doctype/engine_details/engine_details.json',
        'csf_tz/fleet_management/doctype/equipment_table/equipment_table.json',
        'csf_tz/fleet_management/doctype/fixed_expense_table/fixed_expense_table.json',
        'csf_tz/fleet_management/doctype/fuel_request_table/fuel_request_table.json',
        'csf_tz/fleet_management/doctype/fuel_system_checklist/fuel_system_checklist.json',
        'csf_tz/fleet_management/doctype/fuel_system_details/fuel_system_details.json',
        'csf_tz/fleet_management/doctype/lighting_checklist/lighting_checklist.json',
        'csf_tz/fleet_management/doctype/lighting_checklist_details/lighting_checklist_details.json',
        'csf_tz/fleet_management/doctype/power_train_checklist/power_train_checklist.json',
        'csf_tz/fleet_management/doctype/power_train_details/power_train_details.json',
        'csf_tz/fleet_management/doctype/route_steps_table/route_steps_table.json',
        'csf_tz/fleet_management/doctype/steering_checklist/steering_checklist.json',
        'csf_tz/fleet_management/doctype/steering_details/steering_details.json',
        'csf_tz/fleet_management/doctype/subtrips_table/subtrips_table.json',
        'csf_tz/fleet_management/doctype/suspension_checklist/suspension_checklist.json',
        'csf_tz/fleet_management/doctype/suspension_details/suspension_details.json',
        'csf_tz/fleet_management/doctype/tire_checklist/tire_checklist.json',
        'csf_tz/fleet_management/doctype/tire_details/tire_details.json',
        'csf_tz/fleet_management/doctype/tires_checklist/tires_checklist.json',
        'csf_tz/fleet_management/doctype/tires_details/tires_details.json',
        'csf_tz/fleet_management/doctype/transport_assignment/transport_assignment.json',
        'csf_tz/fleet_management/doctype/trip_steps_table/trip_steps_table.json',
        'csf_tz/fleet_management/doctype/vehicle_checklist/vehicle_checklist.json',
        'csf_tz/fleet_management/doctype/vehicle_documents/vehicle_documents.json',
        'csf_tz/fleet_management/doctype/vehicle_service/vehicle_service.json',
        'csf_tz/fleet_management/doctype/vehicle_trip_location_update/vehicle_trip_location_update.json',
        'csf_tz/purchase_and_stock_management/doctype/bin_list/bin_list.json',
        'csf_tz/purchase_and_stock_management/doctype/order_tracking_container/order_tracking_container.json',
        'csf_tz/sales_and_marketing/doctype/customer_item/customer_item.json',
        'csf_tz/sales_and_marketing/doctype/payment_plan/payment_plan.json',
        'csf_tz/sales_and_marketing/doctype/products_of_interest/products_of_interest.json',
        'csf_tz/stanbic/doctype/stanbic_payments_info/stanbic_payments_info.json',
        'csf_tz/workshop/doctype/used_items_table/used_items_table.json'
    ]
    
    # Files to revert (exclude Employee-related files)
    files_to_revert = [f for f in all_modified_files if f not in employee_files_to_keep]
    
    print(f"=== Reverting Non-Employee Related Files ===")
    print(f"Total modified files: {len(all_modified_files)}")
    print(f"Employee files to keep: {len(employee_files_to_keep)}")
    print(f"Files to revert: {len(files_to_revert)}")
    
    # Revert files in batches to avoid command line length limits
    batch_size = 20
    reverted_count = 0
    error_count = 0
    
    for i in range(0, len(files_to_revert), batch_size):
        batch = files_to_revert[i:i+batch_size]
        try:
            cmd = ['git', 'restore'] + batch
            subprocess.run(cmd, cwd='/home/<USER>/Desktop/frappe-bench/apps/csf_tz', check=True)
            reverted_count += len(batch)
            print(f"✓ Reverted batch {i//batch_size + 1}: {len(batch)} files")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to revert batch {i//batch_size + 1}: {e}")
            error_count += len(batch)
    
    print(f"\n=== Summary ===")
    print(f"Successfully reverted: {reverted_count} files")
    print(f"Errors: {error_count} files")
    print(f"\n✓ Kept Employee-related files:")
    for file in employee_files_to_keep:
        print(f"  - {file}")


if __name__ == "__main__":
    main()
