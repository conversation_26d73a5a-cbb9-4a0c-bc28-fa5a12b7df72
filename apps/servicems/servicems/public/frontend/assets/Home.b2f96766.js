var Ie=Object.defineProperty,je=Object.defineProperties;var He=Object.getOwnPropertyDescriptors;var ye=Object.getOwnPropertySymbols;var Ue=Object.prototype.hasOwnProperty,Ae=Object.prototype.propertyIsEnumerable;var be=(e,a,t)=>a in e?Ie(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,Z=(e,a)=>{for(var t in a||(a={}))Ue.call(a,t)&&be(e,t,a[t]);if(ye)for(var t of ye(a))Ae.call(a,t)&&be(e,t,a[t]);return e},ve=(e,a)=>je(e,He(a));var ee=(e,a,t)=>new Promise((n,r)=>{var i=o=>{try{c(t.next(o))}catch(u){r(u)}},l=o=>{try{c(t.throw(o))}catch(u){r(u)}},c=o=>o.done?n(o.value):Promise.resolve(o.value).then(i,l);c((t=t.apply(e,a)).next())});import{_ as ie,o as d,c as b,a as C,u as fe,b as Ge,r as j,d as M,e as w,f as _,g as q,h as Be,i as Re,j as L,w as X,n as Qe,k as N,l as V,P as Xe,m as I,p as re,q as oe,s as Y,t as T,v as we,x as pe,y as ze,z as Je,F as G,A as J,B as Ke,C as xe,D as he,E as ke,G as Ze,H as _e,I as z,J as et,K as qe,L as tt}from"./index.7442ea66.js";const at={name:"ErrorMessage",props:["message"],computed:{errorMessage(){return this.message?this.message instanceof Error?this.message.messages||this.message.message:this.message:""}}},nt=["innerHTML"];function rt(e,a,t,n,r,i){return t.message?(d(),b("div",{key:0,class:"whitespace-pre-line text-sm text-red-600",role:"alert",innerHTML:i.errorMessage},null,8,nt)):C("",!0)}var ot=ie(at,[["render",rt]]);const st={class:"h-screen w-60 bg-gray-200 flex flex-col items-start p-6"},it=["src"],lt={__name:"Sidebar",setup(e){fe();function a(){window.location.href="/app/service-booking"}function t(){window.location.href="/app/service-job-card"}function n(){window.location.href="/app/service-ms"}let r=Ge({doctype:"Navbar Settings",name:"Navbar Settings",onError:i=>{if(!i.messages){tos.error(i.message);return}tos.error(i.messages.join(`
`))}});return(i,l)=>{const c=j("Button");return d(),b("div",st,[M(r).doc&&M(r).doc.app_logo?(d(),b("div",{key:0,class:"mb-8 w-24 h-auto rounded-full flex items-center justify-center mx-8",onClick:l[0]||(l[0]=o=>n())},[w("img",{src:M(r).doc.app_logo,alt:"logo",class:"w-full rounded-full"},null,8,it)])):C("",!0),_(c,{class:"mb-8 w-full hover:bg-green-500/50 text-black font-bold py-6 px-4 rounded border-4 border-gray-300",label:"Bookings",onClick:l[1]||(l[1]=o=>a())}),_(c,{class:"w-full hover:bg-green-500/50 text-black font-bold py-6 px-4 rounded border-4 border-gray-300",label:"Job Card",onClick:l[2]||(l[2]=o=>t())})])}}},$e=6048e5,ct=864e5,Ce=Symbol.for("constructDateFrom");function H(e,a){return typeof e=="function"?e(a):e&&typeof e=="object"&&Ce in e?e[Ce](a):e instanceof Date?new e.constructor(a):new Date(a)}function E(e,a){return H(a||e,e)}function ut(e,a,t){const n=E(e,t==null?void 0:t.in);return isNaN(a)?H((t==null?void 0:t.in)||e,NaN):(a&&n.setDate(n.getDate()+a),n)}let dt={};function ce(){return dt}function se(e,a){var c,o,u,S,g,P,h,k;const t=ce(),n=(k=(h=(S=(u=a==null?void 0:a.weekStartsOn)!=null?u:(o=(c=a==null?void 0:a.locale)==null?void 0:c.options)==null?void 0:o.weekStartsOn)!=null?S:t.weekStartsOn)!=null?h:(P=(g=t.locale)==null?void 0:g.options)==null?void 0:P.weekStartsOn)!=null?k:0,r=E(e,a==null?void 0:a.in),i=r.getDay(),l=(i<n?7:0)+i-n;return r.setDate(r.getDate()-l),r.setHours(0,0,0,0),r}function le(e,a){return se(e,ve(Z({},a),{weekStartsOn:1}))}function We(e,a){const t=E(e,a==null?void 0:a.in),n=t.getFullYear(),r=H(t,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const i=le(r),l=H(t,0);l.setFullYear(n,0,4),l.setHours(0,0,0,0);const c=le(l);return t.getTime()>=i.getTime()?n+1:t.getTime()>=c.getTime()?n:n-1}function Se(e){const a=E(e),t=new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()));return t.setUTCFullYear(a.getFullYear()),+e-+t}function Ne(e,...a){const t=H.bind(null,e||a.find(n=>typeof n=="object"));return a.map(t)}function De(e,a){const t=E(e,a==null?void 0:a.in);return t.setHours(0,0,0,0),t}function Ee(e,a,t){const[n,r]=Ne(t==null?void 0:t.in,e,a),i=De(n),l=De(r),c=+i-Se(i),o=+l-Se(l);return Math.round((c-o)/ct)}function mt(e,a){const t=We(e,a),n=H((a==null?void 0:a.in)||e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),le(n)}function ht(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function ft(e){return!(!ht(e)&&typeof e!="number"||isNaN(+E(e)))}function gt(e,a,t){const[n,r]=Ne(t==null?void 0:t.in,e,a),i=Me(n,r),l=Math.abs(Ee(n,r));n.setDate(n.getDate()-i*l);const c=Number(Me(n,r)===-i),o=i*(l-c);return o===0?0:o}function Me(e,a){const t=e.getFullYear()-a.getFullYear()||e.getMonth()-a.getMonth()||e.getDate()-a.getDate()||e.getHours()-a.getHours()||e.getMinutes()-a.getMinutes()||e.getSeconds()-a.getSeconds()||e.getMilliseconds()-a.getMilliseconds();return t<0?-1:t>0?1:t}function yt(e,a){const t=E(e,a==null?void 0:a.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}const bt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},vt=(e,a,t)=>{let n;const r=bt[e];return typeof r=="string"?n=r:a===1?n=r.one:n=r.other.replace("{{count}}",a.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+n:n+" ago":n};function de(e){return(a={})=>{const t=a.width?String(a.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}const wt={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},pt={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},xt={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},kt={date:de({formats:wt,defaultWidth:"full"}),time:de({formats:pt,defaultWidth:"full"}),dateTime:de({formats:xt,defaultWidth:"full"})},_t={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ct=(e,a,t,n)=>_t[e];function te(e){return(a,t)=>{const n=t!=null&&t.context?String(t.context):"standalone";let r;if(n==="formatting"&&e.formattingValues){const l=e.defaultFormattingWidth||e.defaultWidth,c=t!=null&&t.width?String(t.width):l;r=e.formattingValues[c]||e.formattingValues[l]}else{const l=e.defaultWidth,c=t!=null&&t.width?String(t.width):e.defaultWidth;r=e.values[c]||e.values[l]}const i=e.argumentCallback?e.argumentCallback(a):a;return r[i]}}const St={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Dt={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Mt={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Pt={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Ot={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Vt={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Tt=(e,a)=>{const t=Number(e),n=t%100;if(n>20||n<10)switch(n%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},Bt={ordinalNumber:Tt,era:te({values:St,defaultWidth:"wide"}),quarter:te({values:Dt,defaultWidth:"wide",argumentCallback:e=>e-1}),month:te({values:Mt,defaultWidth:"wide"}),day:te({values:Pt,defaultWidth:"wide"}),dayPeriod:te({values:Ot,defaultWidth:"wide",formattingValues:Vt,defaultFormattingWidth:"wide"})};function ae(e){return(a,t={})=>{const n=t.width,r=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],i=a.match(r);if(!i)return null;const l=i[0],c=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],o=Array.isArray(c)?$t(c,g=>g.test(l)):qt(c,g=>g.test(l));let u;u=e.valueCallback?e.valueCallback(o):o,u=t.valueCallback?t.valueCallback(u):u;const S=a.slice(l.length);return{value:u,rest:S}}}function qt(e,a){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&a(e[t]))return t}function $t(e,a){for(let t=0;t<e.length;t++)if(a(e[t]))return t}function Wt(e){return(a,t={})=>{const n=a.match(e.matchPattern);if(!n)return null;const r=n[0],i=a.match(e.parsePattern);if(!i)return null;let l=e.valueCallback?e.valueCallback(i[0]):i[0];l=t.valueCallback?t.valueCallback(l):l;const c=a.slice(r.length);return{value:l,rest:c}}}const Nt=/^(\d+)(th|st|nd|rd)?/i,Et=/\d+/i,Ft={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Yt={any:[/^b/i,/^(a|c)/i]},Lt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},It={any:[/1/i,/2/i,/3/i,/4/i]},jt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Ht={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Ut={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},At={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Gt={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Rt={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Qt={ordinalNumber:Wt({matchPattern:Nt,parsePattern:Et,valueCallback:e=>parseInt(e,10)}),era:ae({matchPatterns:Ft,defaultMatchWidth:"wide",parsePatterns:Yt,defaultParseWidth:"any"}),quarter:ae({matchPatterns:Lt,defaultMatchWidth:"wide",parsePatterns:It,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ae({matchPatterns:jt,defaultMatchWidth:"wide",parsePatterns:Ht,defaultParseWidth:"any"}),day:ae({matchPatterns:Ut,defaultMatchWidth:"wide",parsePatterns:At,defaultParseWidth:"any"}),dayPeriod:ae({matchPatterns:Gt,defaultMatchWidth:"any",parsePatterns:Rt,defaultParseWidth:"any"})},Xt={code:"en-US",formatDistance:vt,formatLong:kt,formatRelative:Ct,localize:Bt,match:Qt,options:{weekStartsOn:0,firstWeekContainsDate:1}};function zt(e,a){const t=E(e,a==null?void 0:a.in);return Ee(t,yt(t))+1}function Jt(e,a){const t=E(e,a==null?void 0:a.in),n=+le(t)-+mt(t);return Math.round(n/$e)+1}function Fe(e,a){var S,g,P,h,k,D,v,y;const t=E(e,a==null?void 0:a.in),n=t.getFullYear(),r=ce(),i=(y=(v=(h=(P=a==null?void 0:a.firstWeekContainsDate)!=null?P:(g=(S=a==null?void 0:a.locale)==null?void 0:S.options)==null?void 0:g.firstWeekContainsDate)!=null?h:r.firstWeekContainsDate)!=null?v:(D=(k=r.locale)==null?void 0:k.options)==null?void 0:D.firstWeekContainsDate)!=null?y:1,l=H((a==null?void 0:a.in)||e,0);l.setFullYear(n+1,0,i),l.setHours(0,0,0,0);const c=se(l,a),o=H((a==null?void 0:a.in)||e,0);o.setFullYear(n,0,i),o.setHours(0,0,0,0);const u=se(o,a);return+t>=+c?n+1:+t>=+u?n:n-1}function Kt(e,a){var c,o,u,S,g,P,h,k;const t=ce(),n=(k=(h=(S=(u=a==null?void 0:a.firstWeekContainsDate)!=null?u:(o=(c=a==null?void 0:a.locale)==null?void 0:c.options)==null?void 0:o.firstWeekContainsDate)!=null?S:t.firstWeekContainsDate)!=null?h:(P=(g=t.locale)==null?void 0:g.options)==null?void 0:P.firstWeekContainsDate)!=null?k:1,r=Fe(e,a),i=H((a==null?void 0:a.in)||e,0);return i.setFullYear(r,0,n),i.setHours(0,0,0,0),se(i,a)}function Zt(e,a){const t=E(e,a==null?void 0:a.in),n=+se(t,a)-+Kt(t,a);return Math.round(n/$e)+1}function x(e,a){const t=e<0?"-":"",n=Math.abs(e).toString().padStart(a,"0");return t+n}const U={y(e,a){const t=e.getFullYear(),n=t>0?t:1-t;return x(a==="yy"?n%100:n,a.length)},M(e,a){const t=e.getMonth();return a==="M"?String(t+1):x(t+1,2)},d(e,a){return x(e.getDate(),a.length)},a(e,a){const t=e.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(e,a){return x(e.getHours()%12||12,a.length)},H(e,a){return x(e.getHours(),a.length)},m(e,a){return x(e.getMinutes(),a.length)},s(e,a){return x(e.getSeconds(),a.length)},S(e,a){const t=a.length,n=e.getMilliseconds(),r=Math.trunc(n*Math.pow(10,t-3));return x(r,a.length)}},Q={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Pe={G:function(e,a,t){const n=e.getFullYear()>0?1:0;switch(a){case"G":case"GG":case"GGG":return t.era(n,{width:"abbreviated"});case"GGGGG":return t.era(n,{width:"narrow"});case"GGGG":default:return t.era(n,{width:"wide"})}},y:function(e,a,t){if(a==="yo"){const n=e.getFullYear(),r=n>0?n:1-n;return t.ordinalNumber(r,{unit:"year"})}return U.y(e,a)},Y:function(e,a,t,n){const r=Fe(e,n),i=r>0?r:1-r;if(a==="YY"){const l=i%100;return x(l,2)}return a==="Yo"?t.ordinalNumber(i,{unit:"year"}):x(i,a.length)},R:function(e,a){const t=We(e);return x(t,a.length)},u:function(e,a){const t=e.getFullYear();return x(t,a.length)},Q:function(e,a,t){const n=Math.ceil((e.getMonth()+1)/3);switch(a){case"Q":return String(n);case"QQ":return x(n,2);case"Qo":return t.ordinalNumber(n,{unit:"quarter"});case"QQQ":return t.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,a,t){const n=Math.ceil((e.getMonth()+1)/3);switch(a){case"q":return String(n);case"qq":return x(n,2);case"qo":return t.ordinalNumber(n,{unit:"quarter"});case"qqq":return t.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,a,t){const n=e.getMonth();switch(a){case"M":case"MM":return U.M(e,a);case"Mo":return t.ordinalNumber(n+1,{unit:"month"});case"MMM":return t.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(n,{width:"wide",context:"formatting"})}},L:function(e,a,t){const n=e.getMonth();switch(a){case"L":return String(n+1);case"LL":return x(n+1,2);case"Lo":return t.ordinalNumber(n+1,{unit:"month"});case"LLL":return t.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(n,{width:"wide",context:"standalone"})}},w:function(e,a,t,n){const r=Zt(e,n);return a==="wo"?t.ordinalNumber(r,{unit:"week"}):x(r,a.length)},I:function(e,a,t){const n=Jt(e);return a==="Io"?t.ordinalNumber(n,{unit:"week"}):x(n,a.length)},d:function(e,a,t){return a==="do"?t.ordinalNumber(e.getDate(),{unit:"date"}):U.d(e,a)},D:function(e,a,t){const n=zt(e);return a==="Do"?t.ordinalNumber(n,{unit:"dayOfYear"}):x(n,a.length)},E:function(e,a,t){const n=e.getDay();switch(a){case"E":case"EE":case"EEE":return t.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(n,{width:"short",context:"formatting"});case"EEEE":default:return t.day(n,{width:"wide",context:"formatting"})}},e:function(e,a,t,n){const r=e.getDay(),i=(r-n.weekStartsOn+8)%7||7;switch(a){case"e":return String(i);case"ee":return x(i,2);case"eo":return t.ordinalNumber(i,{unit:"day"});case"eee":return t.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(r,{width:"short",context:"formatting"});case"eeee":default:return t.day(r,{width:"wide",context:"formatting"})}},c:function(e,a,t,n){const r=e.getDay(),i=(r-n.weekStartsOn+8)%7||7;switch(a){case"c":return String(i);case"cc":return x(i,a.length);case"co":return t.ordinalNumber(i,{unit:"day"});case"ccc":return t.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(r,{width:"narrow",context:"standalone"});case"cccccc":return t.day(r,{width:"short",context:"standalone"});case"cccc":default:return t.day(r,{width:"wide",context:"standalone"})}},i:function(e,a,t){const n=e.getDay(),r=n===0?7:n;switch(a){case"i":return String(r);case"ii":return x(r,a.length);case"io":return t.ordinalNumber(r,{unit:"day"});case"iii":return t.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(n,{width:"short",context:"formatting"});case"iiii":default:return t.day(n,{width:"wide",context:"formatting"})}},a:function(e,a,t){const r=e.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,a,t){const n=e.getHours();let r;switch(n===12?r=Q.noon:n===0?r=Q.midnight:r=n/12>=1?"pm":"am",a){case"b":case"bb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,a,t){const n=e.getHours();let r;switch(n>=17?r=Q.evening:n>=12?r=Q.afternoon:n>=4?r=Q.morning:r=Q.night,a){case"B":case"BB":case"BBB":return t.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,a,t){if(a==="ho"){let n=e.getHours()%12;return n===0&&(n=12),t.ordinalNumber(n,{unit:"hour"})}return U.h(e,a)},H:function(e,a,t){return a==="Ho"?t.ordinalNumber(e.getHours(),{unit:"hour"}):U.H(e,a)},K:function(e,a,t){const n=e.getHours()%12;return a==="Ko"?t.ordinalNumber(n,{unit:"hour"}):x(n,a.length)},k:function(e,a,t){let n=e.getHours();return n===0&&(n=24),a==="ko"?t.ordinalNumber(n,{unit:"hour"}):x(n,a.length)},m:function(e,a,t){return a==="mo"?t.ordinalNumber(e.getMinutes(),{unit:"minute"}):U.m(e,a)},s:function(e,a,t){return a==="so"?t.ordinalNumber(e.getSeconds(),{unit:"second"}):U.s(e,a)},S:function(e,a){return U.S(e,a)},X:function(e,a,t){const n=e.getTimezoneOffset();if(n===0)return"Z";switch(a){case"X":return Ve(n);case"XXXX":case"XX":return A(n);case"XXXXX":case"XXX":default:return A(n,":")}},x:function(e,a,t){const n=e.getTimezoneOffset();switch(a){case"x":return Ve(n);case"xxxx":case"xx":return A(n);case"xxxxx":case"xxx":default:return A(n,":")}},O:function(e,a,t){const n=e.getTimezoneOffset();switch(a){case"O":case"OO":case"OOO":return"GMT"+Oe(n,":");case"OOOO":default:return"GMT"+A(n,":")}},z:function(e,a,t){const n=e.getTimezoneOffset();switch(a){case"z":case"zz":case"zzz":return"GMT"+Oe(n,":");case"zzzz":default:return"GMT"+A(n,":")}},t:function(e,a,t){const n=Math.trunc(+e/1e3);return x(n,a.length)},T:function(e,a,t){return x(+e,a.length)}};function Oe(e,a=""){const t=e>0?"-":"+",n=Math.abs(e),r=Math.trunc(n/60),i=n%60;return i===0?t+String(r):t+String(r)+a+x(i,2)}function Ve(e,a){return e%60===0?(e>0?"-":"+")+x(Math.abs(e)/60,2):A(e,a)}function A(e,a=""){const t=e>0?"-":"+",n=Math.abs(e),r=x(Math.trunc(n/60),2),i=x(n%60,2);return t+r+a+i}const Te=(e,a)=>{switch(e){case"P":return a.date({width:"short"});case"PP":return a.date({width:"medium"});case"PPP":return a.date({width:"long"});case"PPPP":default:return a.date({width:"full"})}},Ye=(e,a)=>{switch(e){case"p":return a.time({width:"short"});case"pp":return a.time({width:"medium"});case"ppp":return a.time({width:"long"});case"pppp":default:return a.time({width:"full"})}},ea=(e,a)=>{const t=e.match(/(P+)(p+)?/)||[],n=t[1],r=t[2];if(!r)return Te(e,a);let i;switch(n){case"P":i=a.dateTime({width:"short"});break;case"PP":i=a.dateTime({width:"medium"});break;case"PPP":i=a.dateTime({width:"long"});break;case"PPPP":default:i=a.dateTime({width:"full"});break}return i.replace("{{date}}",Te(n,a)).replace("{{time}}",Ye(r,a))},ta={p:Ye,P:ea},aa=/^D+$/,na=/^Y+$/,ra=["D","DD","YY","YYYY"];function oa(e){return aa.test(e)}function sa(e){return na.test(e)}function ia(e,a,t){const n=la(e,a,t);if(console.warn(n),ra.includes(e))throw new RangeError(n)}function la(e,a,t){const n=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${a}\`) for formatting ${n} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const ca=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ua=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,da=/^'([^]*?)'?$/,ma=/''/g,ha=/[a-zA-Z]/;function ne(e,a,t){var S,g,P,h,k,D,v,y,f,s,m,p,$,O,F,R,K,ge;const n=ce(),r=(g=(S=t==null?void 0:t.locale)!=null?S:n.locale)!=null?g:Xt,i=(s=(f=(D=(k=t==null?void 0:t.firstWeekContainsDate)!=null?k:(h=(P=t==null?void 0:t.locale)==null?void 0:P.options)==null?void 0:h.firstWeekContainsDate)!=null?D:n.firstWeekContainsDate)!=null?f:(y=(v=n.locale)==null?void 0:v.options)==null?void 0:y.firstWeekContainsDate)!=null?s:1,l=(ge=(K=(O=($=t==null?void 0:t.weekStartsOn)!=null?$:(p=(m=t==null?void 0:t.locale)==null?void 0:m.options)==null?void 0:p.weekStartsOn)!=null?O:n.weekStartsOn)!=null?K:(R=(F=n.locale)==null?void 0:F.options)==null?void 0:R.weekStartsOn)!=null?ge:0,c=E(e,t==null?void 0:t.in);if(!ft(c))throw new RangeError("Invalid time value");let o=a.match(ua).map(W=>{const B=W[0];if(B==="p"||B==="P"){const ue=ta[B];return ue(W,r.formatLong)}return W}).join("").match(ca).map(W=>{if(W==="''")return{isToken:!1,value:"'"};const B=W[0];if(B==="'")return{isToken:!1,value:fa(W)};if(Pe[B])return{isToken:!0,value:W};if(B.match(ha))throw new RangeError("Format string contains an unescaped latin alphabet character `"+B+"`");return{isToken:!1,value:W}});r.localize.preprocessor&&(o=r.localize.preprocessor(c,o));const u={firstWeekContainsDate:i,weekStartsOn:l,locale:r};return o.map(W=>{if(!W.isToken)return W.value;const B=W.value;(!(t!=null&&t.useAdditionalWeekYearTokens)&&sa(B)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&oa(B))&&ia(B,a,String(e));const ue=Pe[B[0]];return ue(c,B,r.localize,u)}).join("")}function fa(e){const a=e.match(da);return a?a[1].replace(ma,"'"):e}const ga={},ya={width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function ba(e,a){return d(),b("svg",ya,a[0]||(a[0]=[w("circle",{cx:"8",cy:"8",r:"4.5",fill:"transparent",stroke:"currentColor","stroke-width":"3"},null,-1)]))}var va=ie(ga,[["render",ba]]);const wa={class:"w-full"},pa=["onClick"],xa={class:"flex items-center"},ka={key:0,class:"overflow-hidden text-ellipsis whitespace-nowrap text-base leading-5"},_a={key:1,class:"text-base leading-5 text-gray-500"},Ca={class:"mt-1 rounded-lg bg-white py-1 text-base shadow-2xl"},Sa={class:"relative px-1.5 pt-0.5"},Da={key:0,class:"px-2.5 py-1.5 text-sm font-medium text-gray-500"},Ma={key:0,class:"mt-1.5 rounded-md px-2.5 py-1.5 text-base text-gray-600"},Pa={key:0,class:"border-t p-1.5 pb-0.5"},Oa={__name:"Autocomplete",props:{modelValue:{type:String,default:""},options:{type:Array,default:()=>[]},size:{type:String,default:"md"},variant:{type:String,default:"subtle"},placeholder:{type:String,default:""},disabled:{type:Boolean,default:!1},filterable:{type:Boolean,default:!0}},emits:["update:modelValue","update:query","change"],setup(e,{expose:a,emit:t}){const n=e,r=t,i=q(""),l=q(!1),c=q(null),o=Be(),u=Re(),S=L(()=>"value"in o),g=L({get(){return S.value?o.value:n.modelValue},set(f){i.value="",f&&(l.value=!1),r(S.value?"change":"update:modelValue",f)}});function P(){l.value=!1}const h=L(()=>{var s;return!n.options||n.options.length==0?[]:((s=n.options[0])!=null&&s.group?n.options:[{group:"",items:n.options}]).map((m,p)=>({key:p,group:m.group,hideLabel:m.hideLabel||!1,items:n.filterable?k(m.items):m.items})).filter(m=>m.items.length>0)});function k(f){return i.value?f.filter(s=>[s.label,s.value].some(p=>(p||"").toString().toLowerCase().includes(i.value.toLowerCase()))):f}function D(f){if(typeof f=="string"){let m=h.value.flatMap(p=>p.items).find(p=>p.value===f);return(m==null?void 0:m.label)||f}return f==null?void 0:f.label}X(i,f=>{r("update:query",f)}),X(l,f=>{f&&Qe(()=>{c.value.el.focus()})});const v=L(()=>n.disabled?"text-gray-600":"text-gray-800"),y=L(()=>{let f={sm:"text-base rounded h-7",md:"text-base rounded h-8",lg:"text-lg rounded-md h-10",xl:"text-xl rounded-md h-10"}[n.size],s={sm:"py-1.5 px-2",md:"py-1.5 px-2.5",lg:"py-1.5 px-3",xl:"py-1.5 px-3"}[n.size],m=n.disabled?"disabled":n.variant,p={subtle:"border border-gray-100 bg-gray-100 placeholder-gray-500 hover:border-gray-200 hover:bg-gray-200 focus:bg-white focus:border-gray-500 focus:shadow-sm focus:ring-0 focus-visible:ring-2 focus-visible:ring-gray-400",outline:"border border-gray-300 bg-white placeholder-gray-500 hover:border-gray-400 hover:shadow-sm focus:bg-white focus:border-gray-500 focus:shadow-sm focus:ring-0 focus-visible:ring-2 focus-visible:ring-gray-400",disabled:["border bg-gray-50 placeholder-gray-400",n.variant==="outline"?"border-gray-300":"border-transparent"]}[m];return[f,s,p,v.value,"transition-colors w-full"]});return a({query:i}),(f,s)=>(d(),N(M(Ze),{modelValue:g.value,"onUpdate:modelValue":s[3]||(s[3]=m=>g.value=m),nullable:""},{default:V(({open:m})=>[_(M(Xe),{class:"w-full",show:l.value,"onUpdate:show":s[2]||(s[2]=p=>l.value=p)},{target:V(({open:p,togglePopover:$})=>[I(f.$slots,"target",re(oe({open:p,togglePopover:$,isOpen:l.value,selectedValue:g.value,displayValue:D})),()=>[w("div",wa,[w("button",{class:Y(["flex w-full items-center justify-between focus:outline-none",y.value]),onClick:()=>$()},[w("div",xa,[I(f.$slots,"prefix"),g.value?(d(),b("span",ka,T(D(g.value)),1)):(d(),b("span",_a,T(e.placeholder||""),1))]),_(M(we),{name:"chevron-down",class:"h-4 w-4 text-gray-600","aria-hidden":"true"})],10,pa)])])]),body:V(({isOpen:p})=>{var $;return[pe(w("div",null,[w("div",Ca,[w("div",Sa,[_(M(ze),{ref_key:"search",ref:c,class:"form-input w-full",type:"text",onChange:s[0]||(s[0]=O=>{i.value=O.target.value}),value:i.value,autocomplete:"off",placeholder:"Search"},null,8,["value"]),w("button",{class:"absolute right-1.5 inline-flex h-7 w-7 items-center justify-center",onClick:s[1]||(s[1]=O=>g.value=null)},[_(M(we),{name:"x",class:"w-4"})])]),_(M(Je),{class:"my-1 max-h-[12rem] overflow-y-auto px-1.5",static:""},{default:V(()=>[(d(!0),b(G,null,J(h.value,O=>pe((d(),b("div",{class:"mt-1.5",key:O.key},[O.group&&!O.hideLabel?(d(),b("div",Da,T(O.group),1)):C("",!0),(d(!0),b(G,null,J(O.items,F=>(d(),N(M(Ke),{as:"template",key:F.value,value:F},{default:V(({active:R,selected:K})=>[w("li",{class:Y(["flex items-center rounded px-2.5 py-1.5 text-base",{"bg-gray-100":R}])},[I(f.$slots,"item-prefix",xe({ref_for:!0},{active:R,selected:K,option:F})),I(f.$slots,"item-label",xe({ref_for:!0},{active:R,selected:K,option:F}),()=>[he(T(F.label),1)])],2)]),_:2},1032,["value"]))),128))])),[[ke,O.items.length>0]])),128)),h.value.length==0?(d(),b("li",Ma," No results found ")):C("",!0)]),_:3}),M(u).footer?(d(),b("div",Pa,[I(f.$slots,"footer",re(oe({value:($=c.value)==null?void 0:$.el._value,close:P})))])):C("",!0)])],512),[[ke,p]])]}),_:3},8,["show"])]),_:3},8,["modelValue"]))}},Va={key:0,class:"space-y-1.5"},Ta={key:0},Le={__name:"Link",props:{doctype:{type:String,required:!0},modelValue:{type:String,default:""},hideMe:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(e,{emit:a}){const t=e,n=a,r=Be(),i=L(()=>"value"in r),l=L({get:()=>i.value?r.value:t.modelValue,set:h=>(h==null?void 0:h.value)&&n(i.value?"change":"update:modelValue",h==null?void 0:h.value)}),c=q(null),o=q("");_e(()=>{var h;return(h=c.value)==null?void 0:h.query},h=>{h=h||"",o.value!==h&&(o.value=h,S(h))},{debounce:300,immediate:!0}),_e(()=>t.doctype,()=>S(""),{debounce:300,immediate:!0});const u=z({url:"servicems.api.api.search_link_data",cache:[t.doctype,o.value,t.hideMe],method:"POST",params:{txt:o.value,doctype:t.doctype},transform:h=>{let k=h.map(D=>({label:D.value,value:D.value}));return!t.hideMe&&t.doctype=="User"&&k.unshift({label:"@me",value:"@me"}),k}});function S(h){var k,D,v;((k=u.data)==null?void 0:k.length)&&h===((D=u.params)==null?void 0:D.txt)&&t.doctype===((v=u.params)==null?void 0:v.doctype)||(u.update({params:{txt:h,doctype:t.doctype}}),u.reload())}function g(h){n(i.value?"change":"update:modelValue",""),h()}const P=L(()=>[{sm:"text-xs",md:"text-base"}[r.size||"sm"],"text-gray-600"]);return(h,k)=>{const D=j("FeatherIcon"),v=j("Button");return M(r).disabled?C("",!0):(d(),b("div",Va,[M(r).label?(d(),b("label",{key:0,class:Y(["block",P.value])},T(M(r).label),3)):C("",!0),_(Oa,{ref_key:"autocomplete",ref:c,options:M(u).data,modelValue:l.value,"onUpdate:modelValue":k[0]||(k[0]=y=>l.value=y),size:M(r).size||"sm",variant:M(r).variant,placeholder:M(r).placeholder,filterable:!1},{target:V(({open:y,togglePopover:f})=>[I(h.$slots,"target",re(oe({open:y,togglePopover:f})))]),prefix:V(()=>[I(h.$slots,"prefix")]),"item-prefix":V(({active:y,selected:f,option:s})=>[I(h.$slots,"item-prefix",re(oe({active:y,selected:f,option:s})))]),"item-label":V(({active:y,selected:f,option:s})=>[I(h.$slots,"item-label",re(oe({active:y,selected:f,option:s})))]),footer:V(({value:y,close:f})=>[M(r).onCreate?(d(),b("div",Ta,[_(v,{variant:"ghost",class:"w-full !justify-start",label:h.Create,New:"",onClick:s=>M(r).onCreate(y,f)},{prefix:V(()=>[_(D,{name:"plus",class:"h-4"})]),_:2},1032,["label","onClick"])])):C("",!0),w("div",null,[_(v,{variant:"ghost",class:"w-full !justify-start",label:h.Clear,onClick:()=>g(f)},{prefix:V(()=>[_(D,{name:"x",class:"h-4"})]),_:2},1032,["label","onClick"])])]),_:3},8,["options","modelValue","size","variant","placeholder"])]))}}};const Ba={key:0,class:"mb-2 text-base text-gray-900 font-semibold"},qa={key:0,class:"text-red-500 text-xl"},$a={key:3,class:"flex items-center gap-2"},Wa={class:"text-sm text-gray-900 font-bold"},Na={key:0,class:"text-red-500 text-xl"},Ea={__name:"Fields",props:{section:Object,data:Object},setup(e){const a=e;function t(r,i,l){const c=a.data;c[r]&&(c[r][i]=l)}function n(r,i,l){const c=a.data;c[r]&&(c[r][i]=l),c["Service Booking"].is_new_customer&&(c["Service Booking"].customer=""),c["Service Booking"].is_new_vehicle&&(c["Service Booking"].service_vehicle="")}return(r,i)=>{const l=j("FormControl"),c=j("DatePicker");return d(!0),b(G,null,J(e.section.fields,o=>(d(),b("div",{key:o.name},[o.type!="Check"?(d(),b("div",Ba,[he(T(o.label)+" ",1),o.reqd&&!(o.name==="customer"&&e.data["Service Booking"].is_new_customer||o.name==="service_vehicle"&&e.data["Service Booking"].is_new_vehicle)?(d(),b("span",qa," * ")):C("",!0)])):C("",!0),o.read_only&&o.type!=="Check"?(d(),N(l,{key:1,type:"text",placeholder:o.placeholder||o.label,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,disabled:!0,variant:"outline"},null,8,["placeholder","modelValue","onUpdate:modelValue"])):o.type==="Select"?(d(),N(l,{key:2,type:"select",class:Y(["form-control",o.prefix?"prefix":""]),options:o.options,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,placeholder:o.placeholder||o.label,variant:"outline"},et({_:2},[o.prefix?{name:"prefix",fn:V(()=>[_(va,{class:Y(o.prefix)},null,8,["class"])]),key:"0"}:void 0]),1032,["class","options","modelValue","onUpdate:modelValue","placeholder"])):o.type=="Check"?(d(),b("div",$a,[_(l,{class:"form-control",type:"checkbox",modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,onChange:u=>n(e.section.doctype,o.name,u.target.checked),disabled:Boolean(o.read_only),variant:"outline"},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"]),w("label",Wa,[he(T(o.label)+" ",1),o.reqd?(d(),b("span",Na,"*")):C("",!0)])])):o.type==="Link"?(d(),N(Le,{key:4,class:"form-control",value:e.data[e.section.doctype][o.name],doctype:o.options,onChange:u=>e.data[e.section.doctype][o.name]=u,placeholder:o.placeholder||o.label,onCreate:o.create,disabled:o.name==="customer"&&e.data["Service Booking"].is_new_customer||o.name==="service_vehicle"&&e.data["Service Booking"].is_new_vehicle},null,8,["value","doctype","onChange","placeholder","onCreate","disabled"])):o.type==="Date"?(d(),N(c,{key:5,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,placeholder:o.placeholder||o.label},null,8,["modelValue","onUpdate:modelValue","placeholder"])):o.type==="Time"?(d(),N(l,{key:6,type:"time",placeholder:o.placeholder||o.label,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u},null,8,["placeholder","modelValue","onUpdate:modelValue"])):["Small Text","Text","Long Text"].includes(o.type)?(d(),N(l,{key:7,type:"textarea",placeholder:o.placeholder||o.label,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,value:e.data[e.section.doctype][o.name],onInput:u=>t(e.section.doctype,o.name,u.target.value)},null,8,["placeholder","modelValue","onUpdate:modelValue","value","onInput"])):["Int"].includes(o.type)?(d(),N(l,{key:8,type:"number",placeholder:o.placeholder||o.label,modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,onInput:u=>t(e.section.doctype,o.name,u.target.value)},null,8,["placeholder","modelValue","onUpdate:modelValue","onInput"])):(d(),N(l,{key:9,type:"text",placeholder:o.placeholder||o.label,value:e.data[e.section.doctype][o.name],modelValue:e.data[e.section.doctype][o.name],"onUpdate:modelValue":u=>e.data[e.section.doctype][o.name]=u,onInput:u=>t(e.section.doctype,o.name,u.target.value),disabled:Boolean(o.read_only)},null,8,["placeholder","value","modelValue","onUpdate:modelValue","onInput","disabled"]))]))),128)}}};var me=ie(Ea,[["__scopeId","data-v-f7d1b46a"]]);const Fa={class:"flex flex-col gap-6"},Ya={key:0,class:"flex h-7 mb-3 max-w-fit cursor-pointer items-center gap-2 text-xl font-bold text-cyan-700 font-mono leading-5"},La={key:2,class:"flex h-7 mb-3 max-w-fit cursor-pointer items-center gap-2 text-xl font-bold text-cyan-700 font-mono leading-5"},Ia={key:4,class:"flex h-7 mb-3 max-w-fit cursor-pointer items-center gap-2 text-xl font-bold text-cyan-700 font-mono leading-5"},ja={__name:"FieldMap",props:{sections:Array,data:Object},setup(e){return(a,t)=>(d(),b("div",Fa,[(d(!0),b(G,null,J(e.sections,n=>(d(),b("div",{key:n.label,class:Y(["first:border-t-0 first:pt-0",n.hideBorder?"":"border-t pt-4"])},[!n.hideLabel&&n.doctype=="Service Booking"?(d(),b("div",Ya,T(n.label),1)):C("",!0),n.doctype=="Service Booking"?(d(),b("div",{key:1,class:Y(["grid gap-6",n.columns?"grid-cols-"+n.columns:"grid-cols-4 sm:grid-cols-4"])},[_(me,{section:n,data:e.data},null,8,["section","data"])],2)):C("",!0),!n.hideLabel&&n.doctype=="Customer"&&e.data["Service Booking"].is_new_customer?(d(),b("div",La,T(n.label),1)):C("",!0),n.doctype=="Customer"&&e.data["Service Booking"].is_new_customer?(d(),b("div",{key:3,class:Y(["grid gap-4",n.columns?"grid-cols-"+n.columns:"grid-cols-3 sm:grid-cols-3"])},[_(me,{section:n,data:e.data},null,8,["section","data"])],2)):C("",!0),!n.hideLabel&&n.doctype=="Service Vehicle"&&e.data["Service Booking"].is_new_vehicle?(d(),b("div",Ia,T(n.label),1)):C("",!0),n.doctype=="Service Vehicle"&&e.data["Service Booking"].is_new_vehicle?(d(),b("div",{key:5,class:Y(["grid gap-4",n.columns?"grid-cols-"+n.columns:"grid-cols-3 sm:grid-cols-3"])},[_(me,{section:n,data:e.data},null,8,["section","data"])],2)):C("",!0)],2))),128))]))}};var Ha=ie(ja,[["__scopeId","data-v-13c53147"]]);const Ua={class:"bg-white px-4 pb-6 pt-5 sm:px-6"},Aa={class:"mb-5 flex gap-36 items-center justify-around"},Ga={class:"flex justify-end w-12"},Ra={key:0,class:"text-center py-4 text-gray-500"},Qa={class:"px-4 pb-7 pt-4 lg:px-7"},Xa={class:"flex flex-row-reverse gap-2 w-full"},za=["loading"],Ja={__name:"CreateBooking",props:{showDialog:Boolean,bay:String,booking_date:Date},emits:["closeDialog"],setup(e,{emit:a}){const t=q(!1),n=q(""),r=q(null),i=e,l=fe(),c=a,o=q(i.showDialog);function u(s){o.value=s,c("update:showDialog",s)}X(()=>i.showDialog,s=>{o.value=s}),X(()=>i.bay,s=>{y["Service Booking"].bay=s}),X(()=>i.booking_date,s=>{y["Service Booking"].booking_date=s});const S=()=>{o.value=!1,c("closeDialog")};function g(s,m){["success","info","warning","error"].includes(m)&&l[m]?l[m](s):l(s)}const P=z({url:"frappe.client.insert",method:"POST",makeParams(){return{doc:Z({doctype:"Service Booking"},y["Service Booking"])}},validate(s){if(r.value=null,!s.doc.customer)return r.value="Customer is required",g("Customer is required","error"),r.value;if(!s.doc.service_vehicle&&!s.doc.is_new_vehicle)return r.value="Service Vehicle is required",g("Service Vehicle is required","error"),r.value;if(!s.doc.booking_date)return r.value="Booking Date is required",g("Booking Date is required","error"),r.value;if(!s.doc.booking_time)return r.value="Booking Time is required",g("Booking Time is required","error"),r.value;if(!s.doc.bay)return r.value="Bay is required",g("Bay is required","error"),r.value;t.value=!0},onSuccess:s=>{t.value=!1,g("Booking created successfully","success"),S(),v(),c("refreshData")},onError:s=>{if(t.value=!1,!s.messages){r.value=s.message;return}r.value=s.messages.join(`
`)}}),h=z({url:"frappe.client.insert",method:"POST",makeParams(){return{doc:Z({doctype:"Customer"},y.Customer)}},validate(s){if(r.value=null,!s.doc.customer_name)return r.value="Customer Name is required",g("Customer Name is required","error"),r.value;if(!s.doc.customer_type)return r.value="Customer Type is required",g("Customer Type is required","error"),r.value;if(!s.doc.customer_group)return r.value="Customer Group is required",g("Customer Group is required","error"),r.value;if(!s.doc.territory)return r.value="Territory is required",g("Territory is required","error"),r.value},onSuccess:s=>{y["Service Booking"].customer=s.name,y["Service Vehicle"].customer=s.name,g("Customer created successfully","success")},onError:s=>{if(!s.messages){r.value=s.message,g(r.value,"error");return}r.value=s.messages.join(`
`),g(r.value,"error")}}),k=z({url:"frappe.client.insert",method:"POST",makeParams(){return{doc:Z({doctype:"Service Vehicle"},y["Service Vehicle"])}},validate(s){if(r.value=null,!s.doc.registration_number)return r.value="Registration Number is required",g("Registration Number is required","error"),r.value;if(!s.doc.customer)return r.value="Customer is required",g("Customer is required","error"),r.value;if(!s.doc.vehicle_model)return r.value="Vehicle Model is required",g("Vehicle Model is required","error"),r.value},onSuccess:s=>{y["Service Booking"].service_vehicle=s.name,g("Vehicle created successfully","success")},onError:s=>{if(!s.messages){r.value=s.message,g(r.value,"error");return}r.value=s.messages.join(`
`),g(r.value,"error")}});function D(){return ee(this,null,function*(){t.value=!0,r.value=null;try{y["Service Booking"].is_new_customer&&(n.value="Creating customer...",yield h.submit()),y["Service Booking"].is_new_vehicle&&(n.value="Creating vehicle...",yield k.submit()),n.value="Creating booking...",yield P.submit()}catch(s){r.value="Error while creating booking documents",g(r.value,"error")}finally{t.value=!1}})}function v(){y["Service Booking"]={is_new_customer:!1,customer:"",is_new_vehicle:!1,service_vehicle:"",booking_date:"",booking_time:"",bay:"",service_description:"",disabled:!1},y.Customer={customer_name:"",customer_type:"",customer_group:"",territory:"",tax_id:"",mobile_no:"",disabled:!1},y["Service Vehicle"]={registration_number:"",customer:"",vehicle_model:"",make:"",type:"",engine_number:"",chassis_number:"",disabled:!1}}const y=qe({"Service Booking":{is_new_customer:!1,customer:"",is_new_vehicle:!1,service_vehicle:"",booking_date:"",booking_time:"",bay:"",service_description:"",disabled:!1},Customer:{customer_name:"",customer_type:"",customer_group:"",territory:"",tax_id:"",mobile_no:"",disabled:!1},"Service Vehicle":{registration_number:"",customer:"",vehicle_model:"",make:"",type:"",engine_number:"",chassis_number:"",disabled:!1}}),f=[{label:"Booking Details",doctype:"Service Booking",fields:[{name:"is_new_customer",label:"Is New Customer",type:"Check",placeholder:"Is New Customer",reqd:!1},{name:"customer",label:"Customer",type:"Link",placeholder:"Customer",options:"Customer",reqd:!0},{name:"is_new_vehicle",label:"Is New Vehicle",type:"Check",placeholder:"Is New Vehicle",reqd:!1},{name:"service_vehicle",label:"Service Vehicle",type:"Link",placeholder:"Service Vehicle",options:"Service Vehicle",reqd:!0},{name:"booking_date",label:"Booking Date",type:"Date",placeholder:"Booking Date",reqd:!0},{name:"booking_time",label:"Booking Time",type:"Time",placeholder:"Booking Time",reqd:!0},{name:"bay",label:"Bay",type:"Link",placeholder:"Bay",options:"Bay",reqd:!0},{name:"service_description",label:"Service Description",type:"Small Text",placeholder:"Service Description",reqd:!1}],hideLabel:!1},{label:"Customer Details",doctype:"Customer",fields:[{name:"customer_name",label:"Customer Name",type:"Data",placeholder:"Customer Name",reqd:!0},{name:"customer_type",label:"Customer Type",type:"Select",placeholder:"Customer Type",options:["Company","Individual","Partnership","Corporate","Government","Non-profit"],reqd:!0},{name:"customer_group",label:"Customer Group",type:"Link",placeholder:"Customer Group",options:"Customer Group",reqd:!0},{name:"territory",label:"Territory",type:"Link",placeholder:"Territory",options:"Territory",reqd:!0},{name:"tax_id",label:"TIN",type:"Data",placeholder:"TIN",reqd:!1},{name:"mobile_no",label:"Mobile No",type:"Data",placeholder:"Mobile No",reqd:!1}],hideLabel:!1,hideBorder:!1},{label:"Vehicle Details",doctype:"Service Vehicle",fields:[{name:"registration_number",label:"Registration Number",type:"Data",placeholder:"Registration Number",reqd:!0},{name:"customer",label:"Customer",type:"Link",placeholder:"Customer",options:"Customer",reqd:!0},{name:"vehicle_model",label:"Vehicle Model",type:"Data",placeholder:"Vehicle Model",options:"Vehicle Model",reqd:!0},{name:"make",label:"Make",type:"Data",placeholder:"Make",reqd:!1},{name:"type",label:"Type",type:"Data",placeholder:"Type",reqd:!1},{name:"engine_number",label:"Engine Number",type:"Data",placeholder:"Engine Number",reqd:!1},{name:"chassis_number",label:"Chassis Number",type:"Data",placeholder:"Chassis Number",reqd:!1}],hideLabel:!1,hideBorder:!1}];return(s,m)=>{const p=j("FeatherIcon"),$=j("Button"),O=j("Dialog");return d(),N(O,{"model-value":o.value,"onUpdate:modelValue":u,options:{size:"5xl"}},{body:V(()=>[w("div",Ua,[w("div",Aa,[m[2]||(m[2]=w("div",{class:"ml-60 w-112"},[w("h3",{class:"text-2xl text-center font-semibold leading-6 text-gray-900"}," Create Booking ")],-1)),w("div",Ga,[_($,{variant:"ghost",class:"w-7",onClick:m[0]||(m[0]=F=>e.showDialog=!1)},{default:V(()=>[_(p,{name:"x",class:"h-7 w-7"})]),_:1})])]),t.value?(d(),b("div",Ra,T(n.value),1)):C("",!0),w("div",null,[f?(d(),N(Ha,{key:0,sections:f,data:y},null,8,["data"])):C("",!0),r.value?(d(),N(M(ot),{key:1,class:"mt-4 text-lg font-bold",message:r.value},null,8,["message"])):C("",!0)])]),w("div",Qa,[w("div",Xa,[w("button",{class:"w-24 h-10 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-cyan-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",loading:t.value,onClick:m[1]||(m[1]=F=>D())}," Create ",8,za)])])]),_:1},8,["model-value"])}}},Ka={class:"flex justify-around w-full mt-4 h-15 border-2 rounded-2xl"},Za={class:"flex items-center mx-24 gap-24"},en={class:"flex-initial w-64 border-2 border-cyan-500/50 shadow-cyan-900/50 rounded-2xl"},tn={class:"flex-initial w-64 border-2 border-cyan-500/50 shadow-cyan-900/50 rounded-2xl"},an={class:"flex-initial w-64 border-2 border-cyan-500/50 shadow-cyan-900/50 rounded-2xl"},nn={__name:"DateFilter",props:{filters:Object},setup(e){return(a,t)=>{const n=j("DatePicker");return d(),b("div",Ka,[w("div",Za,[w("div",en,[_(n,{modelValue:e.filters.from_date,"onUpdate:modelValue":t[0]||(t[0]=r=>e.filters.from_date=r),variant:"outline",placeholder:"Select From Date"},null,8,["modelValue"])]),w("div",tn,[_(n,{modelValue:e.filters.to_date,"onUpdate:modelValue":t[1]||(t[1]=r=>e.filters.to_date=r),variant:"outline",placeholder:"Select To Date"},null,8,["modelValue"])]),w("div",an,[_(Le,{class:"form-control",value:e.filters.workshop,doctype:"Service Workshop",placeholder:"Service Workshop",onChange:t[2]||(t[2]=r=>e.filters.workshop=r)},null,8,["value"])])])])}}};const rn={class:"mb-6"},on={class:"scroll h-[600px] overflow-y-auto"},sn={key:0,class:"text-center py-4 text-gray-500"},ln={class:"border border-gray-300 rounded-md p-4 relative"},cn={class:"flex shadow-lg shadow-green-500 font-bold mb-8 text-center h-7 text-2xl"},un={class:"flex items-center justify-between relative"},dn=["onClick"],mn=["id"],hn={class:"flex-1 overflow-hidden text-ellipsis whitespace-nowrap mt-2 text-center"},fn={class:"font-bold text-base"},gn={key:0,class:"rounded-md bg-orange-200 text-sm text-orange-900 ring-1 ring-inset ring-orange-500/10"},yn={class:"translate-x-3/4 mb-6"},bn=["onClick"],vn={class:"flex justify-center mt-auto"},wn=["onClick"],pn=["onClick"],xn={__name:"BookingList",setup(e){const a=new Date,t=q(0),n=q(!0),r=q(!1),i=q(""),l=q(a.value),c=qe({from_date:a.value,to_date:"",workshop:""}),o=fe();X(()=>c,()=>ee(this,null,function*(){n.value=!0,yield u.fetch(),n.value=!1}),{deep:!0});const u=z({url:"servicems.api.api.get_service_bays",method:"GET",auto:!0,cache:"bookings",makeParams(){return{from_date:c.from_date||ne(a.value,"yyyy-MM-dd"),to_date:c.to_date,workshop:c.workshop}},validate(v){},onSuccess:v=>{},onError:v=>{if(!v.messages){o.error(v.message);return}o.error(v.messages.join(`
`))}}),S=L(()=>{const v={};a.value=c.from_date?new Date(c.from_date):new Date,t.value=c.to_date?gt(new Date(c.to_date),a.value):6;for(let s=0;s<=t.value;s++){const m=ne(ut(a.value,s),"EE, yyyy-MM-dd");v[m]=[]}const y=u.data,f=y.filter(s=>s.bay_name).map(s=>({bay_name:s.bay_name,workshop:s.workshop,count:0,status:"",customer:"",booking_time:"",booking_date:""}));for(const s in v){const m=new Map;f.forEach(p=>{m.set(p.bay_name,p)}),v[s]=Array.from(m.values())}return Array.isArray(y)&&y.forEach(s=>{const m=s.booking_date;if(m){const p=ne(m,"EE, yyyy-MM-dd");if(v[p]){const $=v[p].findIndex(O=>O.bay_name===s.bay_name);$!==-1?v[p][$]=s:v[p].push(s)}}}),v});function g(v,y){const f=ne(y,"yyyy-MM-dd");window.location.href="/app/service-booking?bay="+v+"&booking_date="+f}function P(v,y){r.value=!0,i.value=v,l.value=ne(y,"yyyy-MM-dd")}const h=v=>{document.getElementById(`container-${v}`).scrollBy({left:-300,behavior:"smooth"})};function k(){return ee(this,null,function*(){n.value=!0,yield u.fetch().then(()=>{n.value=!1}).catch(()=>{n.value=!1})})}const D=v=>{document.getElementById(`container-${v}`).scrollBy({left:300,behavior:"smooth"})};return tt(()=>ee(this,null,function*(){yield u.fetch(),n.value=!1})),(v,y)=>(d(),b(G,null,[w("div",rn,[_(nn,{filters:c},null,8,["filters"])]),w("div",on,[n.value?(d(),b("div",sn,"Loading bookings...")):C("",!0),(d(!0),b(G,null,J(S.value,(f,s)=>(d(),b("div",{key:s,class:"mb-5"},[w("div",ln,[w("h1",cn,T(s),1),w("div",un,[f.length>4?(d(),b("button",{key:0,onClick:m=>h(s),class:"absolute left-0 top-1/2 transform -translate-y-1/2 bg-gray-500 px-2 text-white font-bold text-xl rounded-l-md z-10"}," < ",8,dn)):C("",!0),w("div",{id:`container-${s}`,class:"flex overflow-x-hidden gap-4 px-8 h-40 custom-scroll-hidden relative"},[(d(!0),b(G,null,J(f,m=>(d(),b("div",{key:m.name,class:Y([[m.status?"bg-blue-300 border-blue-300 shadow-lg shadow-cyan-500/50 font-mono":"bg-white-overlay-500 border-0 shadow-xl shadow-cyan-500/50 font-sans"],"w-[140px] h-[150px] flex-shrink-0 rounded-md shadow-md p-2 flex flex-col justify-between"])},[w("div",hn,[w("p",fn,T(m.bay_name),1),m.status?(d(),b("p",gn," Status: "+T(m.status==="In Progress"?"Progress":m.status),1)):C("",!0)]),w("div",yn,[w("div",{class:"bg-gray-400 text-black rounded-full w-9 h-9 flex items-center justify-center text-5xl font-bold hover:bg-blue-700/50",onClick:p=>g(m.bay_name,s)},T(m.count||0),9,bn)]),w("div",vn,[w("button",{class:"bg-gray-400 text-black font-bold px-2 rounded-md text-base w-full h-5",onClick:p=>P(m.bay_name,s)}," +Add ",8,wn)])],2))),128))],8,mn),f.length>4?(d(),b("button",{key:1,onClick:m=>D(s),class:"absolute right-0 top-1/2 transform -translate-y-1/2 bg-gray-500 px-2 text-white font-bold text-xl rounded-r-md z-10"}," > ",8,pn)):C("",!0)])])]))),128)),_(Ja,{showDialog:r.value,bay:i.value,booking_date:l.value,onCloseDialog:y[0]||(y[0]=f=>r.value=!1),onRefreshData:k},null,8,["showDialog","bay","booking_date"])])],64))}};var kn=ie(xn,[["__scopeId","data-v-6c8eb951"]]);const _n={class:"flex"},Cn={class:"flex-1 max-w-7xl py-8 mx-auto w-full"},Mn={__name:"Home",setup(e){return z({url:"ping",auto:!0}),(a,t)=>(d(),b("div",_n,[_(lt),w("div",Cn,[t[0]||(t[0]=w("h1",{class:"text-3xl font-bold text-center"},"Welcome to Booking Page",-1)),_(kn)])]))}};export{Mn as default};
