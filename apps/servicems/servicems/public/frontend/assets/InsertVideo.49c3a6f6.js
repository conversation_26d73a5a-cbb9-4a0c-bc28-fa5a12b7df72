var g=(o,e,t)=>new Promise((p,s)=>{var l=i=>{try{a(t.next(i))}catch(r){s(r)}},d=i=>{try{a(t.throw(i))}catch(r){s(r)}},a=i=>i.done?p(i.value):Promise.resolve(i.value).then(l,d);a((t=t.apply(o,e)).next())});import{_ as y,o as c,c as _,e as D,m as F,p as k,q as S,S as x,U as C,r as m,f as u,l as n,D as f,t as N,k as U,a as v,F as A}from"./index.7442ea66.js";class E{constructor(){this.listeners={},this.failed=!1}on(e,t){this.listeners[e]=this.listeners[e]||[],this.listeners[e].push(t)}trigger(e,t){(this.listeners[e]||[]).forEach(s=>{s.call(this,t)})}upload(e,t){return new Promise((p,s)=>{let l=new XMLHttpRequest;l.upload.addEventListener("loadstart",()=>{this.trigger("start")}),l.upload.addEventListener("progress",i=>{i.lengthComputable&&this.trigger("progress",{uploaded:i.loaded,total:i.total})}),l.upload.addEventListener("load",()=>{this.trigger("finish")}),l.addEventListener("error",()=>{this.trigger("error"),s()}),l.onreadystatechange=()=>{if(l.readyState==XMLHttpRequest.DONE){let i;if(l.status===200){let r=null;try{r=JSON.parse(l.responseText)}catch(V){r=l.responseText}let h=r.message||r;p(h)}else if(l.status===403)i=JSON.parse(l.responseText);else{this.failed=!0;try{i=JSON.parse(l.responseText)}catch(r){}}i&&i.exc&&console.error(JSON.parse(i.exc)[0]),s(i)}};const d=t.upload_endpoint||"/api/method/upload_file";l.open("POST",d,!0),l.setRequestHeader("Accept","application/json"),window.csrf_token&&window.csrf_token!=="{{ csrf_token }}"&&l.setRequestHeader("X-Frappe-CSRF-Token",window.csrf_token);let a=new FormData;e&&a.append("file",e,e.name),a.append("is_private",t.private?"1":"0"),a.append("folder",t.folder||"Home"),t.file_url&&a.append("file_url",t.file_url),t.doctype&&a.append("doctype",t.doctype),t.docname&&a.append("docname",t.docname),t.fieldname&&a.append("fieldname",t.fieldname),t.method&&a.append("method",t.method),t.type&&a.append("type",t.type),l.send(a)})}}const O={name:"FileUploader",props:{fileTypes:{type:[String,Array]},uploadArgs:{type:Object},validateFile:{type:Function,default:null}},data(){return{uploader:null,uploading:!1,uploaded:0,error:null,message:"",total:0,file:null,finishedUploading:!1}},computed:{progress(){let o=Math.floor(this.uploaded/this.total*100);return isNaN(o)?0:o},success(){return this.finishedUploading&&!this.error}},methods:{inputRef(){return this.$refs.input},openFileSelector(){this.$refs.input.click()},onFileAdd(o){return g(this,null,function*(){if(this.error=null,this.file=o.target.files[0],this.file&&this.validateFile)try{let e=yield this.validateFile(this.file);e&&(this.error=e)}catch(e){this.error=e}this.error||this.uploadFile(this.file)})},uploadFile(o){return g(this,null,function*(){this.error=null,this.uploaded=0,this.total=0,this.uploader=new E,this.uploader.on("start",()=>{this.uploading=!0}),this.uploader.on("progress",e=>{this.uploaded=e.uploaded,this.total=e.total}),this.uploader.on("error",()=>{this.uploading=!1,this.error="Error Uploading File"}),this.uploader.on("finish",()=>{this.uploading=!1,this.finishedUploading=!0}),this.uploader.upload(o,this.uploadArgs||{}).then(e=>{this.$emit("success",e)}).catch(e=>{this.uploading=!1;let t="Error Uploading File";e!=null&&e._server_messages?t=JSON.parse(JSON.parse(e._server_messages)[0]).message:e!=null&&e.exc&&(t=JSON.parse(e.exc)[0].split(`
`).slice(-2,-1)[0]),this.error=t,this.$emit("failure",e)})})}},expose:["inputRef"]},R=["accept"];function T(o,e,t,p,s,l){return c(),_("div",null,[D("input",{ref:"input",type:"file",accept:t.fileTypes,class:"hidden",onChange:e[0]||(e[0]=(...d)=>l.onFileAdd&&l.onFileAdd(...d))},null,40,R),F(o.$slots,"default",k(S({file:s.file,uploading:s.uploading,progress:l.progress,uploaded:s.uploaded,message:s.message,error:s.error,total:s.total,success:l.success,openFileSelector:l.openFileSelector})))])}var L=y(O,[["render",T]]);const B={name:"InsertImage",props:["editor"],expose:["openDialog"],data(){return{addVideoDialog:{url:"",file:null,show:!1}}},components:{Button:x,Dialog:C,FileUploader:L},methods:{openDialog(){this.addVideoDialog.show=!0},onVideoSelect(o){let e=o.target.files[0];!e||(this.addVideoDialog.file=e)},addVideo(o){this.editor.chain().focus().insertContent(`<video src="${o}"></video>`).run(),this.reset()},reset(){this.addVideoDialog=this.$options.data().addVideoDialog}}},J={class:"flex items-center space-x-2"},H=["src"];function q(o,e,t,p,s,l){const d=m("Button"),a=m("FileUploader"),i=m("Dialog");return c(),_(A,null,[F(o.$slots,"default",k(S({onClick:l.openDialog}))),u(i,{options:{title:"Add Video"},modelValue:s.addVideoDialog.show,"onUpdate:modelValue":e[2]||(e[2]=r=>s.addVideoDialog.show=r),onAfterLeave:l.reset},{"body-content":n(()=>[u(a,{"file-types":"video/*",onSuccess:e[0]||(e[0]=r=>s.addVideoDialog.url=r.file_url)},{default:n(({file:r,progress:h,uploading:V,openFileSelector:w})=>[D("div",J,[u(d,{onClick:w},{default:n(()=>[f(N(V?`Uploading ${h}%`:s.addVideoDialog.url?"Change Video":"Upload Video"),1)]),_:2},1032,["onClick"]),s.addVideoDialog.url?(c(),U(d,{key:0,onClick:()=>{s.addVideoDialog.url=null,s.addVideoDialog.file=null}},{default:n(()=>e[3]||(e[3]=[f(" Remove ")])),_:2},1032,["onClick"])):v("",!0)])]),_:1}),s.addVideoDialog.url?(c(),_("video",{key:0,src:s.addVideoDialog.url,class:"mt-2 w-full rounded-lg",type:"video/mp4",controls:""},null,8,H)):v("",!0)]),actions:n(()=>[u(d,{variant:"solid",onClick:e[1]||(e[1]=r=>l.addVideo(s.addVideoDialog.url))},{default:n(()=>e[4]||(e[4]=[f(" Insert Video ")])),_:1}),u(d,{onClick:l.reset},{default:n(()=>e[5]||(e[5]=[f("Cancel")])),_:1},8,["onClick"])]),_:1},8,["modelValue","onAfterLeave"])],64)}var P=y(B,[["render",q]]);export{P as default};
