[pre_model_sync]

[post_model_sync]

csf_tz.patches.custom_fields.custom_fields_for_removed_edu_fields_in_csf_tz
csf_tz.patches.remove_stock_entry_qty_field
csf_tz.patches.remove_core_doctype_custom_docperm
csf_tz.patches.add_custom_fields_for_sales_invoice_item_and_purchase_invoice_item
csf_tz.patches.add_custom_fields_on_customer_for_auto_close_dn
csf_tz.patches.custom_fields.create_custom_fields_for_additional_salary
csf_tz.patches.custom_fields.auth_otp_custom_fields
csf_tz.patches.custom_fields.payroll_approval_custom_fields
csf_tz.patches.custom_fields.attendance_overtime_calculation_custom_fields
csf_tz.patches.add_custom_fields_for_employee_advance
csf_tz.patches.add_custom_field_for_cusomer_suppliers_groups
csf_tz.patches.fix_module_for_core_reports
csf_tz.patches.create_the_stock_entry_type
csf_tz.patches.custom_fields.add_fields_in_stock_entry
csf_tz.patches.tz_post_code.create_tz_post_code
csf_tz.patches.custom_fields.payroll_cost_center_custom_fields 
csf_tz.patches.update_salary_slips_from_currrent_employee_payroll_cost_center
csf_tz.patches.custom_fields.delete_employee_custom_fields
csf_tz.patches.delete_default_value_fields
csf_tz.patches.disable_signup_in_website_settings
csf_tz.patches.update_payware_settings_values_to_csf_tz_settings
csf_tz.patches.custom_fields.create_custom_fields_for_trade_in_feature
