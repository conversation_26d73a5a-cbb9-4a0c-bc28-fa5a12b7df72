[{"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.paid==0", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Repayment Schedule", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "change_amount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "paid", "label": "Change Amount", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:36:59.411030", "name": "Repayment Schedule-change_amount", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.change_amount==1", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Repayment Schedule", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "changed_principal_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "change_amount", "label": "Changed Principal Amount", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:36:53.437116", "name": "Repayment Schedule-changed_principal_amount", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.change_amount==1", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Repayment Schedule", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "changed_interest_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "changed_principal_amount", "label": "Changed Interest Amount", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:36:48.588660", "name": "Repayment Schedule-changed_interest_amount", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "payware_specifics", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "disabled", "label": "Payware Specifics", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:37:15.863472", "name": "Salary Component-payware_specifics", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.type==\"Deduction\"", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "create_cash_journal", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payware_specifics", "label": "Create Cash Journal", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:08.564648", "name": "Salary Component-create_cash_journal", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:(doc.type==\"Earning\" && doc.do_not_include_in_total==0)", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "sdl_emolument_category", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "create_cash_journal", "label": "SDL Emolument Category", "length": 0, "mandatory_depends_on": null, "modified": "2020-02-21 20:56:19.842840", "name": "Salary Component-sdl_emolument_category", "no_copy": 0, "non_negative": 0, "options": "None\nAny Other Allowances\nBasic pay\nLeave pay\nSick pay\nPayment in Lieu of Leave\nFees\nCommission\nBonus\nGratuity\nSubsistence Allowance\nTraveling Allowance\nEntertainment Allowance\nHousing Allowance", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_16", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "sdl_emolument_category", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:37:04.529362", "name": "Salary Component-column_break_16", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.type==\"Earning\"", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "based_on_hourly_rate", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_16", "label": "Based on Hourly Rate", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:29.723933", "name": "Salary Component-based_on_hourly_rate", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "based_on_hourly_rate", "description": "Enter percentage % e.g. for 1.5 times enter 150%", "docstatus": 0, "doctype": "Custom Field", "dt": "Salary Component", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "hourly_rate", "fieldtype": "Percent", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "based_on_hourly_rate", "label": "Hourly Rate", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:24.893715", "name": "Salary Component-hourly_rate", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Loan", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "not_from_salary_loan_repayments", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "repayment_schedule", "label": "Not from Salary Loan Repayments", "length": 0, "mandatory_depends_on": null, "modified": "2020-01-13 00:29:29.512557", "name": "Loan-not_from_salary_loan_repayments", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Loan", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "loan_repayments_not_from_salary", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "not_from_salary_loan_repayments", "label": "Loan Repayments Not From Salary", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:13.673970", "name": "Loan-loan_repayments_not_from_salary", "no_copy": 0, "non_negative": 0, "options": "Loan NFS Repayments", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "<PERSON><PERSON>", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "overtime_components", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "hour_rate", "label": "Overtime Components", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:40.030549", "name": "<PERSON><PERSON> Slip-overtime_components", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "<PERSON><PERSON>", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "salary_slip_ot_component", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "overtime_components", "label": "Salary Slip OT Component", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:39:34.526842", "name": "Salary Slip-salary_slip_ot_component", "no_copy": 0, "non_negative": 0, "options": "Salary Slip OT Component", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bank_payment_details", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "payment_account", "label": "Bank Payment Details", "length": 0, "mandatory_depends_on": null, "modified": "2019-08-01 14:57:29.215552", "name": "Payroll Entry-bank_payment_details", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Loan", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "total_nsf_repayments", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "total_payment", "label": "Other Repayments", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-03 16:36:43.703791", "name": "Loan-total_nsf_repayments", "no_copy": 0, "non_negative": 0, "options": "Company:company:default_currency", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bank_account_for_transfer", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bank_payment_details", "label": "Bank Account for Transfer", "length": 0, "mandatory_depends_on": null, "modified": "2020-08-29 10:22:31.570122", "name": "Payroll Entry-bank_account_for_transfer", "no_copy": 0, "non_negative": 0, "options": "Bank Account", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_34", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bank_account_for_transfer", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2019-08-01 14:58:49.138438", "name": "Payroll Entry-column_break_34", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "cheque_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_34", "label": "Cheque Number", "length": 0, "mandatory_depends_on": null, "modified": "2019-08-01 14:58:48.945205", "name": "Payroll Entry-cheque_number", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "cheque_date", "fieldtype": "Date", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "cheque_number", "label": "Cheque Date", "length": 0, "mandatory_depends_on": null, "modified": "2019-08-01 14:58:49.298214", "name": "Payroll Entry-cheque_date", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Payroll Entry", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "section_break_35", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "cheque_date", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2020-08-28 16:07:43.297644", "name": "Payroll Entry-section_break_35", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "statutory_details", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "branch", "label": "Statutory Details", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:02:43.983151", "name": "Employee-statutory_details", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pension_fund", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "statutory_details", "label": "Pension Fund", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:02:29.445415", "name": "Employee-pension_fund", "no_copy": 0, "non_negative": 0, "options": "\nNSSF\nPPF", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pension_fund_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pension_fund", "label": "Pension Fund Number", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:02:19.556948", "name": "Employee-pension_fund_number", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "wcf_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pension_fund_number", "label": "WCF Number", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:01:36.094822", "name": "Employee-wcf_number", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_54", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "wcf_number", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2020-08-28 11:21:14.375156", "name": "Employee-column_break_54", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "999-999-999", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "tin_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_54", "label": "TIN Number", "length": 0, "mandatory_depends_on": null, "modified": "2020-08-28 11:42:00.423112", "name": "Employee-tin_number", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "national_identity", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tin_number", "label": "National Identity", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 10:57:52.506504", "name": "Employee-national_identity", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 1, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "other_allowance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "ctc", "label": "Other Allowance", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-19 11:15:41.355680", "name": "Employee-other_allowance", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "worker_subsistence", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "other_allowance", "label": "Worker Subsistence", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-25 11:02:07.885126", "name": "Employee-worker_subsistence", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_49", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "worker_subsistence", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2020-09-08 09:33:12.893860", "name": "Employee-column_break_49", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "enable_biometric", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "attendance_device_id", "label": "Enable Biometric Attendance", "length": 0, "mandatory_depends_on": null, "modified": "2020-01-13 22:49:16.070450", "name": "Employee-enable_biometric", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "biometric_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "enable_biometric", "label": "Biometric Employee ID", "length": 0, "mandatory_depends_on": null, "modified": "2020-01-12 23:31:12.236589", "name": "Employee-biometric_id", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 1, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "biometric_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "biometric_id", "label": "Biometric Employee Code", "length": 0, "mandatory_depends_on": null, "modified": "2020-01-12 23:31:32.711236", "name": "Employee-biometric_code", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 1, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "area", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "biometric_code", "label": "Area", "length": 0, "mandatory_depends_on": null, "modified": "2020-01-12 23:33:27.310096", "name": "Employee-area", "no_copy": 0, "non_negative": 0, "options": "Employee Area", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_50", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bank_name", "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:01:50.417359", "name": "Employee-column_break_50", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "eval:doc.salary_mode == 'Bank'", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "bank_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bank_ac_no", "label": "Bank Code", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:01:22.872766", "name": "Employee-bank_code", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "overtime_components", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "bank_code", "label": "Overtime Components", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:04:49.873588", "name": "Employee-overtime_components", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "employee_ot_component", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "overtime_components", "label": "Employee OT Component", "length": 0, "mandatory_depends_on": null, "modified": "2019-12-02 11:04:37.206071", "name": "Employee-employee_ot_component", "no_copy": 0, "non_negative": 0, "options": "Employee OT Component", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "attachments", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "health_details", "label": "Attachments", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-26 15:40:35.333309", "name": "Employee-attachments", "no_copy": 0, "non_negative": 0, "options": null, "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Employee", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "files", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "attachments", "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-26 15:42:04.237924", "name": "Employee-files", "no_copy": 0, "non_negative": 0, "options": "Document Attachment", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}]