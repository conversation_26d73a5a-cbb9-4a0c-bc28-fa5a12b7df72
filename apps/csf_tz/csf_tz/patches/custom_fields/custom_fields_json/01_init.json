[{"name": "Employee-custom_employee_country", "owner": "Administrator", "creation": "2025-07-14 10:08:02.475162", "modified": "2025-07-14 10:08:02.475162", "modified_by": "Administrator", "docstatus": 0, "idx": 157, "is_system_generated": 0, "dt": "Employee", "label": "Employee Country", "fieldname": "custom_employee_country", "insert_after": "custom_bank_account_name", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "fetch_from": "", "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-custom_bank_account_name", "owner": "Administrator", "creation": "2025-07-14 10:08:02.162716", "modified": "2025-07-14 10:08:02.162716", "modified_by": "Administrator", "docstatus": 0, "idx": 156, "is_system_generated": 0, "dt": "Employee", "label": "Bank Account Name", "fieldname": "custom_bank_account_name", "insert_after": "custom_bank_country_code", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-custom_bank_country_code", "owner": "Administrator", "creation": "2025-07-14 10:08:01.849583", "modified": "2025-07-14 10:08:01.849583", "modified_by": "Administrator", "docstatus": 0, "idx": 155, "is_system_generated": 0, "dt": "Employee", "label": "Bank Country Code", "fieldname": "custom_bank_country_code", "insert_after": "custom_beneficiary_bank_bic", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "fetch_from": "bank_country.code", "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-custom_beneficiary_bank_bic", "owner": "Administrator", "creation": "2025-07-14 10:08:01.543270", "modified": "2025-07-14 10:08:01.543270", "modified_by": "Administrator", "docstatus": 0, "idx": 154, "is_system_generated": 0, "dt": "Employee", "label": "Beneficiary Bank BIC", "fieldname": "custom_beneficiary_bank_bic", "insert_after": "custom_employee_country_code", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "fetch_from": "", "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-custom_employee_country_code", "owner": "Administrator", "creation": "2025-07-14 10:08:01.236514", "modified": "2025-07-14 10:08:01.236514", "modified_by": "Administrator", "docstatus": 0, "idx": 153, "is_system_generated": 0, "dt": "Employee", "label": "Employee Country Code", "fieldname": "custom_employee_country_code", "insert_after": "custom_iso2022_details", "length": 0, "fieldtype": "Link", "precision": "", "hide_seconds": 0, "hide_days": 0, "options": "Country", "sort_options": 0, "fetch_if_empty": 0, "fetch_from": "", "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-custom_iso2022_details", "owner": "Administrator", "creation": "2025-07-14 10:08:01.048810", "modified": "2025-07-14 10:08:01.048810", "modified_by": "Administrator", "docstatus": 0, "idx": 152, "is_system_generated": 0, "dt": "Employee", "label": "ISO2022 Details", "fieldname": "custom_iso2022_details", "insert_after": "worker_subsistence", "length": 0, "fieldtype": "Tab Break", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}]