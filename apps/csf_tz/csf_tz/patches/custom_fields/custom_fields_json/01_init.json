[{"name": "Employee-iso2022_details_tab", "owner": "Administrator", "creation": "2024-01-08 10:59:36.659265", "modified": "2024-03-11 11:52:18.262136", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "ISO2022 Details", "fieldname": "iso2022_details_tab", "insert_after": "connections_tab", "length": 0, "fieldtype": "Tab Break", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T12:24:42.382Z"}, {"name": "Employee-iso2022_details_section", "owner": "Administrator", "creation": "2024-01-08 10:59:36.659265", "modified": "2024-03-11 11:52:18.262136", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "ISO2022 Details", "fieldname": "iso2022_details_section", "insert_after": "iso2022_details_tab", "length": 0, "fieldtype": "Section Break", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T12:24:42.382Z"}, {"name": "Employee-employee_country", "owner": "Administrator", "creation": "2024-01-08 10:59:36.659265", "modified": "2024-03-11 11:52:18.262136", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "Employee Country", "fieldname": "employee_country", "insert_after": "iso2022_details_section", "length": 0, "fieldtype": "Link", "precision": "", "hide_seconds": 0, "hide_days": 0, "options": "Country", "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T12:24:42.382Z"}, {"name": "Employee-employee_country_code", "owner": "Administrator", "creation": "2024-01-08 10:59:36.969011", "modified": "2024-03-11 11:52:18.449055", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "Employee Country Code", "fieldname": "employee_country_code", "insert_after": "employee_country", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_from": "employee_country.code", "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T12:27:17.729Z"}, {"name": "Employee-bank_country", "owner": "<EMAIL>", "creation": "2023-12-04 17:35:21.645060", "modified": "2024-04-12 17:04:58.597745", "modified_by": "<EMAIL>", "docstatus": 0, "idx": 113, "is_system_generated": 0, "dt": "Employee", "label": "Bank Country", "fieldname": "bank_country", "insert_after": "employee_country_code", "length": 0, "fieldtype": "Link", "precision": "", "hide_seconds": 0, "hide_days": 0, "options": "Country", "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "depends_on": "", "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T12:05:49.512Z"}, {"name": "Employee-bank_country_code", "owner": "<EMAIL>", "creation": "2023-12-04 17:35:22.103394", "modified": "2024-04-12 17:04:58.888127", "modified_by": "<EMAIL>", "docstatus": 0, "idx": 114, "is_system_generated": 0, "dt": "Employee", "label": "Bank Country Code", "fieldname": "bank_country_code", "insert_after": "bank_country", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "options": "Country", "sort_options": 0, "fetch_from": "bank_country.code", "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 1, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2024-06-18T11:59:46.816Z", "depends_on": "", "__unsaved": 1}, {"name": "Employee-beneficiary_bank_bic", "owner": "Administrator", "creation": "2024-06-04 22:30:42.853319", "modified": "2024-11-08 12:00:34.595348", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "Beneficiary Bank BIC", "fieldname": "beneficiary_bank_bic", "insert_after": "bank_country_code", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}, {"name": "Employee-bank_account_name", "owner": "Administrator", "creation": "2024-06-04 22:30:40.132382", "modified": "2024-11-08 11:38:09.094406", "modified_by": "Administrator", "docstatus": 0, "idx": 0, "is_system_generated": 1, "dt": "Employee", "label": "Bank Account Name", "fieldname": "bank_account_name", "insert_after": "beneficiary_bank_bic", "length": 0, "fieldtype": "Data", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 1, "hide_border": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field"}]