{"add_total_row": 0, "add_translate_data": 0, "columns": [], "creation": "2025-07-11 16:03:13.708571", "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "letter_head": "Rubis TZ", "letterhead": null, "modified": "2025-07-11 16:03:13.708571", "modified_by": "Administrator", "module": "CSF TZ", "name": "Register Salary", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "<PERSON><PERSON>", "report_name": "Register Salary", "report_type": "Script Report", "roles": [{"role": "Employee"}, {"role": "HR Manager"}, {"role": "Employee Self Service"}, {"role": "Rubis GM"}, {"role": "System Manager"}, {"role": "Report Manager"}], "timeout": 0}