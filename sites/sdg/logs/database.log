2025-05-16 18:04:28,138 WARNING database DDL Query made to DB:
create table `tabBlog Post` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`blog_category` varchar(140),
`blogger` varchar(140),
`route` varchar(140) unique,
`read_time` int(11) not null default 0,
`published_on` date,
`published` int(1) not null default 0,
`featured` int(1) not null default 0,
`hide_cta` int(1) not null default 0,
`enable_email_notification` int(1) not null default 1,
`disable_comments` int(1) not null default 0,
`disable_likes` int(1) not null default 0,
`blog_intro` text,
`content_type` varchar(140) default 'Markdown',
`content` longtext,
`content_md` longtext,
`content_html` longtext,
`email_sent` int(1) not null default 0,
`meta_title` varchar(60),
`meta_description` text,
`meta_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:28,218 WARNING database DDL Query made to DB:
create sequence if not exists web_form_list_column_id_seq nocache nocycle
2025-05-16 18:04:28,243 WARNING database DDL Query made to DB:
create table `tabWeb Form List Column` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:28,943 WARNING database DDL Query made to DB:
create table `tabWorkflow Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`reference_name` varchar(140),
`reference_doctype` varchar(140),
`workflow_state` varchar(140),
`completed_by_role` varchar(140),
`completed_by` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:28,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Action`
				ADD INDEX IF NOT EXISTS `reference_name_reference_doctype_status_index`(reference_name, reference_doctype, status)
2025-05-16 18:04:29,123 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Permitted Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,193 WARNING database DDL Query made to DB:
create table `tabWorkflow Document State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`doc_status` varchar(140) default '0',
`update_field` varchar(140),
`update_value` varchar(140),
`is_optional_state` int(1) not null default 0,
`avoid_status_override` int(1) not null default 0,
`next_action_email_template` varchar(140),
`allow_edit` varchar(140),
`send_email` int(1) not null default 1,
`message` text,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,273 WARNING database DDL Query made to DB:
create table `tabWorkflow State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_state_name` varchar(140) unique,
`icon` varchar(140),
`style` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,350 WARNING database DDL Query made to DB:
create table `tabWorkflow Transition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`action` varchar(140),
`next_state` varchar(140),
`allowed` varchar(140),
`allow_self_approval` int(1) not null default 1,
`send_email_to_creator` int(1) not null default 0,
`condition` longtext,
`workflow_builder_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,454 WARNING database DDL Query made to DB:
create table `tabWorkflow Action Master` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_action_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,546 WARNING database DDL Query made to DB:
create table `tabWorkflow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workflow_name` varchar(140) unique,
`document_type` varchar(140),
`is_active` int(1) not null default 0,
`override_status` int(1) not null default 0,
`send_email_alert` int(1) not null default 0,
`workflow_state_field` varchar(140) default 'workflow_state',
`workflow_data` json,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,662 WARNING database DDL Query made to DB:
create table `tabEmail Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`use_html` int(1) not null default 0,
`response_html` longtext,
`response` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,815 WARNING database DDL Query made to DB:
create table `tabEmail Unsubscribe` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`global_unsubscribe` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email`(`email`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:29,879 WARNING database DDL Query made to DB:
create table `tabIMAP Folder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`folder_name` varchar(140),
`append_to` varchar(140),
`uidvalidity` varchar(140),
`uidnext` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,011 WARNING database DDL Query made to DB:
create table `tabEmail Domain` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`domain_name` varchar(140) unique,
`email_server` varchar(140),
`use_imap` int(1) not null default 0,
`use_ssl` int(1) not null default 0,
`use_starttls` int(1) not null default 0,
`incoming_port` varchar(140),
`attachment_limit` int(11) not null default 0,
`smtp_server` varchar(140),
`use_tls` int(1) not null default 0,
`use_ssl_for_outgoing` int(1) not null default 0,
`smtp_port` varchar(140),
`append_emails_to_sent_folder` int(1) not null default 0,
`sent_folder_name` varchar(140) default 'Sent',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,100 WARNING database DDL Query made to DB:
create table `tabUnhandled Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_account` varchar(140),
`uid` varchar(140),
`reason` longtext,
`message_id` longtext,
`raw` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,173 WARNING database DDL Query made to DB:
create table `tabEmail Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sender` varchar(140),
`show_as_cc` text,
`message` longtext,
`status` varchar(140) default 'Not Sent',
`error` longtext,
`message_id` text,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`communication` varchar(140),
`send_after` datetime(6),
`priority` int(11) not null default 1,
`add_unsubscribe_link` int(1) not null default 1,
`unsubscribe_param` varchar(140),
`unsubscribe_method` varchar(140),
`expose_recipients` varchar(140),
`attachments` longtext,
`retry` int(11) not null default 0,
`email_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_name`(`reference_name`),
index `communication`(`communication`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `index_bulk_flush`(status, send_after, priority, creation)
2025-05-16 18:04:30,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue`
				ADD INDEX IF NOT EXISTS `message_id_index`(message_id(140))
2025-05-16 18:04:30,376 WARNING database DDL Query made to DB:
create table `tabEmail Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140) unique,
`email_account_name` varchar(140) unique,
`domain` varchar(140),
`service` varchar(140),
`auth_method` varchar(140) default 'Basic',
`backend_app_flow` int(1) not null default 0,
`password` text,
`awaiting_password` int(1) not null default 0,
`ascii_encode_password` int(1) not null default 0,
`connected_app` varchar(140),
`connected_user` varchar(140),
`login_id_is_different` int(1) not null default 0,
`login_id` varchar(140),
`enable_incoming` int(1) not null default 0,
`default_incoming` int(1) not null default 0,
`use_imap` int(1) not null default 0,
`use_ssl` int(1) not null default 0,
`use_starttls` int(1) not null default 0,
`email_server` varchar(140),
`incoming_port` varchar(140),
`attachment_limit` int(11) not null default 0,
`email_sync_option` varchar(140) default 'UNSEEN',
`initial_sync_count` varchar(140) default '250',
`append_emails_to_sent_folder` int(1) not null default 0,
`sent_folder_name` varchar(140),
`append_to` varchar(140),
`create_contact` int(1) not null default 1,
`enable_automatic_linking` int(1) not null default 0,
`notify_if_unreplied` int(1) not null default 0,
`unreplied_for_mins` int(11) not null default 30,
`send_notification_to` text,
`enable_outgoing` int(1) not null default 0,
`use_tls` int(1) not null default 0,
`use_ssl_for_outgoing` int(1) not null default 0,
`smtp_server` varchar(140),
`smtp_port` varchar(140),
`default_outgoing` int(1) not null default 0,
`always_use_account_email_id_as_sender` int(1) not null default 0,
`always_use_account_name_as_sender_name` int(1) not null default 0,
`send_unsubscribe_message` int(1) not null default 1,
`track_email_status` int(1) not null default 1,
`no_smtp_authentication` int(1) not null default 0,
`always_bcc` varchar(140),
`add_signature` int(1) not null default 0,
`signature` longtext,
`enable_auto_reply` int(1) not null default 0,
`auto_reply_message` longtext,
`footer` longtext,
`brand_logo` text,
`uidvalidity` varchar(140),
`uidnext` int(11) not null default 0,
`no_failed` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,490 WARNING database DDL Query made to DB:
create table `tabNewsletter Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,552 WARNING database DDL Query made to DB:
create table `tabEmail Queue Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`recipient` varchar(140),
`status` varchar(140) default 'Not Sent',
`error` longtext,
index `status`(`status`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient`
				ADD INDEX IF NOT EXISTS `modified_index`(modified)
2025-05-16 18:04:30,703 WARNING database DDL Query made to DB:
create table `tabDocument Follow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,777 WARNING database DDL Query made to DB:
create table `tabEmail Group Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_group` varchar(140),
`email` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_group`(`email_group`),
index `unsubscribed`(`unsubscribed`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,817 WARNING database DDL Query made to DB:
alter table `tabEmail Group Member`
					add unique `unique_email_group_email`(email_group, email)
2025-05-16 18:04:30,862 WARNING database DDL Query made to DB:
create table `tabNewsletter Email Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_group` varchar(140),
`total_subscribers` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:30,946 WARNING database DDL Query made to DB:
create table `tabNewsletter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_sent_at` datetime(6),
`total_recipients` int(11) not null default 0,
`total_views` int(11) not null default 0,
`email_sent` int(1) not null default 0,
`sender_name` varchar(140),
`sender_email` varchar(140),
`send_from` varchar(140),
`subject` text,
`content_type` varchar(140),
`message` longtext,
`message_md` longtext,
`message_html` longtext,
`campaign` varchar(140),
`send_unsubscribe_link` int(1) not null default 1,
`send_webview_link` int(1) not null default 0,
`scheduled_to_send` int(11) not null default 0,
`schedule_sending` int(1) not null default 0,
`schedule_send` datetime(6),
`published` int(1) not null default 0,
`route` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,010 WARNING database DDL Query made to DB:
create table `tabEmail Flag Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_completed` int(1) not null default 0,
`communication` varchar(140),
`action` varchar(140),
`email_account` varchar(140),
`uid` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,072 WARNING database DDL Query made to DB:
create table `tabEmail Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140) unique,
`is_spam` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,133 WARNING database DDL Query made to DB:
create table `tabNotification Recipient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`receiver_by_document_field` varchar(140),
`receiver_by_role` varchar(140),
`cc` longtext,
`bcc` longtext,
`condition` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,216 WARNING database DDL Query made to DB:
create table `tabAuto Email Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report` varchar(140),
`user` varchar(140) default 'User',
`enabled` int(1) not null default 1,
`report_type` varchar(140),
`reference_report` varchar(140),
`send_if_data` int(1) not null default 1,
`data_modified_till` int(11) not null default 0,
`no_of_rows` int(11) not null default 100,
`filters` text,
`filter_meta` text,
`from_date_field` varchar(140),
`to_date_field` varchar(140),
`dynamic_date_period` varchar(140),
`use_first_day_of_period` int(1) not null default 0,
`email_to` text,
`day_of_week` varchar(140) default 'Monday',
`sender` varchar(140),
`frequency` varchar(140),
`format` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,293 WARNING database DDL Query made to DB:
create table `tabEmail Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`total_subscribers` int(11) not null default 0,
`confirmation_email_template` varchar(140),
`welcome_email_template` varchar(140),
`welcome_url` varchar(140),
`add_query_parameters` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,345 WARNING database DDL Query made to DB:
create table `tabDocType Layout Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,504 WARNING database DDL Query made to DB:
create table `tabDocType Layout` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`route` varchar(140),
`client_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:31,735 WARNING database DDL Query made to DB:
create table `tabCustomize Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default 'Data',
`fieldname` varchar(140),
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`no_copy` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`translatable` int(1) not null default 1,
`link_filters` json,
`default` text,
`precision` varchar(140),
`length` int(11) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`depends_on` longtext,
`permlevel` int(11) not null default 0,
`hidden` int(1) not null default 0,
`read_only` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`allow_bulk_edit` int(1) not null default 0,
`collapsible_depends_on` longtext,
`ignore_user_permissions` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`remember_last_selected_value` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`in_filter` int(1) not null default 0,
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`description` text,
`placeholder` varchar(140),
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`columns` int(11) not null default 0,
`width` varchar(140),
`is_custom_field` int(1) not null default 0,
index `label`(`label`),
index `fieldtype`(`fieldtype`),
index `fieldname`(`fieldname`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,015 WARNING database DDL Query made to DB:
create table `tabCurrency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`currency_name` varchar(140) unique,
`enabled` int(1) not null default 0,
`fraction` varchar(140),
`fraction_units` int(11) not null default 0,
`smallest_currency_fraction_value` decimal(21,9) not null default 0,
`symbol` varchar(140),
`symbol_on_right` int(1) not null default 0,
`number_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,242 WARNING database DDL Query made to DB:
create table `tabCountry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country_name` varchar(140) unique,
`date_format` varchar(140),
`time_format` varchar(140) default 'HH:mm:ss',
`time_zones` text,
`code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,312 WARNING database DDL Query made to DB:
create table `tabChangelog Feed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`app_name` varchar(140),
`link` longtext,
`posting_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_timestamp`(`posting_timestamp`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,478 WARNING database DDL Query made to DB:
create table `tabTag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,533 WARNING database DDL Query made to DB:
create table `tabConsole Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script` longtext,
`type` varchar(140),
`committed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,600 WARNING database DDL Query made to DB:
create table `tabCustom HTML Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`private` int(1) not null default 0,
`html` longtext,
`script` longtext,
`style` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,705 WARNING database DDL Query made to DB:
create table `tabNotification Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`enable_email_notifications` int(1) not null default 1,
`enable_email_mention` int(1) not null default 1,
`enable_email_assignment` int(1) not null default 1,
`enable_email_threads_on_assigned_document` int(1) not null default 1,
`enable_email_energy_point` int(1) not null default 1,
`enable_email_share` int(1) not null default 1,
`enable_email_event_reminders` int(1) not null default 1,
`user` varchar(140),
`seen` int(1) not null default 0,
`energy_points_system_notifications` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,760 WARNING database DDL Query made to DB:
create table `tabRoute History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`route` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,829 WARNING database DDL Query made to DB:
create table `tabNote` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`public` int(1) not null default 0,
`notify_on_login` int(1) not null default 0,
`notify_on_every_login` int(1) not null default 0,
`expire_notification_on` date,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `expire_notification_on`(`expire_notification_on`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:32,896 WARNING database DDL Query made to DB:
create table `tabDashboard Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`chart_config` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,011 WARNING database DDL Query made to DB:
create table `tabKanban Board` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kanban_board_name` varchar(140) unique,
`reference_doctype` varchar(140),
`field_name` varchar(140),
`private` int(1) not null default 0,
`show_labels` int(1) not null default 0,
`filters` longtext,
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,087 WARNING database DDL Query made to DB:
create table `tabToDo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Open',
`priority` varchar(140) default 'Medium',
`color` varchar(140),
`date` date,
`allocated_to` varchar(140),
`description` longtext,
`reference_type` varchar(140),
`reference_name` varchar(140),
`role` varchar(140),
`assigned_by` varchar(140),
`assigned_by_full_name` varchar(140),
`sender` varchar(140),
`assignment_rule` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabToDo`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-05-16 18:04:33,201 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`y_field` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,293 WARNING database DDL Query made to DB:
create table `tabGlobal Search DocType` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,466 WARNING database DDL Query made to DB:
create table `tabNumber Card Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,525 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart` varchar(140),
`width` varchar(140) default 'Half',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,581 WARNING database DDL Query made to DB:
create table `tabList Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`filter_name` varchar(140),
`reference_doctype` varchar(140),
`for_user` varchar(140),
`filters` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,635 WARNING database DDL Query made to DB:
create table `tabNote Seen By` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,766 WARNING database DDL Query made to DB:
create table `tabEvent Participants` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,859 WARNING database DDL Query made to DB:
create table `tabTag Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`tag` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:33,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-05-16 18:04:33,986 WARNING database DDL Query made to DB:
create table `tabNotification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`for_user` varchar(140),
`type` varchar(140),
`email_content` longtext,
`document_type` varchar(140),
`read` int(1) not null default 0,
`document_name` varchar(140),
`attached_file` longtext,
`from_user` varchar(140),
`link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `for_user`(`for_user`),
index `document_name`(`document_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,050 WARNING database DDL Query made to DB:
create table `tabCalendar View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`subject_field` varchar(140),
`start_date_field` varchar(140),
`end_date_field` varchar(140),
`all_day` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,117 WARNING database DDL Query made to DB:
create table `tabKanban Board Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`column_name` varchar(140),
`status` varchar(140) default 'Active',
`indicator` varchar(140) default 'Gray',
`order` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,174 WARNING database DDL Query made to DB:
create table `tabNotification Subscribed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,251 WARNING database DDL Query made to DB:
create table `tabDesktop Icon` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_name` varchar(140),
`label` varchar(140),
`standard` int(1) not null default 0,
`custom` int(1) not null default 0,
`app` varchar(140),
`description` text,
`category` varchar(140),
`hidden` int(1) not null default 0,
`blocked` int(1) not null default 0,
`force_show` int(1) not null default 0,
`type` varchar(140),
`_doctype` varchar(140),
`_report` varchar(140),
`link` text,
`color` varchar(140),
`icon` varchar(140),
`reverse` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,296 WARNING database DDL Query made to DB:
alter table `tabDesktop Icon`
					add unique `unique_module_name_owner_standard`(module_name, owner, standard)
2025-05-16 18:04:34,391 WARNING database DDL Query made to DB:
create table `tabList View Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable_count` int(1) not null default 0,
`disable_comment_count` int(1) not null default 0,
`disable_sidebar_stats` int(1) not null default 0,
`disable_auto_refresh` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`disable_automatic_recency_filters` int(1) not null default 0,
`total_fields` varchar(140),
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,482 WARNING database DDL Query made to DB:
create table `tabEvent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`event_category` varchar(140),
`event_type` varchar(140),
`color` varchar(140),
`send_reminder` int(1) not null default 1,
`repeat_this_event` int(1) not null default 0,
`starts_on` datetime(6),
`ends_on` datetime(6),
`status` varchar(140) default 'Open',
`sender` varchar(140),
`all_day` int(1) not null default 0,
`sync_with_google_calendar` int(1) not null default 0,
`add_video_conferencing` int(1) not null default 0,
`google_calendar` varchar(140),
`google_calendar_id` varchar(140),
`google_calendar_event_id` varchar(320),
`google_meet_link` text,
`pulled_from_google_calendar` int(1) not null default 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` int(1) not null default 0,
`tuesday` int(1) not null default 0,
`wednesday` int(1) not null default 0,
`thursday` int(1) not null default 0,
`friday` int(1) not null default 0,
`saturday` int(1) not null default 0,
`sunday` int(1) not null default 0,
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `event_type`(`event_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,693 WARNING database DDL Query made to DB:
create table `tabWebhook Header` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` text,
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,888 WARNING database DDL Query made to DB:
create table `tabWebhook` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_doctype` varchar(140),
`webhook_docevent` varchar(140),
`enabled` int(1) not null default 1,
`condition` text,
`request_url` text,
`is_dynamic_url` int(1) not null default 0,
`timeout` int(11) not null default 5,
`background_jobs_queue` varchar(140),
`request_method` varchar(140) default 'POST',
`request_structure` varchar(140),
`enable_security` int(1) not null default 0,
`webhook_secret` text,
`webhook_json` longtext,
`preview_document` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:34,956 WARNING database DDL Query made to DB:
create table `tabOAuth Client` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client_id` varchar(140),
`app_name` varchar(140),
`user` varchar(140),
`client_secret` varchar(140),
`skip_authorization` int(1) not null default 0,
`scopes` text default 'all openid',
`redirect_uris` text,
`default_redirect_uri` varchar(140),
`grant_type` varchar(140),
`response_type` varchar(140) default 'Code',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:35,260 WARNING database DDL Query made to DB:
create table `tabLDAP Group Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ldap_group` varchar(140),
`erpnext_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:35,377 WARNING database DDL Query made to DB:
create table `tabSlack Webhook URL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_name` varchar(140) unique,
`webhook_url` varchar(140),
`show_document_link` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:35,488 WARNING database DDL Query made to DB:
create table `tabOAuth Authorization Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`authorization_code` varchar(140) unique,
`expiration_time` datetime(6),
`redirect_uri_bound_to_authorization_code` varchar(140),
`validity` varchar(140),
`nonce` varchar(140),
`code_challenge` varchar(140),
`code_challenge_method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:35,686 WARNING database DDL Query made to DB:
create table `tabOAuth Bearer Token` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`access_token` varchar(140) unique,
`refresh_token` varchar(140),
`expiration_time` datetime(6),
`expires_in` int(11) not null default 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:35,784 WARNING database DDL Query made to DB:
create table `tabSocial Login Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable_social_login` int(1) not null default 0,
`social_login_provider` varchar(140) default 'Custom',
`client_id` varchar(140),
`provider_name` varchar(140),
`client_secret` text,
`icon` varchar(140),
`base_url` varchar(140),
`sign_ups` varchar(140),
`authorize_url` varchar(140),
`access_token_url` varchar(140),
`redirect_url` varchar(140),
`api_endpoint` varchar(140),
`custom_base_url` int(1) not null default 0,
`api_endpoint_args` longtext,
`auth_url_data` longtext,
`user_id_property` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,114 WARNING database DDL Query made to DB:
create table `tabGoogle Calendar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 1,
`calendar_name` varchar(140) unique,
`user` varchar(140),
`pull_from_google_calendar` int(1) not null default 1,
`sync_as_public` int(1) not null default 0,
`push_to_google_calendar` int(1) not null default 1,
`google_calendar_id` varchar(140),
`refresh_token` text,
`authorization_code` text,
`next_sync_token` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,361 WARNING database DDL Query made to DB:
create table `tabConnected App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`provider_name` varchar(140),
`openid_configuration` varchar(140),
`client_id` varchar(140),
`redirect_uri` varchar(140),
`client_secret` text,
`authorization_uri` text,
`token_uri` varchar(140),
`revocation_uri` varchar(140),
`userinfo_uri` varchar(140),
`introspection_uri` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,468 WARNING database DDL Query made to DB:
create table `tabQuery Parameters` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,544 WARNING database DDL Query made to DB:
create table `tabGoogle Contacts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 0,
`email_id` varchar(140),
`last_sync_on` datetime(6),
`authorization_code` text,
`refresh_token` text,
`next_sync_token` text,
`pull_from_google_contacts` int(1) not null default 0,
`push_to_google_contacts` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,615 WARNING database DDL Query made to DB:
create table `tabWebhook Request Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook` varchar(140),
`reference_document` varchar(140),
`headers` longtext,
`data` longtext,
`user` varchar(140),
`url` text,
`response` longtext,
`error` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,721 WARNING database DDL Query made to DB:
create table `tabOAuth Client Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,777 WARNING database DDL Query made to DB:
create table `tabOAuth Scope` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scope` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,849 WARNING database DDL Query made to DB:
create table `tabIntegration Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_id` varchar(140),
`integration_request_service` varchar(140),
`is_remote_request` int(1) not null default 0,
`request_description` varchar(140),
`status` varchar(140) default 'Queued',
`url` text,
`request_headers` longtext,
`data` longtext,
`output` longtext,
`error` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,915 WARNING database DDL Query made to DB:
create table `tabToken Cache` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`connected_app` varchar(140),
`provider_name` varchar(140),
`access_token` text,
`refresh_token` text,
`expires_in` int(11) not null default 0,
`state` varchar(140),
`success_uri` varchar(140),
`token_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:36,974 WARNING database DDL Query made to DB:
create table `tabWebhook Data` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`key` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:37,090 WARNING database DDL Query made to DB:
create table `tabPrint Heading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_heading` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:37,261 WARNING database DDL Query made to DB:
create table `tabNetwork Printer Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`server_ip` varchar(140) default 'localhost',
`port` int(11) not null default 631,
`printer_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:37,353 WARNING database DDL Query made to DB:
create table `tabLetter Head` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`letter_head_name` varchar(140) unique,
`source` varchar(140),
`footer_source` varchar(140) default 'HTML',
`disabled` int(1) not null default 0,
`is_default` int(1) not null default 0,
`image` text,
`image_height` decimal(21,9) not null default 0,
`image_width` decimal(21,9) not null default 0,
`align` varchar(140) default 'Left',
`content` longtext,
`footer` longtext,
`footer_image` text,
`footer_image_height` decimal(21,9) not null default 0,
`footer_image_width` decimal(21,9) not null default 0,
`footer_align` varchar(140),
`header_script` longtext,
`footer_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_default`(`is_default`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:37,471 WARNING database DDL Query made to DB:
create table `tabPrint Format Field Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`field` varchar(140),
`template_file` varchar(140),
`module` varchar(140),
`standard` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:37,806 WARNING database DDL Query made to DB:
create table `tabAddress Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140) unique,
`is_default` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,006 WARNING database DDL Query made to DB:
create table `tabAddress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`address_title` varchar(140),
`address_type` varchar(140),
`address_line1` varchar(240),
`address_line2` varchar(240),
`city` varchar(140),
`county` varchar(140),
`state` varchar(140),
`country` varchar(140),
`pincode` varchar(140),
`email_id` varchar(140),
`phone` varchar(140),
`fax` varchar(140),
`is_primary_address` int(1) not null default 0,
`is_shipping_address` int(1) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `city`(`city`),
index `country`(`country`),
index `pincode`(`pincode`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,120 WARNING database DDL Query made to DB:
create table `tabContact Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`phone` varchar(140),
`is_primary_phone` int(1) not null default 0,
`is_primary_mobile_no` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,182 WARNING database DDL Query made to DB:
create table `tabSalutation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salutation` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,244 WARNING database DDL Query made to DB:
create table `tabGender` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gender` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,335 WARNING database DDL Query made to DB:
create table `tabContact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email_id` varchar(140),
`user` varchar(140),
`address` varchar(140),
`sync_with_google_contacts` int(1) not null default 0,
`status` varchar(140) default 'Passive',
`salutation` varchar(140),
`designation` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`mobile_no` varchar(140),
`company_name` varchar(140),
`image` text,
`google_contacts` varchar(140),
`google_contacts_id` varchar(140),
`pulled_from_google_contacts` int(1) not null default 0,
`is_primary_contact` int(1) not null default 0,
`department` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,417 WARNING database DDL Query made to DB:
create table `tabContact Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140),
`is_primary` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,557 WARNING database DDL Query made to DB:
create table `tabEnergy Point Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`rule_name` varchar(140) unique,
`reference_doctype` varchar(140),
`for_doc_event` varchar(140) default 'Custom',
`field_to_check` varchar(140),
`points` int(11) not null default 0,
`for_assigned_users` int(1) not null default 0,
`user_field` varchar(140),
`multiplier_field` varchar(140),
`max_points` int(11) not null default 0,
`apply_only_once` int(1) not null default 0,
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,738 WARNING database DDL Query made to DB:
create table `tabEnergy Point Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`type` varchar(140),
`points` int(11) not null default 0,
`rule` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reverted` int(1) not null default 0,
`revert_of` varchar(140),
`reason` text,
`seen` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,857 WARNING database DDL Query made to DB:
create table `tabReview Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`level_name` varchar(140) unique,
`role` varchar(140) unique,
`review_points` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:38,979 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-05-16 18:04:39,338 WARNING database DDL Query made to DB:
create table `tabMilestone Tracker` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`track_field` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,392 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,466 WARNING database DDL Query made to DB:
create table `tabAssignment Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`due_date_based_on` varchar(140),
`priority` int(11) not null default 0,
`disabled` int(1) not null default 0,
`description` text default 'Automatic Assignment',
`assign_condition` longtext,
`unassign_condition` longtext,
`close_condition` longtext,
`rule` varchar(140),
`field` varchar(140),
`last_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,573 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,626 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,709 WARNING database DDL Query made to DB:
create table `tabAuto Repeat` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`submit_on_creation` int(1) not null default 0,
`start_date` date,
`end_date` date,
`disabled` int(1) not null default 0,
`frequency` varchar(140),
`repeat_on_day` int(11) not null default 0,
`repeat_on_last_day` int(1) not null default 0,
`next_schedule_date` date,
`notify_by_email` int(1) not null default 0,
`recipients` text,
`template` varchar(140),
`subject` varchar(140),
`message` text default 'Please find attached {{ doc.doctype }} #{{ doc.name }}',
`print_format` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `next_schedule_date`(`next_schedule_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 18:04:39,820 WARNING database DDL Query made to DB:
create table `tabAssignment Rule Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,122 WARNING database DDL Query made to DB:
create table `tabESG Criterion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criterion` varchar(140),
`category` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,255 WARNING database DDL Query made to DB:
create table `tabSDG Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal_name` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,334 WARNING database DDL Query made to DB:
create table `tabGRI Standard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`standard_code` varchar(140),
`title` varchar(140),
`description` text,
`disclosure_requirement` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,442 WARNING database DDL Query made to DB:
create table `tabSDG Target` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sdg_goal` varchar(140),
`target_code` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,508 WARNING database DDL Query made to DB:
create table `tabSustainability Metric` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`linked_indicator` varchar(140),
`esg_criterion` varchar(140),
`gri_standard` varchar(140),
`data_source` varchar(140),
`reporting_frequency` varchar(140),
`unit` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,592 WARNING database DDL Query made to DB:
create table `tabSDG Indicator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sdg_target` varchar(140),
`indicator_code` varchar(140),
`definition` text,
`calculation_method` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:10,654 WARNING database DDL Query made to DB:
create table `tabSustainability Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`metric` varchar(140),
`reporting_period` date,
`value` decimal(21,9) not null default 0,
`remarks` text,
`evidence_file` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-16 21:02:39,085 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-04 22:44:50,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD COLUMN `goal_no` varchar(140) unique
2025-06-04 22:44:50,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD UNIQUE INDEX IF NOT EXISTS goal_no (`goal_no`)
2025-06-04 22:49:14,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Target` ADD COLUMN `goal_no` varchar(140), ADD COLUMN `target_no` varchar(140) unique
2025-06-04 22:49:14,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Target` ADD UNIQUE INDEX IF NOT EXISTS target_no (`target_no`)
2025-06-04 22:50:53,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD COLUMN `target_no` varchar(140), ADD COLUMN `indicator_no` varchar(140) unique
2025-06-04 22:50:53,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD UNIQUE INDEX IF NOT EXISTS indicator_no (`indicator_no`)
2025-06-04 22:51:43,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD COLUMN `description` text
2025-06-04 22:54:37,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabESG Criterion` ADD COLUMN `factor` varchar(140), ADD COLUMN `criteria` varchar(140)
2025-06-04 22:59:34,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabESG Criterion` ADD UNIQUE INDEX IF NOT EXISTS criteria (`criteria`)
2025-06-05 12:03:10,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD COLUMN `amended_from` varchar(140)
2025-06-05 12:03:11,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD INDEX `amended_from_index`(`amended_from`)
2025-06-21 12:05:50,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabSustainability Metric` ADD COLUMN `metric_name` varchar(140), ADD COLUMN `esg_pillar` varchar(140), ADD COLUMN `gri_disclosure` varchar(140), ADD COLUMN `sdg_link` varchar(140), ADD COLUMN `sdg_target` varchar(140), ADD COLUMN `measures` varchar(140)
2025-06-21 12:06:23,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabSustainability Metric` ADD UNIQUE INDEX IF NOT EXISTS metric_name (`metric_name`)
2025-07-02 16:27:12,476 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-02 16:27:12,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` int(1) not null default 0, ADD COLUMN `is_setup_complete` int(1) not null default 0
2025-07-02 16:27:13,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-07-02 16:27:14,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-07-13 11:32:03,790 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:36:29,116 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
