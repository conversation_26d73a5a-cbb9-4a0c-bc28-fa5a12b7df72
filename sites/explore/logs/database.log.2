2025-05-23 09:31:15,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0
2025-05-23 09:31:18,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-23 09:31:19,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0
2025-05-23 09:31:21,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-23 09:31:21,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-05-23 09:31:21,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-05-23 09:31:22,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-23 09:31:23,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-23 09:31:23,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-23 09:31:25,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-23 09:31:25,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`
2025-05-23 09:31:25,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-23 09:31:25,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-23 09:31:26,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-23 09:31:26,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-23 09:31:27,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-23 09:31:27,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-23 09:31:30,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date
2025-05-23 09:31:36,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0
2025-06-03 15:20:29,658 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_735b0b6f43cd3001'@'localhost'
2025-06-03 15:20:48,503 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_735b0b6f43cd3001`
2025-06-03 15:20:48,508 WARNING database DDL Query made to DB:
CREATE USER '_735b0b6f43cd3001'@'localhost' IDENTIFIED BY 'PiooDemKkYUVnx5o'
2025-06-03 15:20:48,517 WARNING database DDL Query made to DB:
CREATE DATABASE `_735b0b6f43cd3001` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-03 15:27:46,635 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-03 15:27:46,645 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-12 08:32:16,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-12 08:32:17,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:17,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabSection` MODIFY `monthly_target` decimal(21,9) not null default 0
2025-06-12 08:32:17,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-06-12 08:32:17,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-12 08:32:18,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report` MODIFY `a_vat` decimal(21,9) not null default 0, MODIFY `c_vat` decimal(21,9) not null default 0, MODIFY `d_turnover` decimal(21,9) not null default 0, MODIFY `a_net_sum` decimal(21,9) not null default 0, MODIFY `a_turnover` decimal(21,9) not null default 0, MODIFY `allowable_difference` decimal(21,9) not null default 0, MODIFY `total_turnover_ticked` decimal(21,9) not null default 0, MODIFY `e_turnover` decimal(21,9) not null default 0, MODIFY `money` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_vat` decimal(21,9) not null default 0, MODIFY `d_vat` decimal(21,9) not null default 0, MODIFY `total_excluding_vat_ticked` decimal(21,9) not null default 0, MODIFY `d_net_sum` decimal(21,9) not null default 0, MODIFY `b_turnover` decimal(21,9) not null default 0, MODIFY `total_turnover_ex_sr` decimal(21,9) not null default 0, MODIFY `c_net_sum` decimal(21,9) not null default 0, MODIFY `total_turnover` decimal(21,9) not null default 0, MODIFY `total_vat_ticked` decimal(21,9) not null default 0, MODIFY `b_vat` decimal(21,9) not null default 0, MODIFY `b_net_sum` decimal(21,9) not null default 0, MODIFY `total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0, MODIFY `c_turnover` decimal(21,9) not null default 0
2025-06-12 08:32:18,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:18,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:19,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 08:32:20,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabNMB Callback` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:20,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting GL Entry` MODIFY `credit_amount` decimal(21,9) not null default 0, MODIFY `debit_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 08:32:21,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-12 08:32:21,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-12 08:32:21,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `item_balance` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:21,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Cover Note` MODIFY `exchangerate` decimal(21,9) not null default 0, MODIFY `totalpremiumamountincludingtax` decimal(21,9) not null default 0, MODIFY `totalpremiumamountexcludingtax` decimal(21,9) not null default 0, MODIFY `commisionpaid` decimal(21,9) not null default 0, MODIFY `commisionrate` decimal(21,9) not null default 0
2025-06-12 08:32:22,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:32:23,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-12 08:32:23,316 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 08:32:23,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-12 08:32:23,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-12 08:32:23,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:24,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-06-12 08:32:24,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:24,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-06-12 08:32:25,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill` MODIFY `billequivalentamount` decimal(21,9) not null default 0, MODIFY `miscellaneousamount` decimal(21,9) not null default 0, MODIFY `billedamount` decimal(21,9) not null default 0
2025-06-12 08:32:26,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:26,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:26,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0
2025-06-12 08:32:27,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-12 08:32:27,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-12 08:32:27,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:32:28,007 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 08:32:28,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Record` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:28,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpen Invoice Exchange Rate Revaluation` MODIFY `exchange_rate_to_company_currency` decimal(21,9) not null default 0
2025-06-12 08:32:31,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:31,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-06-12 08:32:31,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-12 08:32:32,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-06-12 08:32:32,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Old` MODIFY `customer_signature` longtext default 'Customer Signature'
2025-06-12 08:32:32,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:32,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-12 08:32:33,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:33,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:33,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-06-12 08:32:34,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 08:32:34,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport File` MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-12 08:32:35,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0
2025-06-12 08:32:36,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Clearance` MODIFY `goods_quantity` decimal(21,9) not null default 0, MODIFY `loose_net_weight` decimal(21,9) not null default 0, MODIFY `loose_gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:36,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond` MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-12 08:32:36,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-06-12 08:32:38,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Request` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:38,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 08:32:39,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:39,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-06-12 08:32:40,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-06-12 08:32:41,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-06-12 08:32:41,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip` MODIFY `main_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Route` MODIFY `total_tzs` decimal(21,9) not null default 0, MODIFY `total_usd` decimal(21,9) not null default 0
2025-06-12 08:32:42,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:44,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-12 08:32:44,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Sales` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `planned_amount` decimal(21,9) not null default 0, MODIFY `actual_amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Serial No` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-06-12 08:32:45,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:46,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:46,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `rate_per_hour` decimal(21,9) not null default 0, MODIFY `billable_hours` decimal(21,9) not null default 0
2025-06-12 08:32:47,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-06-12 08:32:47,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Initiation` MODIFY `control_sum` decimal(21,9) not null default 0
2025-06-12 08:32:48,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-12 08:32:48,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 08:33:30,331 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-12 08:33:31,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` ADD COLUMN `vfd_payment_type` varchar(140)
2025-06-12 08:33:31,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0
2025-06-12 08:33:32,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-12 08:33:32,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-12 08:33:33,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-12 08:33:34,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-12 08:33:34,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:35,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:35,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-12 08:33:36,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:36,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-12 08:33:37,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:37,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0
2025-06-12 08:33:38,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:38,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 08:33:38,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-12 08:33:38,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0
2025-06-12 08:33:39,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-12 08:33:39,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:33:39,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-06-12 08:33:39,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `warehouse`, DROP INDEX `posting_date_index`
2025-06-12 08:33:39,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-12 08:33:40,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0
2025-06-12 08:33:40,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-12 08:33:40,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0
2025-06-12 08:33:40,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `expected_compensation` decimal(21,9) not null default 0, MODIFY `time_to_fill` decimal(21,9)
2025-06-12 08:33:41,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-06-12 08:33:41,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-06-12 08:33:45,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-06-12 08:33:45,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 08:33:45,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-12 08:33:46,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 08:33:46,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:47,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `actual_cost` decimal(21,9) not null default 0
2025-06-12 08:33:47,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:47,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:47,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:47,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `sanctioned_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:33:48,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:48,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 08:33:48,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:49,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:33:49,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:49,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-06-12 08:33:49,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:49,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `average_rating` decimal(3,2), MODIFY `expected_average_rating` decimal(3,2)
2025-06-12 08:33:49,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:49,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-06-12 08:33:50,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:50,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-06-12 08:33:50,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:50,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-06-12 08:33:50,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:50,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-06-12 08:33:50,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:50,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:50,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:51,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:51,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:51,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:51,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:52,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-06-12 08:33:52,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:52,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `total_leave_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-12 08:33:52,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:52,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-06-12 08:33:52,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:52,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-06-12 08:33:52,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:53,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0
2025-06-12 08:33:53,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:53,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `new_leaves_allocated` decimal(21,9) not null default 0
2025-06-12 08:33:53,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:53,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:53,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-06-12 08:33:53,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:53,984 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:54,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0
2025-06-12 08:33:54,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:54,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:54,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:54,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 08:33:54,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-06-12 08:33:55,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `applicant_rating` decimal(3,2), MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-06-12 08:33:55,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:55,962 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:56,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-06-12 08:33:56,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-12 08:33:56,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:56,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:56,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:56,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-06-12 08:33:56,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:57,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:57,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-06-12 08:33:57,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:57,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:57,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:57,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-06-12 08:33:57,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:58,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:58,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:58,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-06-12 08:33:58,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:58,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:58,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `total_estimated_cost` decimal(21,9) not null default 0, MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0
2025-06-12 08:33:59,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-06-12 08:33:59,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,448 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `revised_ctc` decimal(21,9) not null default 0, MODIFY `current_ctc` decimal(21,9) not null default 0
2025-06-12 08:33:59,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:59,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `max_leaves_allowed` decimal(21,9) not null default 0, MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0
2025-06-12 08:33:59,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-12 08:34:00,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,672 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:00,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_asset_recovery_cost` decimal(21,9) not null default 0, MODIFY `total_receivable_amount` decimal(21,9) not null default 0, MODIFY `total_payable_amount` decimal(21,9) not null default 0
2025-06-12 08:34:00,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:01,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 08:34:01,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:01,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-06-12 08:34:01,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:01,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:01,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-06-12 08:34:01,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:02,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:02,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 08:34:02,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:02,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-06-12 08:34:02,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:02,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 08:34:02,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:03,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:34:03,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:03,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 08:34:03,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:03,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:04,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:34:04,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:04,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `total_actual_amount` decimal(21,9) not null default 0, MODIFY `exemption_amount` decimal(21,9) not null default 0
2025-06-12 08:34:04,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:04,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:04,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:34:04,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:05,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 08:34:05,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:05,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `pension_rate` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `wcf_rate` decimal(21,9) not null default 0, MODIFY `base` decimal(21,9) not null default 0, MODIFY `heslb_rate` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `sdl_rate` decimal(21,9) not null default 0
2025-06-12 08:34:05,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:05,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-06-12 08:34:05,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:05,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0
2025-06-12 08:34:05,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:06,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `max_amount_eligible` decimal(21,9) not null default 0
2025-06-12 08:34:06,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:06,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-06-12 08:34:06,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:06,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:07,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:07,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:07,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-12 08:34:07,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 08:34:08,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_declared_amount` decimal(21,9) not null default 0, MODIFY `total_exemption_amount` decimal(21,9) not null default 0
2025-06-12 08:34:08,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-06-12 08:34:08,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-12 08:34:08,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0
2025-06-12 08:34:08,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:08,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0
2025-06-12 08:34:09,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:09,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:09,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:09,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0
2025-06-12 08:34:09,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:09,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0
2025-06-12 08:34:09,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:09,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:34:10,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:10,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 08:34:10,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:10,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-06-12 08:34:10,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:10,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0
2025-06-12 08:34:10,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `creation`(`creation`)
2025-06-12 08:34:11,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 08:34:11,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 08:34:12,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:34:12,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:34:12,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0
2025-06-12 08:34:12,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-06-12 08:34:13,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-12 08:34:13,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-06-12 08:34:22,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:34:25,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 08:34:25,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-12 08:34:27,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 08:34:27,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0
2025-06-12 08:34:27,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-12 08:34:27,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `allow_over_sell` int(1) not null default 1, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-06-24 17:53:18,920 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 17:53:19,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-06-24 17:53:20,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-24 17:53:21,544 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-24 17:53:22,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-24 17:53:22,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0
2025-06-24 17:53:24,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-24 17:53:24,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-06-24 17:53:24,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-24 17:53:25,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-24 17:53:25,147 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `parent`
2025-06-24 17:53:25,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-06-24 17:53:25,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `parent`
2025-06-24 17:53:25,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0
2025-06-24 17:53:25,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `parent`
2025-06-24 17:53:25,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `parent`
2025-06-24 17:53:26,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `parent`
2025-06-24 17:53:26,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `parent`
2025-06-24 17:53:26,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:26,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `parent`
2025-06-24 17:53:26,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:26,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `parent`
2025-06-24 17:53:27,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-24 17:53:27,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `parent`
2025-06-24 17:53:27,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `item_balance` decimal(21,9) not null default 0
2025-06-24 17:53:27,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `parent`
2025-06-24 17:53:27,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-24 17:53:27,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `parent`
2025-06-24 17:53:27,650 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `parent`
2025-06-24 17:53:27,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-24 17:53:27,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `parent`
2025-06-24 17:53:28,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `parent`
2025-06-24 17:53:28,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 17:53:28,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `parent`
2025-06-24 17:53:28,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:28,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `parent`
2025-06-24 17:53:28,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `parent`
2025-06-24 17:53:29,147 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:29,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `parent`
2025-06-24 17:53:29,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 17:53:29,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `parent`
2025-06-24 17:53:29,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0, MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-24 17:53:29,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `parent`
2025-06-24 17:53:29,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `parent`
2025-06-24 17:53:30,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-24 17:53:30,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` DROP INDEX `parent`
2025-06-24 17:53:30,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0
2025-06-24 17:53:30,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `parent`
2025-06-24 17:53:30,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0
2025-06-24 17:53:30,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `parent`
2025-06-24 17:53:30,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `parent`
2025-06-24 17:53:30,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `parent`
2025-06-24 17:53:31,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-24 17:53:31,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `parent`
2025-06-24 17:53:31,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-24 17:53:31,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `parent`
2025-06-24 17:53:31,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-24 17:53:31,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `parent`
2025-06-24 17:53:31,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `parent`
2025-06-24 17:53:32,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:32,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` DROP INDEX `parent`
2025-06-24 17:53:32,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `parent`
2025-06-24 17:53:32,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `parent`
2025-06-24 17:53:32,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `parent`
2025-06-24 17:53:32,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `parent`
2025-06-24 17:53:33,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 17:53:33,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `parent`
2025-06-24 17:53:33,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-24 17:53:33,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `parent`
2025-06-24 17:53:33,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-06-24 17:53:33,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `parent`
2025-06-24 17:53:33,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `parent`
2025-06-24 17:53:34,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 17:53:34,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `parent`
2025-06-24 17:53:34,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `parent`
2025-06-24 17:53:34,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-24 17:53:34,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `parent`
2025-06-24 17:53:34,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-06-24 17:53:34,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `parent`
2025-06-24 17:53:35,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-24 17:53:35,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`
2025-06-24 17:53:35,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `parent`
2025-06-24 17:53:35,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `parent`
2025-06-24 17:53:35,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-24 17:53:35,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `parent`
2025-06-24 17:53:35,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `parent`
2025-06-24 17:53:36,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `parent`
2025-06-24 17:53:36,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `parent`
2025-06-24 17:53:36,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-24 17:53:36,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `parent`
2025-06-24 17:53:36,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `parent`
2025-06-24 17:53:36,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `parent`
2025-06-24 17:53:37,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-24 17:53:37,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `parent`
2025-06-24 17:53:37,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `parent`
2025-06-24 17:53:37,609 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `parent`
2025-06-24 17:53:37,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `parent`
2025-06-24 17:53:38,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `parent`
2025-06-24 17:53:38,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `parent`
2025-06-24 17:53:38,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `parent`
2025-06-24 17:53:38,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `parent`
2025-06-24 17:53:38,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `parent`
2025-06-24 17:53:38,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `parent`
