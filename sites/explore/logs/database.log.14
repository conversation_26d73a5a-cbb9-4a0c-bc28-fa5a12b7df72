2025-02-18 14:38:59,870 WARNING database DDL Query made to DB:
create table `tabCommunications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type_of_communication` varchar(140),
`customer` varchar(140),
`customer_name` varchar(140),
`contacted_by` varchar(140),
`date_of_communication` datetime(6),
`communication_feedback` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:00,042 WARNING database DDL Query made to DB:
create table `tabPast Sales` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`item_sold` varchar(140),
`customer` varchar(140),
`amount` decimal(21,9) not null default 0,
`sold_date` date,
`serial_no` varchar(140),
`plate_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:00,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` ADD COLUMN `item_code` varchar(140), ADD COLUMN `serial_no` varchar(140), ADD COLUMN `reference` varchar(140)
2025-02-18 14:39:00,368 WARNING database DDL Query made to DB:
create table `tabPayment Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`planned_date` date,
`planned_amount` decimal(21,9) not null default 0,
`actual_date` date,
`actual_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:00,528 WARNING database DDL Query made to DB:
create table `tabPast Serial No` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`item_code` varchar(140),
`past_item_code` varchar(140),
`customer` varchar(140),
`past_item_group` varchar(140),
`amount` decimal(21,9) not null default 0,
`sales_ref_no` varchar(140),
`date_of_sale` date,
`plate_number` varchar(140),
`extra_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:00,812 WARNING database DDL Query made to DB:
create table `tabIssued Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item` varchar(140),
`requested` decimal(21,9) not null default 0,
`issued` decimal(21,9) not null default 0,
`difference` decimal(21,9) not null default 0,
`units` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:00,989 WARNING database DDL Query made to DB:
create table `tabRequested Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`approval_status` varchar(140) default 'Waiting Approval',
`items_issue_status` varchar(140) default 'Waiting Approval',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,156 WARNING database DDL Query made to DB:
create table `tabWorkshop Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,315 WARNING database DDL Query made to DB:
create table `tabUsed Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`item_description` varchar(140),
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`extra_information` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,451 WARNING database DDL Query made to DB:
create table `tabRequested Items Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`item` varchar(140),
`description` longtext,
`quantity` decimal(21,9) not null default 0,
`units` varchar(140),
`requested_for` varchar(140),
`status` varchar(140) default 'Open',
`recommended_by` varchar(140),
`recommended_date` datetime(6),
`approved_by` varchar(140),
`approved_date` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,596 WARNING database DDL Query made to DB:
create table `tabWorkshop Services Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`work_done` text,
`service_status` varchar(140),
`subcontracted` int(1) not null default 0,
`subcontractor` varchar(140),
`technician` varchar(140),
`start_date` date,
`start_time` time(6),
`end_date` date,
`end_time` time(6),
`billable_hours` decimal(21,9) not null default 0,
`rate_per_hour` decimal(21,9) not null default 0,
`currency_rate_per_hour` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,720 WARNING database DDL Query made to DB:
create table `tabWorkshop Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`service_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:01,914 WARNING database DDL Query made to DB:
create table `tabWorkshop Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`requested_for` varchar(140),
`requested_for_docname` varchar(140),
`requested_date` date,
`request_type` varchar(140),
`previous_job` varchar(140),
`details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:02,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabOTP Register` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `otp_secret` text, ADD COLUMN `registered` int(1) not null default 0, ADD COLUMN `amended_from` varchar(140)
2025-02-18 14:39:02,464 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:02,622 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Device` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`device_id` varchar(140) unique,
`device_nick_name` varchar(140),
`installed_date` date,
`device_site_name` varchar(140),
`device_location` varchar(140),
`device_supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:02,792 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`punch` varchar(140),
`user_id` varchar(140),
`uid` varchar(140),
`status` varchar(140),
`timestamp` datetime(6),
`device_id` varchar(140),
`device_ip` varchar(140),
`punch_direction` varchar(140),
`biometric_user` varchar(140),
`biometric_user_name` varchar(140),
`biometric` varchar(140),
`meal_type` varchar(140),
`site_name` varchar(140),
`supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:02,936 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140) unique,
`uid` varchar(140),
`erpnext_user` varchar(140),
`user_name` varchar(140),
`user_type` varchar(140),
`amended_from` varchar(140),
`gender` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:03,081 WARNING database DDL Query made to DB:
create table `tabCSF TZ Meal Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meal_name` varchar(140) unique,
`meal_type` varchar(140),
`start_time` time(6),
`end_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:03,284 WARNING database DDL Query made to DB:
create table `tabStanbic Setting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`enabled` int(1) not null default 1,
`currency` varchar(140),
`sftp_url` varchar(140),
`sftp_user` varchar(140),
`private_key` text,
`port` int(11) not null default 0,
`pgp_public_key` text,
`pgp_private_key` text,
`initiating_party_name` varchar(140),
`customerid` varchar(140),
`payment_type` varchar(140),
`user` varchar(140),
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140),
`file_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:03,461 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Info` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_slip` varchar(140),
`employee` varchar(140),
`transfer_currency` varchar(140),
`transfer_amount` decimal(21,9) not null default 0,
`beneficiary_bank_bic` varchar(140),
`beneficiary_bank_sort_code` varchar(140),
`beneficiary_bank_name` varchar(140),
`beneficiary_bank_country_code` varchar(140),
`beneficiary_name` varchar(140),
`beneficiary_country` varchar(140),
`beneficiary_address` varchar(140),
`beneficiary_iban` varchar(140),
`beneficiary_account_number` varchar(140),
`beneficiary_account_type` int(11) not null default 0,
`beneficiary_account_currency` varchar(140),
`stanbic_finaud_status` varchar(140),
`stanbic_intaud_status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:03,657 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Initiation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`file_code` varchar(140),
`amended_from` varchar(140),
`posting_date` date,
`posting_time` time(6),
`number_of_transactions` int(11) not null default 0,
`control_sum` decimal(21,9) not null default 0,
`stanbic_setting` varchar(140),
`initiating_party_name` varchar(140),
`customer_id` varchar(140),
`ordering_customer_name` varchar(140),
`ordering_customer_account_country` varchar(140) default 'TZ',
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140) default 'DEBT',
`xml` longtext,
`encrypted_xml` longtext,
`stanbic_ack` longtext,
`stanbic_intaud` longtext,
`stanbic_finaud` longtext,
`stanbic_ack_status` varchar(140),
`stanbic_ack_change` int(1) not null default 0,
`stanbic_intaud_change` int(1) not null default 0,
`stanbic_finaud_change` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:07,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-02-18 14:39:07,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-02-18 14:39:07,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-02-18 14:39:07,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-02-18 14:39:07,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-02-18 14:39:07,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-02-18 14:39:07,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-02-18 14:39:07,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0
2025-02-18 14:39:21,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_bank_details` text, ADD COLUMN `vrn` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `p_o_box` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `plot_number` varchar(140), ADD COLUMN `block_number` varchar(140), ADD COLUMN `street` varchar(140), ADD COLUMN `max_records_in_dialog` int(11) not null default 0, ADD COLUMN `default_withholding_payable_account` varchar(140), ADD COLUMN `auto_create_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `default_withholding_receivable_account` varchar(140), ADD COLUMN `auto_create_for_sales_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_sales_withholding` int(1) not null default 0, ADD COLUMN `bypass_material_request_validation` int(1) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1
2025-02-18 14:39:21,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-02-18 14:39:21,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `form_sales_invoice` varchar(140)
2025-02-18 14:39:21,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0
2025-02-18 14:39:22,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `previous_invoice_number` varchar(140), ADD COLUMN `default_item_discount` decimal(21,9) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `price_reduction` decimal(21,9) not null default 0, ADD COLUMN `tra_control_number` varchar(140), ADD COLUMN `witholding_tax_certificate_number` varchar(140), ADD COLUMN `electronic_fiscal_device` varchar(140), ADD COLUMN `efd_z_report` varchar(140), ADD COLUMN `excise_duty_applicable` int(1) not null default 0, ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1, ADD COLUMN `delivery_status` varchar(140)
2025-02-18 14:39:22,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 14:39:22,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-02-18 14:39:22,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0
2025-02-18 14:39:22,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `vrn` varchar(140)
2025-02-18 14:39:22,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-02-18 14:39:22,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `posting_date` date, ADD COLUMN `default_item_discount` decimal(21,9) not null default 0
2025-02-18 14:39:22,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-02-18 14:39:22,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-02-18 14:39:22,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-02-18 14:39:22,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140)
2025-02-18 14:39:22,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-02-18 14:39:22,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `posting_date` date, ADD COLUMN `start_date` date, ADD COLUMN `end_date` date
2025-02-18 14:39:22,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-02-18 14:39:22,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD COLUMN `spare_name` varchar(140), ADD COLUMN `quantity` decimal(21,9) not null default 0, ADD COLUMN `invoice` varchar(140)
2025-02-18 14:39:23,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-02-18 14:39:23,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `posting_date` date
2025-02-18 14:39:23,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-02-18 14:39:23,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140), ADD COLUMN `referance_doctype` varchar(140), ADD COLUMN `referance_docname` varchar(140), ADD COLUMN `from_date` date, ADD COLUMN `to_date` date
2025-02-18 14:39:23,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0
2025-02-18 14:39:23,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `material_request` varchar(140)
2025-02-18 14:39:23,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-02-18 14:39:23,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `old_employee_id` varchar(140), ADD COLUMN `heslb_f4_index_number` varchar(140)
2025-02-18 14:39:23,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-02-18 14:39:23,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `is_ignored_in_pending_qty` int(1) not null default 0, ADD COLUMN `allow_over_sell` int(1) not null default 0, ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140), ADD COLUMN `allow_override_net_rate` int(1) not null default 0, ADD COLUMN `delivery_status` varchar(140)
2025-02-18 14:39:23,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0
2025-02-18 14:39:23,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140)
2025-02-18 14:39:23,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 14:39:24,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom DocPerm` ADD COLUMN `dependent` int(1) not null default 0
2025-02-18 14:39:24,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `repack_template` varchar(140), ADD COLUMN `item_uom` varchar(140), ADD COLUMN `repack_qty` decimal(21,9) not null default 0, ADD COLUMN `final_destination` varchar(140), ADD COLUMN `total_net_weight` decimal(21,9) not null default 0, ADD COLUMN `transporter` varchar(140), ADD COLUMN `driver` varchar(140), ADD COLUMN `transport_receipt_no` varchar(140), ADD COLUMN `vehicle_no` varchar(140), ADD COLUMN `transporter_name` varchar(140), ADD COLUMN `driver_name` varchar(140), ADD COLUMN `transport_receipt_date` date
2025-02-18 14:39:24,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-02-18 14:39:24,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `trip_destination` varchar(140), ADD COLUMN `destination_description` varchar(140)
2025-02-18 14:39:24,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-02-18 14:39:24,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `vrn` varchar(140)
2025-02-18 14:39:24,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `weight_per_unit` decimal(21,9) not null default 0, ADD COLUMN `total_weight` decimal(21,9) not null default 0, ADD COLUMN `weight_uom` varchar(140)
2025-02-18 14:39:24,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-02-18 14:39:24,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-02-18 14:39:24,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-02-18 14:39:24,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) not null default 0
2025-02-18 14:39:24,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `csf_tz_year` int(11) not null default 0, ADD COLUMN `csf_tz_acquisition_odometer` int(11) not null default 0, ADD COLUMN `csf_tz_engine_number` varchar(140)
2025-02-18 14:39:24,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `uom` varchar(140), MODIFY `vehicle_value` decimal(21,9) not null default 0
2025-02-18 14:39:24,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` ADD COLUMN `image` text
2025-02-18 14:39:24,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-02-18 14:39:24,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `stock_reconciliation` varchar(140)
2025-02-18 14:39:25,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-02-18 14:39:25,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, ADD COLUMN `excisable_item` int(1) not null default 0, ADD COLUMN `default_tax_template` varchar(140)
2025-02-18 14:39:25,138 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0
2025-02-18 14:39:25,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `electronic_fiscal_device` varchar(140)
2025-02-18 14:39:25,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` ADD COLUMN `fully_paid` int(1) not null default 0
2025-02-18 14:39:25,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `charge` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 14:39:26,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-02-18 14:39:26,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-02-18 14:39:26,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-02-18 14:39:26,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-02-18 14:39:27,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-02-18 14:39:27,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-02-18 14:39:27,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-02-18 14:39:27,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 14:39:27,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:29,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-02-18 14:39:29,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-02-18 14:39:29,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-02-18 14:39:29,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-02-18 14:39:29,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-02-18 14:39:29,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-02-18 14:39:29,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:29,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-02-18 14:39:29,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0
2025-02-18 14:39:29,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:30,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employee_country` varchar(140), ADD COLUMN `employee_country_code` varchar(140), ADD COLUMN `beneficiary_bank_bic` varchar(140), ADD COLUMN `bank_country_code` varchar(140), ADD COLUMN `bank_country` varchar(140), ADD COLUMN `bank_account_name` varchar(140)
2025-02-18 14:39:30,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-02-18 14:39:31,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-02-18 14:39:31,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-02-18 14:39:31,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:31,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-02-18 14:39:31,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0
2025-02-18 14:39:32,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-02-18 14:39:32,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0
2025-02-18 14:39:32,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:32,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-02-18 14:39:32,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-02-18 14:39:32,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-02-18 14:39:32,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-02-18 14:39:32,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-02-18 14:39:33,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-02-18 14:39:33,299 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-02-18 14:39:33,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-02-18 14:39:33,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `applicable_charges` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-02-18 14:39:33,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-02-18 14:39:34,750 WARNING database DDL Query made to DB:
create table `tabCorridor Levy Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
index `country`(`country`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:35,075 WARNING database DDL Query made to DB:
create table `tabContainer Movement Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`manifest` varchar(140),
`company` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`size` varchar(140),
`container_count` varchar(140),
`cargo_type` varchar(140),
`ship` varchar(140),
`port` varchar(140),
`voyage_no` varchar(140),
`movement_date` date,
`ship_dc_date` date,
`icd_time_in` time(6),
`port_time_out` time(6),
`transporter` varchar(140),
`driver` varchar(140),
`driver_license` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver_signature` longtext,
`driver_time` time(6),
`gate_no_signature` longtext,
`gate_no_time` time(6),
`amended_from` varchar(140),
`naming_series` varchar(140) default 'ICD-CMO-.YYYY.-',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `manifest`(`manifest`),
index `m_bl_no`(`m_bl_no`),
index `container_no`(`container_no`),
index `container_count`(`container_count`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
index `ship_dc_date`(`ship_dc_date`),
index `truck`(`truck`),
index `trailer`(`trailer`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:35,292 WARNING database DDL Query made to DB:
create table `tabConsignee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consignee_name` varchar(140) unique,
`consignee_tin` varchar(140),
`consignee_tel` varchar(140),
`disabled` int(1) not null default 0,
`consignee_address` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:35,524 WARNING database DDL Query made to DB:
create table `tabGate Pass` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`manifest` varchar(140),
`sline` varchar(140),
`vessel_name` varchar(140),
`voyage_no` varchar(140),
`arrival_date` date,
`company` varchar(140),
`submitted_date` date,
`submitted_time` time(6),
`submitted_by` varchar(140),
`container_id` varchar(140),
`bl_no` varchar(140),
`container_no` varchar(140),
`size` varchar(140),
`seal_no` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`goods_description` text,
`transporter` varchar(140),
`naming_series` varchar(140) default 'ICD-GP-.YYYY.-',
`amended_from` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver` varchar(140),
`license_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `manifest`(`manifest`),
index `container_id`(`container_id`),
index `bl_no`(`bl_no`),
index `container_no`(`container_no`),
index `c_and_f_company`(`c_and_f_company`),
index `clearing_agent`(`clearing_agent`),
index `consignee`(`consignee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:35,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD COLUMN `m_bl_no` varchar(140), ADD COLUMN `type_of_container` varchar(140), ADD COLUMN `no_of_packages` varchar(140), ADD COLUMN `seal_no_1` varchar(140), ADD COLUMN `seal_no_2` varchar(140), ADD COLUMN `seal_no_3` varchar(140), ADD COLUMN `freight_indicator` varchar(140), ADD COLUMN `volume` int(11) not null default 0, ADD COLUMN `volume_unit` varchar(140), ADD COLUMN `weight` varchar(140), ADD COLUMN `weight_unit` varchar(140), ADD COLUMN `size` varchar(140), ADD COLUMN `cargo_type` varchar(140), ADD COLUMN `container_count` varchar(140), ADD COLUMN `consignee` varchar(140), ADD COLUMN `sline` varchar(140), ADD COLUMN `sline_code` varchar(140), ADD COLUMN `ship` varchar(140), ADD COLUMN `voyage_no` varchar(140), ADD COLUMN `port_of_loading` varchar(140), ADD COLUMN `port_of_destination` varchar(140), ADD COLUMN `abbr_for_destination` varchar(140), ADD COLUMN `place_of_delivery` varchar(140), ADD COLUMN `place_of_destination` varchar(140), ADD COLUMN `country_of_destination` varchar(140), ADD COLUMN `package_unit` text, ADD COLUMN `cargo_description` text, ADD COLUMN `arrival_date` date, ADD COLUMN `departure_date` date, ADD COLUMN `received_date` date, ADD COLUMN `booking_date` date, ADD COLUMN `last_inspection_date` date, ADD COLUMN `original_location` varchar(140), ADD COLUMN `current_location` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `customs_status` varchar(140), ADD COLUMN `naming_series` varchar(140), ADD COLUMN `manifest` varchar(140), ADD COLUMN `movement_order` varchar(140), ADD COLUMN `container_reception` varchar(140), ADD COLUMN `company` varchar(140), ADD COLUMN `total_days` int(11) not null default 0, ADD COLUMN `no_of_free_days` int(11) not null default 0, ADD COLUMN `no_of_writeoff_days` int(11) not null default 0, ADD COLUMN `no_of_billable_days` int(11) not null default 0, ADD COLUMN `days_to_be_billed` int(11) not null default 0, ADD COLUMN `no_of_billed_days` int(11) not null default 0, ADD COLUMN `has_removal_charges` varchar(140) default 'Yes', ADD COLUMN `r_sales_invoice` varchar(140), ADD COLUMN `has_corridor_levy_charges` varchar(140) default 'Yes', ADD COLUMN `c_sales_invoice` varchar(140), ADD COLUMN `has_single_charge` int(1) not null default 0, ADD COLUMN `has_double_charge` int(1) not null default 0
2025-02-18 14:39:35,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD UNIQUE INDEX IF NOT EXISTS container_no (`container_no`)
2025-02-18 14:39:35,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `received_date_index`(`received_date`)
2025-02-18 14:39:36,092 WARNING database DDL Query made to DB:
create table `tabService Order Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`remarks` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:36,266 WARNING database DDL Query made to DB:
create table `tabContainer Inspection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`in_yard_container_booking` varchar(140),
`driver_name` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`container_id` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`company` varchar(140),
`posting_datetime` datetime(6),
`current_container_location` varchar(140),
`inspector_name` varchar(140),
`inspection_results` text,
`inspection_date` datetime(6),
`inspection_comments` text,
`new_container_location` varchar(140),
`additional_note` text,
`amended_from` varchar(140),
`naming_series` varchar(140) default 'ICD-CI-.YYYY.-',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `m_bl_no`(`m_bl_no`),
index `container_no`(`container_no`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:36,432 WARNING database DDL Query made to DB:
create table `tabClearing and Forwarding Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`phone` varchar(140),
`email` varchar(140),
`license_no` varchar(140),
`tin` varchar(140),
`vrn` varchar(140),
`disabled` int(1) not null default 0,
`physical_address` text,
`person_name` varchar(140),
`title` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:36,595 WARNING database DDL Query made to DB:
create table `tabContainers Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`type_of_container` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`seal_no1` varchar(140),
`seal_no2` varchar(140),
`seal_no3` varchar(140),
`freight_indicator` varchar(140),
`no_of_packages` varchar(140),
`package_unit` text,
`volume` varchar(140),
`volume_unit` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`plug_type_of_reefer` varchar(140),
`minimum_temperature` varchar(140),
`maximum_temperature` varchar(140),
`has_order` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:36,800 WARNING database DDL Query made to DB:
create table `tabMasterBI` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`cargo_classification` varchar(140),
`bl_type` varchar(140),
`port_of_loading` varchar(140),
`place_of_destination` varchar(140),
`place_of_delivery` varchar(140),
`oil_type` varchar(140),
`number_of_containers` varchar(140),
`cargo_description` text,
`number_of_package` varchar(140),
`package_unit` text,
`gross_weight` varchar(140),
`gross_weight_unit` varchar(140),
`gross_volume` varchar(140),
`gross_volume_unit` varchar(140),
`invoice_value` varchar(140),
`invoice_currency` varchar(140),
`freight_charge` varchar(140),
`freight_currency` varchar(140),
`imdg_code` varchar(140),
`packing_type` varchar(140),
`shipping_agent_code` varchar(140),
`shipping_agent_name` varchar(140),
`forwarder_code` varchar(140),
`forwarder_name` varchar(140),
`forwarder_tel` varchar(140),
`exporter_name` varchar(140),
`exporter_tel` varchar(140),
`exporter_address` text,
`exporter_tin` varchar(140),
`cosignee_name` varchar(140),
`cosignee_tel` varchar(140),
`cosignee_address` text,
`cosignee_tin` varchar(140),
`notify_name` varchar(140),
`notify_tel` varchar(140),
`notify_address` text,
`notify_tin` varchar(140),
`shipping_mark` varchar(140),
`net_weight` varchar(140),
`net_weight_unit` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:36,945 WARNING database DDL Query made to DB:
create table `tabSecurity officer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140) unique,
`employee_id` varchar(140),
`disabled` int(1) not null default 0,
`email` varchar(140),
`phone_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:37,148 WARNING database DDL Query made to DB:
create table `tabICD TZ Settings Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`destination` varchar(140),
`charge` varchar(140),
`from` int(11) not null default 0,
`to` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:37,285 WARNING database DDL Query made to DB:
create table `tabContainer Inspection Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`tra_official` varchar(140),
`icd_official` varchar(140),
`clearing_agent` varchar(140),
`total_no_of_striping_and_stuffing` varchar(140),
`exam_status` varchar(140),
`new_seal_no` varchar(140),
`status_changed_to` varchar(140),
`volume` varchar(140),
`remarks` text,
`sales_invoice` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:37,473 WARNING database DDL Query made to DB:
create table `tabHouseBI` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`cargo_classification` varchar(140),
`place_of_destination` varchar(140),
`net_weight` decimal(21,9) not null default 0,
`net_weight_unit` varchar(140),
`number_of_containers` int(11) not null default 0,
`description_of_goods` text,
`number_of_package` int(11) not null default 0,
`package_unit` text,
`gross_weight` decimal(21,9) not null default 0,
`gross_weight_unit` varchar(140),
`gross_volume` decimal(21,9) not null default 0,
`gross_volume_unit` varchar(140),
`invoice_value` varchar(140),
`invoice_currency` varchar(140),
`freight_charge` decimal(21,9) not null default 0,
`freight_currency` varchar(140),
`imdg_code` varchar(140),
`packing_type` varchar(140),
`shipping_agent_code` varchar(140),
`shipping_agent_name` varchar(140),
`forwarder_code` varchar(140),
`forwarder_name` varchar(140),
`exporter_name` varchar(140),
`exporter_tel` varchar(140),
`exporter_address` text,
`exporter_tin` varchar(140),
`consignee_name` varchar(140),
`consignee_tel` varchar(140),
`consignee_address` text,
`consignee_tin` varchar(140),
`notify_name` varchar(140),
`notify_tel` varchar(140),
`notify_address` text,
`notify_tin` varchar(140),
`shipping_mark` varchar(140),
`oil_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:37,774 WARNING database DDL Query made to DB:
create table `tabContainer Reception` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`movement_order` varchar(140),
`manifest` varchar(140),
`company` varchar(140),
`ship` varchar(140),
`port` varchar(140),
`voyage_no` varchar(140),
`received_date` date,
`ship_dc_date` date,
`icd_time_in` time(6),
`port_time_out` time(6),
`has_transport_charges` varchar(140) default 'Yes',
`t_sales_invoice` varchar(140),
`has_shore_handling_charges` varchar(140) default 'Yes',
`s_sales_invoice` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`size` varchar(140),
`container_count` varchar(140),
`seal_no_2` varchar(140),
`volume` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`cargo_type` varchar(140),
`seal_no_1` varchar(140),
`seal_no_3` varchar(140),
`transporter` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver` varchar(140),
`driver_lisence` varchar(140),
`container_location` varchar(140),
`abbr_for_destination` varchar(140),
`place_of_destination` varchar(140),
`country_of_destination` varchar(140),
`clerk` varchar(140),
`cleck_name` varchar(140),
`security_officer` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `movement_order`(`movement_order`),
index `manifest`(`manifest`),
index `port`(`port`),
index `received_date`(`received_date`),
index `ship_dc_date`(`ship_dc_date`),
index `icd_time_in`(`icd_time_in`),
index `port_time_out`(`port_time_out`),
index `m_bl_no`(`m_bl_no`),
index `container_no`(`container_no`),
index `container_count`(`container_count`),
index `transporter`(`transporter`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:37,961 WARNING database DDL Query made to DB:
create table `tabHBIContainer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`type_of_container` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`seal_no1` varchar(140),
`seal_no2` varchar(140),
`seal_no3` varchar(140),
`freight_indicator` varchar(140),
`no_of_packages` varchar(140),
`package_unit` text,
`volume` varchar(140),
`volume_unit` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`plug_type_of_reefer` varchar(140),
`minimum_temperature` varchar(140),
`maximum_temperature` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:38,183 WARNING database DDL Query made to DB:
create table `tabService Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`container_inspection` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`company` varchar(140),
`posting_datetime` datetime(6),
`manifest` varchar(140),
`vessel_name` varchar(140),
`port` varchar(140),
`m_bl_no` varchar(140),
`place_of_destination` varchar(140),
`country_of_destination` varchar(140),
`container_id` varchar(140),
`container_no` varchar(140),
`container_seal` varchar(140),
`container_size` varchar(140),
`container_status` varchar(140),
`container_location` varchar(140),
`sales_order` varchar(140),
`sales_invoice` varchar(140),
`get_pass` varchar(140),
`naming_series` varchar(140) default 'ICD-SO-.YYYY.-',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `c_and_f_company`(`c_and_f_company`),
index `clearing_agent`(`clearing_agent`),
index `consignee`(`consignee`),
index `manifest`(`manifest`),
index `port`(`port`),
index `m_bl_no`(`m_bl_no`),
index `container_id`(`container_id`),
index `container_no`(`container_no`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:38,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` ADD COLUMN `document_type` varchar(140), ADD COLUMN `document_reference_id` varchar(140), ADD COLUMN `document_description` varchar(140), ADD COLUMN `attach_document` text
2025-02-18 14:39:38,548 WARNING database DDL Query made to DB:
create table `tabCondition state` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`reference` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:38,751 WARNING database DDL Query made to DB:
create table `tabContainer Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`is_group` int(1) not null default 0,
`old_parent` varchar(140),
`parent_container_location` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:38,971 WARNING database DDL Query made to DB:
create table `tabIn Yard Container Booking` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`posting_datetime` datetime(6),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`container_id` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`cargo_description` text,
`inspection_datetime` datetime(6),
`inspection_location` varchar(140),
`has_stripping_charges` varchar(140),
`s_sales_invoice` varchar(140),
`has_custom_verification_charges` varchar(140) default 'Yes',
`cv_sales_invoice` varchar(140),
`inspection_log` text,
`movement_log` text,
`notify_c_and_f_agent` int(1) not null default 0,
`notify_consignee` int(1) not null default 0,
`notification_method` varchar(140),
`container_inspection` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:39,125 WARNING database DDL Query made to DB:
create table `tabDocument Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:39,355 WARNING database DDL Query made to DB:
create table `tabManifest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`manifest` text,
`company` varchar(140),
`mrn` varchar(140),
`vessel_name` varchar(140),
`tpa_uid` varchar(140),
`voyage_no` varchar(140),
`arrival_date` date,
`port` varchar(140),
`call_sign` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140) default 'ICD-M-.YYYY.-',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `mrn`(`mrn`),
index `vessel_name`(`vessel_name`),
index `tpa_uid`(`tpa_uid`),
index `voyage_no`(`voyage_no`),
index `arrival_date`(`arrival_date`),
index `port`(`port`),
index `call_sign`(`call_sign`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:39,592 WARNING database DDL Query made to DB:
create table `tabContainer Service Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`is_billable` int(1) not null default 1,
`is_free` int(1) not null default 0,
`sales_invoice` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:39,754 WARNING database DDL Query made to DB:
create table `tabBooking detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`booking_id` varchar(140),
`booking_date` date,
`booking_time` time(6),
`container_number` varchar(140),
`cargo_description` text,
`c_and_f_agent` varchar(140),
`customer` varchar(140),
`inspection_location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:39,891 WARNING database DDL Query made to DB:
create table `tabContainer State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:40,077 WARNING database DDL Query made to DB:
create table `tabTransporter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`phone` varchar(140),
`email` varchar(140),
`tin` varchar(140),
`vrn` varchar(140),
`disabled` int(1) not null default 0,
`physical_address` text,
`person_name` varchar(140),
`title` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:40,345 WARNING database DDL Query made to DB:
create table `tabContainer Verification Movement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`movement_date` datetime(6),
`in_yard_container_booking` varchar(140),
`amended_from` varchar(140),
`driver_name` varchar(140),
`container` varchar(140),
`original_location` varchar(140),
`inspector_name` varchar(140),
`inspection_results` text,
`inspection_date` datetime(6),
`inspection_comments` text,
`yard_number` varchar(140),
`return_movement_date` date,
`row` varchar(140),
`position` varchar(140),
`additional_note` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:39:40,540 WARNING database DDL Query made to DB:
create table `tabClearing Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`c_and_f_company` varchar(140),
`tafer_id` varchar(140),
`agent_name` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`introduction_letter` text,
`disabled` int(1) not null default 0,
`other_details` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-02-18 14:40:25,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabHouseBI` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `freight_charge` decimal(21,9) not null default 0
2025-02-18 14:40:29,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabDriver` ADD COLUMN `vehicle_owner` varchar(140)
2025-02-18 14:40:29,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `vehicle_owner` varchar(140), ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `is_truck` int(1) not null default 0, ADD COLUMN `is_trailer` int(1) not null default 0
2025-02-18 14:40:29,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-02-18 14:40:30,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `consignee` varchar(140), ADD COLUMN `m_bl_no` varchar(140)
2025-02-18 14:40:30,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-02-18 14:40:31,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `consignee` varchar(140), ADD COLUMN `m_bl_no` varchar(140)
