2025-05-19 13:02:48,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:48,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:49,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:49,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:49,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-05-19 13:02:49,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:49,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `total_leave_days` decimal(21,9) not null default 0
2025-05-19 13:02:49,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:49,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-05-19 13:02:49,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:50,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-05-19 13:02:50,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:50,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0
2025-05-19 13:02:50,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:50,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0
2025-05-19 13:02:50,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:50,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:51,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-05-19 13:02:51,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:51,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:51,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `score` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-05-19 13:02:51,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:52,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:52,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:52,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-05-19 13:02:52,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:52,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-05-19 13:02:52,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:52,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:53,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:53,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:53,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `applicant_rating` decimal(3,2)
2025-05-19 13:02:53,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:53,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:53,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-05-19 13:02:53,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:54,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:54,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:54,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-05-19 13:02:54,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:54,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:54,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-05-19 13:02:54,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,312 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-05-19 13:02:55,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:55,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-05-19 13:02:55,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:56,125 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:56,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:56,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0, MODIFY `total_estimated_cost` decimal(21,9) not null default 0
2025-05-19 13:02:56,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:56,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:56,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-05-19 13:02:56,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:57,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:57,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `revised_ctc` decimal(21,9) not null default 0, MODIFY `current_ctc` decimal(21,9) not null default 0
2025-05-19 13:02:57,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:57,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0, MODIFY `max_leaves_allowed` decimal(21,9) not null default 0
2025-05-19 13:02:57,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:57,627 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-05-19 13:02:57,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:57,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:58,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:58,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:58,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:58,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_payable_amount` decimal(21,9) not null default 0, MODIFY `total_asset_recovery_cost` decimal(21,9) not null default 0, MODIFY `total_receivable_amount` decimal(21,9) not null default 0
2025-05-19 13:02:58,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:58,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-19 13:02:58,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:59,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-05-19 13:02:59,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:59,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:59,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-05-19 13:02:59,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:59,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:02:59,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-05-19 13:02:59,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:00,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-05-19 13:03:00,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:00,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-05-19 13:03:00,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:00,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-19 13:03:00,909 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:01,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-19 13:03:01,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:01,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:01,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-05-19 13:03:01,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:01,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `total_actual_amount` decimal(21,9) not null default 0, MODIFY `exemption_amount` decimal(21,9) not null default 0
2025-05-19 13:03:01,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:01,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:02,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-19 13:03:02,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:02,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-19 13:03:02,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:02,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `base` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `pension_rate` decimal(21,9) not null default 0, MODIFY `heslb_rate` decimal(21,9) not null default 0, MODIFY `sdl_rate` decimal(21,9) not null default 0, MODIFY `wcf_rate` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0
2025-05-19 13:03:02,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:02,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-05-19 13:03:02,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:03,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0
2025-05-19 13:03:03,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:03,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-05-19 13:03:03,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:03,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-05-19 13:03:03,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:04,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:04,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:04,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:04,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-05-19 13:03:04,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:05,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-05-19 13:03:05,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:05,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_declared_amount` decimal(21,9) not null default 0, MODIFY `total_exemption_amount` decimal(21,9) not null default 0
2025-05-19 13:03:05,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:05,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-05-19 13:03:05,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:05,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-05-19 13:03:05,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:05,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `percent` decimal(21,9) not null default 0, MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0
2025-05-19 13:03:05,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:06,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-19 13:03:06,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:06,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:06,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:06,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0
2025-05-19 13:03:06,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:07,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0
2025-05-19 13:03:07,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:07,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-19 13:03:07,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:07,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD COLUMN `tax_relief_limit` decimal(21,9) not null default 0
2025-05-19 13:03:07,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-05-19 13:03:07,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:07,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-19 13:03:07,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:08,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-05-19 13:03:08,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:08,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0
2025-05-19 13:03:08,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `creation`(`creation`)
2025-05-19 13:03:09,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 13:03:09,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 13:03:10,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-05-19 13:03:10,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`
2025-05-19 13:03:10,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-19 13:03:10,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-19 13:03:11,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 13:03:11,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 13:03:11,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-19 13:03:11,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-19 13:03:12,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Service Detail` ADD COLUMN `reefer_payment_reference` varchar(140)
2025-05-19 13:03:20,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 13:03:24,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-05-19 14:52:48,621 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-19 14:52:49,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-19 14:52:51,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-19 14:52:52,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-19 14:52:52,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-19 14:52:53,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 14:52:53,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 14:52:54,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-19 14:52:54,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`
2025-05-19 14:52:54,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-19 14:52:54,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-19 14:52:55,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 14:52:55,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-19 14:52:56,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-19 14:52:56,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-19 14:52:57,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `auto_repeat_end_date` date, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-05-19 14:53:01,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-05-20 16:02:03,274 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-20 16:02:04,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-20 16:02:05,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-20 16:02:06,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-20 16:02:06,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-20 16:02:07,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-20 16:02:07,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-20 16:02:08,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-20 16:02:08,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`
2025-05-20 16:02:08,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-20 16:02:08,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-20 16:02:09,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-20 16:02:09,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-20 16:02:09,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-20 16:02:09,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-20 16:02:11,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date
2025-05-20 16:02:14,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0
2025-05-20 16:05:59,436 WARNING database DDL Query made to DB:
alter table `tabContainer Service Detail` add column if not exists parent varchar(140)
2025-05-20 16:05:59,437 WARNING database DDL Query made to DB:
alter table `tabContainer Service Detail` add column if not exists parenttype varchar(140)
2025-05-20 16:05:59,438 WARNING database DDL Query made to DB:
alter table `tabContainer Service Detail` add column if not exists parentfield varchar(140)
2025-05-21 10:03:07,370 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-21 10:03:08,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-21 10:03:09,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-21 10:03:10,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-21 10:03:11,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-21 10:03:11,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 10:03:12,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 10:03:12,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-05-21 10:03:12,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`
2025-05-21 10:03:13,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-21 10:03:13,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-21 10:03:13,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 10:03:13,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 10:03:14,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-21 10:03:14,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-05-21 10:03:16,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-21 10:03:19,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-21 11:51:44,441 WARNING database DDL Query made to DB:
create table `tabLimit Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment_no` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`is_cash_inpatient` int(1) not null default 0,
`previous_cash_limit` decimal(21,9) not null default 0,
`current_total_deposit` decimal(21,9) not null default 0,
`inpatient_record` varchar(140),
`cash_limit` decimal(21,9) not null default 0,
`is_non_nhif_patient` int(1) not null default 0,
`previous_daily_limit` decimal(21,9) not null default 0,
`current_total_cost` decimal(21,9) not null default 0,
`insurance_company` varchar(140),
`daily_limit` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`naming_series` varchar(140),
`approved_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:44,684 WARNING database DDL Query made to DB:
create table `tabModality Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`modality_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:44,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `healthcare_practitioner_type` varchar(140), ADD COLUMN `supplier` varchar(140)
2025-05-21 11:51:44,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-05-21 11:51:45,023 WARNING database DDL Query made to DB:
create table `tabLab Machine Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`machine_make` varchar(140),
`machine_model` varchar(140),
`msh` int(11) not null default 0,
`obr` int(11) not null default 0,
`obx_nm_start` int(11) not null default 0,
`obx_nm_end` int(11) not null default 0,
`lab_test_prefix` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:45,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `total_service_unit_capacity` int(11) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality_name` varchar(140)
2025-05-21 11:51:45,329 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:45,450 WARNING database DDL Query made to DB:
create table `tabHealthcare Return Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:45,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `inpatient_record` varchar(140)
2025-05-21 11:51:45,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:51:45,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140), ADD COLUMN `admission_practitioner` varchar(140), ADD COLUMN `discharge_ordered_date` date, ADD COLUMN `discharge_date` date
2025-05-21 11:51:45,946 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_category_name` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `clinical_procedure` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `intent` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `priority` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-05-21 11:51:46,219 WARNING database DDL Query made to DB:
create table `tabMedication Class Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,331 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Contract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`default_price_list` varchar(140),
`is_active` int(1) not null default 0,
`start_date` date,
`end_date` date,
`apply_coverage_on_amount_with_tax` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,446 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,569 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,729 WARNING database DDL Query made to DB:
create table `tabEncounter Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`encounter_category` varchar(140) unique,
`default_healthcare_service_unit` varchar(140),
`default_healthcare_practitioner` varchar(140),
`default_appointment_type` varchar(140),
`encounter_fee` decimal(21,9) not null default 0,
`encounter_fee_item` varchar(140),
`create_sales_invoice` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:46,885 WARNING database DDL Query made to DB:
create table `tabRadiology Examination` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`prescribe` int(1) not null default 0,
`company` varchar(140),
`practitioner` varchar(140),
`radiology_examination_template` varchar(140),
`medical_department` varchar(140),
`service_unit` varchar(140),
`radiology_procedure_prescription` varchar(140),
`modality_type` varchar(140),
`modality` varchar(140),
`source` varchar(140) default 'Direct',
`referring_practitioner` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`start_date` date,
`start_time` time(6),
`notes` text,
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:47,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `staff_role` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `clinical_procedure_check_list_template` varchar(140)
2025-05-21 11:51:47,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:51:47,224 WARNING database DDL Query made to DB:
create table `tabMedication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`national_drug_code` varchar(140),
`medication_name` varchar(140),
`generic_name` varchar(140),
`medication_class` varchar(140),
`medication_category` varchar(140),
`is_generic` int(1) not null default 0,
`disabled` int(1) not null default 0,
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`abbr` varchar(140),
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`description` text,
`stock_uom` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`default_dosage_form` varchar(140),
`default_prescription_dosage` varchar(140),
`default_prescription_duration` varchar(140),
`default_interval` int(11) not null default 0,
`default_interval_uom` varchar(140),
`bypass_medication_class_interaction_check` int(1) not null default 1,
`bypass_allergy_check` int(1) not null default 1,
`bypass_medical_coding_check` int(1) not null default 1,
`reference_url` longtext,
`change_in_item` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:47,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `staff_role` varchar(140)
2025-05-21 11:51:47,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:51:47,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `triage` varchar(140), ADD COLUMN `color` varchar(140), ADD COLUMN `patient_referral` varchar(140), ADD COLUMN `radiology_examination_template` varchar(140), ADD COLUMN `radiology_procedure_prescription` varchar(140), ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality` varchar(140), ADD COLUMN `practitioner_availability` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-05-21 11:51:47,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-05-21 11:51:47,757 WARNING database DDL Query made to DB:
create table `tabMediciation Override Reason Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason_code` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:47,880 WARNING database DDL Query made to DB:
create table `tabHealthcare Notes Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`disabled` int(1) not null default 0,
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,049 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Subscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`is_active` int(1) not null default 1,
`patient` varchar(140),
`patient_name` varchar(140),
`mobile_number` varchar(140),
`customer` varchar(140),
`gender` varchar(140),
`country` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`subscription_end_date` date,
`healthcare_insurance_coverage_plan` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,175 WARNING database DDL Query made to DB:
create table `tabInsurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`insurance_coverage_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,270 WARNING database DDL Query made to DB:
create table `tabReferring Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `intent` varchar(140), ADD COLUMN `quantity` int(11) not null default 0, ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `expected_date` date, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `priority` varchar(140), ADD COLUMN `occurrence` datetime(6), ADD COLUMN `occurence_period` decimal(21,9), ADD COLUMN `note` text, ADD COLUMN `drug_prescription_created` int(1) not null default 0
2025-05-21 11:51:48,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-05-21 11:51:48,766 WARNING database DDL Query made to DB:
create table `tabInpatient Consultancy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`consultation_item` varchar(140),
`rate` decimal(21,9) not null default 0,
`delivery_note` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,872 WARNING database DDL Query made to DB:
create table `tabLab Bundle Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_template` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:48,971 WARNING database DDL Query made to DB:
create table `tabSample Collection Lab Test Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test` varchar(140),
`lab_test_tempate` varchar(140),
`test_abbr` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:49,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `allow_procedures` int(1) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140)
2025-05-21 11:51:49,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-05-21 11:51:49,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `intent` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `priority` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-05-21 11:51:49,417 WARNING database DDL Query made to DB:
create table `tabPatient Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`naming_series` varchar(140),
`triage` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Pending',
`date` date,
`time` time(6),
`referring_practitioner` varchar(140),
`priority` varchar(140),
`patient_encounter` varchar(140),
`referring_reason` varchar(140),
`referred_to_practitioner` varchar(140),
`referral_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:49,531 WARNING database DDL Query made to DB:
create table `tabItem Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`quantity` int(11) not null default 1,
`reason` varchar(140),
`encounter_no` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:49,632 WARNING database DDL Query made to DB:
create table `tabMedication Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`condition` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:49,755 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`customer` varchar(140),
`website` varchar(140),
`billed_but_not_claimed_account` varchar(140),
`approved_claim_receivable_account` varchar(140),
`rejected_claims_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:49,901 WARNING database DDL Query made to DB:
create table `tabMedication Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_category` varchar(140) unique,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`is_group` int(1) not null default 0,
`old_parent` varchar(140),
`parent_medication_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:50,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `staff_role` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140)
2025-05-21 11:51:50,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0
2025-05-21 11:51:50,259 WARNING database DDL Query made to DB:
create table `tabTriage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`triage_description` varchar(140) unique,
`category` varchar(140),
`attend_within` decimal(21,9),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:50,443 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` date,
`from_time` time(6),
`to_date` date,
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` int(1) not null default 0,
`tuesday` int(1) not null default 0,
`wednesday` int(1) not null default 0,
`thursday` int(1) not null default 0,
`friday` int(1) not null default 0,
`saturday` int(1) not null default 0,
`sunday` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:50,583 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`insurance_claim` varchar(140),
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`sales_invoice` varchar(140),
`discount` decimal(21,9) not null default 0,
`claim_coverage` decimal(21,9) not null default 0,
`claim_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:50,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` ADD COLUMN `estimated_duration` decimal(21,9), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-05-21 11:51:50,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` ADD UNIQUE INDEX IF NOT EXISTS diagnosis (`diagnosis`)
2025-05-21 11:51:50,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `triage` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-05-21 11:51:51,037 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_priority` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,210 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`mode_of_claim_approval` varchar(140),
`claim_posting_date` date,
`claim_status` varchar(140),
`approval_validity_end_date` date,
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`service_doctype` varchar(140),
`service_item` varchar(140),
`medical_code` varchar(140),
`price_list_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`coverage` decimal(21,9) not null default 0,
`coverage_amount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`sales_invoice_posting_date` date,
`billing_date` date,
`billing_amount` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,372 WARNING database DDL Query made to DB:
create table `tabTherapy Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`sessions_prescribed` int(11) not null default 0,
`sessions_cancelled` int(11) not null default 0,
`sessions_to_cancel` int(11) not null default 0,
`reason` varchar(140),
`encounter_no` varchar(140),
`therapy_plan` varchar(140),
`therapy_session` varchar(140),
`encounter_child_table_id` varchar(140),
`plan_child_table_id` varchar(140),
index `therapy_type`(`therapy_type`),
index `reason`(`reason`),
index `encounter_no`(`encounter_no`),
index `therapy_session`(`therapy_session`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,471 WARNING database DDL Query made to DB:
create table `tabHealthcare External Referrer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner_name` varchar(140) unique,
`mobile_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,635 WARNING database DDL Query made to DB:
create table `tabDiet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,750 WARNING database DDL Query made to DB:
create table `tabSales Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 0,
`quantity_returned` decimal(21,9) not null default 0,
`quantity_serviced` decimal(21,9) not null default 0,
`delivery_note_no` varchar(140),
`dn_detail` varchar(140),
`warehouse` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,852 WARNING database DDL Query made to DB:
create table `tabHealthcare Company Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`service_unit` varchar(140),
`is_not_available` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:51,963 WARNING database DDL Query made to DB:
create table `tabAllowed Price List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`price_list` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,072 WARNING database DDL Query made to DB:
create table `tabResult Component Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140),
`result_component_option` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,182 WARNING database DDL Query made to DB:
create table `tabMTUHA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`mtuha` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,304 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_name` varchar(140) unique,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,465 WARNING database DDL Query made to DB:
create table `tabEpisode of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140),
`type_of_episode` varchar(140),
`company` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140) default 'Planned',
`primary_practitioner` varchar(140),
`care_cordinator` varchar(140),
`initiated_by` varchar(140),
`initiated_via` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,605 WARNING database DDL Query made to DB:
create table `tabPatient Care Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient_care_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,752 WARNING database DDL Query made to DB:
create table `tabInsurance Service Frequency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_frequency_name` varchar(140) unique,
`service_frequency_count` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:52,891 WARNING database DDL Query made to DB:
create table `tabMedication Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 1.0,
`qty_returned` decimal(21,9) not null default 0,
`quantity_to_return` decimal(21,9) not null default 0,
`reason` varchar(140),
`drug_condition` varchar(140),
`encounter_no` varchar(140),
`delivery_note_no` varchar(140),
`status` varchar(140),
`dn_detail` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:53,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `is_main` int(1) not null default 0, ADD COLUMN `main_department` varchar(140)
2025-05-21 11:51:53,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD UNIQUE INDEX IF NOT EXISTS department (`department`)
2025-05-21 11:51:53,184 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`posting_date_type` varchar(140),
`from_date` date,
`to_date` date,
`total_claim_amount` decimal(21,9) not null default 0,
`is_finished` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:53,338 WARNING database DDL Query made to DB:
create table `tabInsurance Coverage Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_assignment` varchar(140),
`insurance_company` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice__posting_date` date,
`service` varchar(140),
`service_name` varchar(140),
`medical_code` varchar(140),
`mode_of_service__approval` varchar(140),
`service_approval_date` date,
`serviceapproval_reference` text,
`approval_validity_end_date` date,
`requested_quantity` decimal(21,9) not null default 0,
`approved_quantity` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`claim_posting_date` date,
`insurance_claim_coverage` decimal(21,9) not null default 0,
`insurance_claim_amount` decimal(21,9) not null default 0,
`request_status` varchar(140),
`claim_status` varchar(140),
`approved_amount` decimal(21,9) not null default 0,
`rejected_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:53,458 WARNING database DDL Query made to DB:
create table `tabAllergy Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:53,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `triage` varchar(140), ADD COLUMN `nhif_employername` varchar(140)
2025-05-21 11:51:53,813 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Insurance Coverage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`healthcare_service` varchar(140),
`healthcare_service_template` varchar(140),
`is_active` int(1) not null default 0,
`healthcare_insurance_coverage_plan` varchar(140),
`company` varchar(140),
`coverage` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`maximum_number_of_claims` int(11) not null default 0,
`approval_mandatory_for_claim` int(1) not null default 0,
`manual_approval_only` int(1) not null default 0,
`start_date` date,
`end_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:53,963 WARNING database DDL Query made to DB:
create table `tabHealthcare Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`task` varchar(140),
`task_order` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`service_unit` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`blood_group` varchar(140),
`gender` varchar(140),
`birth_date` date,
`description` text,
`date` date,
`time` time(6),
`inpatient_record` varchar(140),
`remarks` text,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,120 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Coverage Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`coverage_plan_name` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`is_active` int(1) not null default 0,
`hms_tz_has_nhif_coverage` int(1) not null default 0,
`is_exclusions` int(1) not null default 0,
`price_list` varchar(140),
`amended_from` varchar(140),
`nhif_employername` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,281 WARNING database DDL Query made to DB:
create table `tabResult Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-05-21 11:51:54,531 WARNING database DDL Query made to DB:
create table `tabRadiology Examination Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`procedure_name` varchar(140) unique,
`abbr` varchar(140) unique,
`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`description` text,
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`modality_type` varchar(140),
`healthcare_service_unit_type` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`accession_number` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,651 WARNING database DDL Query made to DB:
create table `tabAllergy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,761 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list` varchar(140),
`task` varchar(140),
`expected_time` int(11) not null default 0,
`nursing_task_reference` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:54,872 WARNING database DDL Query made to DB:
create table `tabLab Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_bundle_name` varchar(140) unique,
`public` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:55,006 WARNING database DDL Query made to DB:
create table `tabMedication Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140) unique,
`prescribed_after` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:55,144 WARNING database DDL Query made to DB:
create table `tabRadiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-21 11:51:55,252 WARNING database DDL Query made to DB:
create table `tabLab Machine Message` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date_and_time` datetime(6),
`machine_make` varchar(140),
`machine_model` varchar(140),
`lab_test_name` varchar(140),
`lab_test` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
