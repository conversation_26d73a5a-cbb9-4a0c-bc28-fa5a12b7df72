2025-04-19 11:30:50,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0
2025-04-19 11:30:50,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `creation`(`creation`)
2025-04-19 11:30:50,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension` ADD INDEX `document_type_index`(`document_type`)
2025-04-19 11:30:50,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` ADD COLUMN `gain_loss_posting_date` date
2025-04-19 11:30:50,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `unreconciled_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-04-19 11:30:50,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD COLUMN `base_outstanding` decimal(21,9) not null default 0, ADD COLUMN `base_paid_amount` decimal(21,9) not null default 0
2025-04-19 11:30:50,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding` decimal(21,9) not null default 0, MODIFY `base_payment_amount` decimal(21,9) not null default 0, MODIFY `invoice_portion` decimal(21,9) not null default 0, MODIFY `discount` decimal(21,9) not null default 0, MODIFY `payment_amount` decimal(21,9) not null default 0
2025-04-19 11:30:51,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD INDEX `creation`(`creation`)
2025-04-19 11:30:51,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `print_receipt_on_order_complete` int(1) not null default 0, ADD COLUMN `disable_grand_total_to_default_mop` int(1) not null default 0, ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-04-19 11:30:51,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-04-19 11:30:51,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) not null default 0
2025-04-19 11:30:51,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` ADD COLUMN `dont_enforce_free_item_qty` int(1) not null default 0
2025-04-19 11:30:51,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `recurse_for` decimal(21,9) not null default 0, MODIFY `min_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0, MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `max_qty` decimal(21,9) not null default 0
2025-04-19 11:30:52,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-04-19 11:30:52,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-04-19 11:30:53,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-04-19 11:30:53,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-04-19 11:30:53,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` ADD INDEX `creation`(`creation`)
2025-04-19 11:30:53,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` ADD COLUMN `subcontracted_quantity` decimal(21,9) not null default 0
2025-04-19 11:30:53,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-04-19 11:30:54,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` ADD COLUMN `hide_timesheets` int(1) not null default 0
2025-04-19 11:30:54,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-04-19 11:30:54,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD INDEX `project_index`(`project`)
2025-04-19 11:30:54,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `cost_center` varchar(140), ADD COLUMN `project` varchar(140)
2025-04-19 11:30:55,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-04-19 11:30:55,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD INDEX `project_index`(`project`)
2025-04-19 11:30:55,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 11:30:55,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-04-19 11:30:56,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `total_time_in_mins` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `time_required` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-04-19 11:30:56,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` ADD COLUMN `allow_alternative_item` int(1) not null default 1
2025-04-19 11:30:56,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Creator Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-04-19 11:30:56,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-04-19 11:30:56,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-04-19 11:30:56,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` ADD COLUMN `total_weight` decimal(21,9) not null default 0
2025-04-19 11:30:56,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` MODIFY `value_of_goods` decimal(21,9) not null default 0, MODIFY `pickup_to` time(6) default '17:00', MODIFY `pickup_from` time(6) default '09:00', MODIFY `shipment_amount` decimal(21,9) not null default 0
2025-04-19 11:30:57,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-04-19 11:30:57,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0
2025-04-19 11:30:57,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-04-19 11:30:57,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` ADD INDEX `default_warehouse_index`(`default_warehouse`)
2025-04-19 11:30:58,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-04-19 11:30:58,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-04-19 11:30:58,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0
2025-04-19 11:30:58,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0
2025-04-19 11:30:58,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD INDEX `parent_detail_docname_index`(`parent_detail_docname`)
2025-04-19 11:30:58,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `letter_head` varchar(140)
2025-04-19 11:30:58,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-04-19 11:30:59,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0
2025-04-19 11:30:59,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-04-19 11:30:59,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial and Batch Bundle` MODIFY `naming_series` varchar(140) default 'SABB-.########', MODIFY `avg_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-04-19 11:30:59,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` ADD COLUMN `has_corrective_cost` int(1) not null default 0
2025-04-19 11:30:59,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-04-19 11:31:00,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `sla_resolution_by` datetime(6), ADD COLUMN `sla_resolution_date` datetime(6)
2025-04-19 11:31:00,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `avg_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
2025-04-19 11:31:00,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` ADD COLUMN `difference_account` varchar(140)
2025-04-19 11:31:00,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `current_asset_value` decimal(21,9) not null default 0, MODIFY `new_asset_value` decimal(21,9) not null default 0
2025-04-19 11:31:00,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD COLUMN `purchase_receipt_item` varchar(140)
2025-04-19 11:31:00,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-04-19 11:31:00,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD INDEX `creation`(`creation`)
2025-04-19 11:31:01,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0
2025-04-19 11:31:01,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD COLUMN `subcontracting_conversion_factor` decimal(21,9) not null default 0, ADD COLUMN `job_card` varchar(140)
2025-04-19 11:31:01,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `service_cost_per_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rm_cost_per_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-04-19 11:31:01,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-04-19 11:31:02,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-19 11:31:02,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `pay_via_payment_entry` int(1) not null default 0, ADD COLUMN `expense_account` varchar(140), ADD COLUMN `payable_account` varchar(140), ADD COLUMN `posting_date` date, ADD COLUMN `paid_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `status` varchar(140)
2025-04-19 11:31:02,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-04-19 11:31:02,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `half_day_status` varchar(140)
2025-04-19 11:31:02,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-19 11:31:02,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD COLUMN `auto_update_last_sync` int(1) not null default 0
2025-04-19 11:31:02,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-04-19 11:31:03,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` MODIFY `goal_score_percentage` decimal(21,9) not null default 0, MODIFY `self_score` decimal(21,9) not null default 0, MODIFY `final_score` decimal(21,9) not null default 0, MODIFY `avg_feedback_score` decimal(21,9) not null default 0, MODIFY `total_score` decimal(21,9) not null default 0
2025-04-19 11:31:03,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 11:31:03,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0
2025-04-19 11:31:04,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 11:31:04,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-19 11:31:05,757 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-04-19 11:31:05,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `posting_datetime_creation_index`(posting_datetime, creation)
2025-04-19 11:31:09,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-04-19 11:33:01,499 WARNING database DDL Query made to DB:
create table `tabCSF TZ Permission Backup` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-19 11:33:35,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` ADD COLUMN `roles` varchar(140), ADD COLUMN `permissions` json
2025-04-19 11:34:11,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json, ADD UNIQUE INDEX IF NOT EXISTS roles (`roles`)
2025-04-19 11:36:03,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` ADD COLUMN `role` varchar(140) unique
2025-04-19 11:36:03,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json, ADD UNIQUE INDEX IF NOT EXISTS role (`role`)
2025-04-19 11:37:33,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json
2025-04-19 11:38:09,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json
2025-04-19 12:03:35,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json
2025-04-19 12:05:09,383 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-19 12:05:10,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-19 12:05:11,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 12:05:12,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 12:05:12,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-19 12:05:13,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Permission Backup` MODIFY `permissions` json
2025-04-19 12:05:14,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-04-19 12:05:14,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-19 12:05:14,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 12:05:18,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-04-22 20:52:29,659 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-22 20:52:30,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-22 20:52:32,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-22 20:52:33,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-22 20:52:34,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-22 20:52:35,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-04-22 20:52:36,089 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-22 20:52:36,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-22 20:52:37,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Movement Order` ADD COLUMN `status` varchar(140), ADD COLUMN `freight_indicator` varchar(140)
2025-04-22 20:52:37,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Movement Order` ADD INDEX `status_index`(`status`), ADD INDEX `freight_indicator_index`(`freight_indicator`)
2025-04-22 20:52:37,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabConsignee` ADD COLUMN `customer` varchar(140)
2025-04-22 20:52:37,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD COLUMN `action_for_missing_booking` varchar(140) default 'Stop', ADD COLUMN `missing_booking_allowed_by` varchar(140), ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `container_status` varchar(140), ADD COLUMN `tafer_id` varchar(140), ADD COLUMN `is_empty_container` int(1) not null default 0
2025-04-22 20:52:37,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabGate Pass` ADD INDEX `action_for_missing_booking_index`(`action_for_missing_booking`), ADD INDEX `h_bl_no_index`(`h_bl_no`), ADD INDEX `container_status_index`(`container_status`), ADD INDEX `tafer_id_index`(`tafer_id`), ADD INDEX `is_empty_container_index`(`is_empty_container`)
2025-04-22 20:52:37,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaster BL` ADD COLUMN `consignee_name` varchar(140), ADD COLUMN `consignee_tel` varchar(140), ADD COLUMN `consignee_address` text, ADD COLUMN `consignee_tin` varchar(140)
2025-04-22 20:52:37,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaster BL` ADD INDEX `consignee_name_index`(`consignee_name`), ADD INDEX `consignee_tel_index`(`consignee_tel`), ADD INDEX `consignee_tin_index`(`consignee_tin`)
2025-04-22 20:52:37,969 WARNING database DDL Query made to DB:
create table `tabICD TZ Service Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`size` varchar(140),
`cargo_type` varchar(140),
`port` varchar(140),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `size`(`size`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 20:52:38,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `plug_type_of_reefer` varchar(140), ADD COLUMN `maximum_temperature` varchar(140), ADD COLUMN `is_empty_container` int(1) not null default 0, ADD COLUMN `has_hbl` int(1) not null default 0, ADD COLUMN `minimum_temperature` varchar(140), ADD COLUMN `gross_volume` decimal(21,9) not null default 0, ADD COLUMN `gross_volume_unit` varchar(140), ADD COLUMN `gross_weight_unit` varchar(140), ADD COLUMN `net_weight_unit` varchar(140)
2025-04-22 20:52:38,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-04-22 20:52:38,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `m_bl_no_index`(`m_bl_no`), ADD INDEX `h_bl_no_index`(`h_bl_no`), ADD INDEX `container_no_index`(`container_no`), ADD INDEX `type_of_container_index`(`type_of_container`), ADD INDEX `seal_no_1_index`(`seal_no_1`), ADD INDEX `seal_no_2_index`(`seal_no_2`), ADD INDEX `seal_no_3_index`(`seal_no_3`), ADD INDEX `freight_indicator_index`(`freight_indicator`), ADD INDEX `plug_type_of_reefer_index`(`plug_type_of_reefer`), ADD INDEX `volume_index`(`volume`), ADD INDEX `volume_unit_index`(`volume_unit`), ADD INDEX `weight_index`(`weight`), ADD INDEX `weight_unit_index`(`weight_unit`), ADD INDEX `size_index`(`size`), ADD INDEX `cargo_type_index`(`cargo_type`), ADD INDEX `container_count_index`(`container_count`), ADD INDEX `is_empty_container_index`(`is_empty_container`), ADD INDEX `has_hbl_index`(`has_hbl`), ADD INDEX `minimum_temperature_index`(`minimum_temperature`), ADD INDEX `consignee_index`(`consignee`), ADD INDEX `sline_index`(`sline`), ADD INDEX `sline_code_index`(`sline_code`), ADD INDEX `ship_index`(`ship`), ADD INDEX `voyage_no_index`(`voyage_no`), ADD INDEX `port_of_loading_index`(`port_of_loading`), ADD INDEX `port_of_destination_index`(`port_of_destination`), ADD INDEX `place_of_delivery_index`(`place_of_delivery`), ADD INDEX `abbr_for_destination_index`(`abbr_for_destination`), ADD INDEX `place_of_destination_index`(`place_of_destination`), ADD INDEX `country_of_destination_index`(`country_of_destination`), ADD INDEX `gross_volume_index`(`gross_volume`), ADD INDEX `gross_volume_unit_index`(`gross_volume_unit`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `gross_weight_unit_index`(`gross_weight_unit`), ADD INDEX `net_weight_index`(`net_weight`), ADD INDEX `net_weight_unit_index`(`net_weight_unit`), ADD INDEX `arrival_date_index`(`arrival_date`), ADD INDEX `departure_date_index`(`departure_date`), ADD INDEX `booking_date_index`(`booking_date`), ADD INDEX `last_inspection_date_index`(`last_inspection_date`), ADD INDEX `original_location_index`(`original_location`), ADD INDEX `current_location_index`(`current_location`), ADD INDEX `status_index`(`status`), ADD INDEX `naming_series_index`(`naming_series`), ADD INDEX `manifest_index`(`manifest`), ADD INDEX `movement_order_index`(`movement_order`), ADD INDEX `container_reception_index`(`container_reception`), ADD INDEX `company_index`(`company`)
2025-04-22 20:52:38,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Order Detail` ADD COLUMN `qty` decimal(21,9) not null default 0
2025-04-22 20:52:38,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Order Detail` ADD INDEX `qty_index`(`qty`)
2025-04-22 20:52:39,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Inspection` ADD COLUMN `container_size` varchar(140), ADD COLUMN `h_bl_no` varchar(140)
2025-04-22 20:52:39,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Inspection` ADD INDEX `container_size_index`(`container_size`), ADD INDEX `container_id_index`(`container_id`), ADD INDEX `h_bl_no_index`(`h_bl_no`), ADD INDEX `company_index`(`company`), ADD INDEX `posting_datetime_index`(`posting_datetime`), ADD INDEX `current_container_location_index`(`current_container_location`), ADD INDEX `inspector_name_index`(`inspector_name`), ADD INDEX `inspection_date_index`(`inspection_date`)
2025-04-22 20:52:39,311 WARNING database DDL Query made to DB:
create table `tabICD TZ Loose Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`size` varchar(140),
`cargo_type` varchar(140),
`port` varchar(140),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `size`(`size`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-22 20:52:39,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Inspection Detail` ADD INDEX `status_changed_to_index`(`status_changed_to`), ADD INDEX `volume_index`(`volume`)
2025-04-22 20:52:39,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Reception` ADD COLUMN `freight_indicator` varchar(140)
2025-04-22 20:52:39,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Reception` ADD INDEX `freight_indicator_index`(`freight_indicator`)
2025-04-22 20:52:40,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Order` ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `gross_volume` decimal(21,9) not null default 0
2025-04-22 20:52:40,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Order` ADD INDEX `company_index`(`company`), ADD INDEX `posting_datetime_index`(`posting_datetime`), ADD INDEX `vessel_name_index`(`vessel_name`), ADD INDEX `place_of_destination_index`(`place_of_destination`), ADD INDEX `country_of_destination_index`(`country_of_destination`), ADD INDEX `h_bl_no_index`(`h_bl_no`), ADD INDEX `container_seal_index`(`container_seal`), ADD INDEX `container_size_index`(`container_size`), ADD INDEX `container_status_index`(`container_status`), ADD INDEX `container_location_index`(`container_location`), ADD INDEX `gross_volume_index`(`gross_volume`)
2025-04-22 20:52:40,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabIn Yard Container Booking` ADD COLUMN `h_bl_no` varchar(140)
2025-04-22 20:52:40,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabIn Yard Container Booking` ADD INDEX `company_index`(`company`), ADD INDEX `posting_datetime_index`(`posting_datetime`), ADD INDEX `c_and_f_company_index`(`c_and_f_company`), ADD INDEX `clearing_agent_index`(`clearing_agent`), ADD INDEX `consignee_index`(`consignee`), ADD INDEX `container_id_index`(`container_id`), ADD INDEX `m_bl_no_index`(`m_bl_no`), ADD INDEX `h_bl_no_index`(`h_bl_no`), ADD INDEX `container_no_index`(`container_no`), ADD INDEX `container_size_index`(`container_size`), ADD INDEX `inspection_date_index`(`inspection_date`), ADD INDEX `inspection_location_index`(`inspection_location`)
2025-04-22 20:52:44,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-04-22 20:52:45,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `c_and_f_company` varchar(140), ADD COLUMN `h_bl_no` varchar(140)
2025-04-22 20:52:45,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-04-22 20:52:45,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `c_and_f_company` varchar(140)
2025-04-22 20:52:45,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-04-24 10:20:51,980 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_735b0b6f43cd3001'@'localhost'
2025-04-24 10:21:04,620 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_735b0b6f43cd3001`
2025-04-24 10:21:04,626 WARNING database DDL Query made to DB:
CREATE USER '_735b0b6f43cd3001'@'localhost' IDENTIFIED BY 'PiooDemKkYUVnx5o'
2025-04-24 10:21:04,633 WARNING database DDL Query made to DB:
CREATE DATABASE `_735b0b6f43cd3001` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-04-24 10:21:51,241 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-04-24 10:21:51,258 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-04-24 10:23:06,957 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-24 10:29:42,054 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-24 10:29:42,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `protect_attached_files` int(1) not null default 0
2025-04-24 10:29:43,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-04-24 10:29:43,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-04-24 10:29:43,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-24 10:29:44,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0
2025-04-24 10:29:44,984 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-04-24 10:29:45,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-04-24 10:29:45,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-24 10:29:47,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0
2025-04-24 11:06:55,351 WARNING database DDL Query made to DB:
create table `tabHouse BL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`cargo_classification` varchar(140),
`place_of_destination` varchar(140),
`net_weight` decimal(21,9) not null default 0,
`net_weight_unit` varchar(140),
`number_of_containers` int(11) not null default 0,
`description_of_goods` text,
`number_of_package` int(11) not null default 0,
`package_unit` text,
`gross_weight` decimal(21,9) not null default 0,
`gross_weight_unit` varchar(140),
`gross_volume` decimal(21,9) not null default 0,
`gross_volume_unit` varchar(140),
`invoice_value` varchar(140),
`invoice_currency` varchar(140),
`freight_charge` decimal(21,9) not null default 0,
`freight_currency` varchar(140),
`imdg_code` varchar(140),
`packing_type` varchar(140),
`shipping_agent_code` varchar(140),
`shipping_agent_name` varchar(140),
`forwarder_code` varchar(140),
`forwarder_name` varchar(140),
`exporter_name` varchar(140),
`exporter_tel` varchar(140),
`exporter_address` text,
`exporter_tin` varchar(140),
`consignee_name` varchar(140),
`consignee_tel` varchar(140),
`consignee_address` text,
`consignee_tin` varchar(140),
`notify_name` varchar(140),
`notify_tel` varchar(140),
`notify_address` text,
`notify_tin` varchar(140),
`shipping_mark` varchar(140),
`oil_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:55,455 WARNING database DDL Query made to DB:
create table `tabCorridor Levy Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
index `country`(`country`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:55,619 WARNING database DDL Query made to DB:
create table `tabContainer Movement Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`manifest` varchar(140),
`company` varchar(140),
`status` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`freight_indicator` varchar(140),
`size` varchar(140),
`container_count` varchar(140),
`cargo_type` varchar(140),
`ship` varchar(140),
`port` varchar(140),
`voyage_no` varchar(140),
`movement_date` date,
`ship_dc_date` date,
`icd_time_in` time(6),
`port_time_out` time(6),
`transporter` varchar(140),
`driver` varchar(140),
`driver_license` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver_signature` longtext,
`driver_time` time(6),
`gate_no_signature` longtext,
`gate_no_time` time(6),
`amended_from` varchar(140),
`naming_series` varchar(140) default 'ICD-CMO-.YYYY.-',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `manifest`(`manifest`),
index `status`(`status`),
index `m_bl_no`(`m_bl_no`),
index `container_no`(`container_no`),
index `freight_indicator`(`freight_indicator`),
index `container_count`(`container_count`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
index `ship_dc_date`(`ship_dc_date`),
index `truck`(`truck`),
index `trailer`(`trailer`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:55,762 WARNING database DDL Query made to DB:
create table `tabConsignee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consignee_name` varchar(140) unique,
`consignee_tin` varchar(140),
`consignee_tel` varchar(140),
`disabled` int(1) not null default 0,
`consignee_address` text,
`customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:55,998 WARNING database DDL Query made to DB:
create table `tabGate Pass` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`manifest` varchar(140),
`vessel_name` varchar(140),
`voyage_no` varchar(140),
`ship_dc_date` date,
`company` varchar(140),
`submitted_date` date,
`submitted_time` time(6),
`submitted_by` varchar(140),
`action_for_missing_booking` varchar(140) default 'Stop',
`missing_booking_allowed_by` varchar(140),
`container_id` varchar(140),
`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`sline` varchar(140),
`container_no` varchar(140),
`size` varchar(140),
`seal_no` varchar(140),
`container_status` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`tafer_id` varchar(140),
`consignee` varchar(140),
`is_empty_container` int(1) not null default 0,
`goods_description` text,
`transporter` varchar(140),
`naming_series` varchar(140) default 'ICD-GP-.YYYY.-',
`amended_from` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver` varchar(140),
`license_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `manifest`(`manifest`),
index `action_for_missing_booking`(`action_for_missing_booking`),
index `container_id`(`container_id`),
index `m_bl_no`(`m_bl_no`),
index `h_bl_no`(`h_bl_no`),
index `container_no`(`container_no`),
index `container_status`(`container_status`),
index `c_and_f_company`(`c_and_f_company`),
index `clearing_agent`(`clearing_agent`),
index `tafer_id`(`tafer_id`),
index `consignee`(`consignee`),
index `is_empty_container`(`is_empty_container`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:56,174 WARNING database DDL Query made to DB:
create table `tabMaster BL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`cargo_classification` varchar(140),
`bl_type` varchar(140),
`port_of_loading` varchar(140),
`place_of_destination` varchar(140),
`place_of_delivery` varchar(140),
`oil_type` varchar(140),
`number_of_containers` varchar(140),
`cargo_description` text,
`number_of_package` varchar(140),
`package_unit` text,
`gross_weight` varchar(140),
`gross_weight_unit` varchar(140),
`gross_volume` varchar(140),
`gross_volume_unit` varchar(140),
`invoice_value` varchar(140),
`invoice_currency` varchar(140),
`freight_charge` varchar(140),
`freight_currency` varchar(140),
`imdg_code` varchar(140),
`packing_type` varchar(140),
`shipping_agent_code` varchar(140),
`shipping_agent_name` varchar(140),
`forwarder_code` varchar(140),
`forwarder_name` varchar(140),
`forwarder_tel` varchar(140),
`exporter_name` varchar(140),
`exporter_tel` varchar(140),
`exporter_address` text,
`exporter_tin` varchar(140),
`consignee_name` varchar(140),
`consignee_tel` varchar(140),
`consignee_address` text,
`consignee_tin` varchar(140),
`cosignee_name` varchar(140),
`cosignee_tel` varchar(140),
`cosignee_address` text,
`cosignee_tin` varchar(140),
`notify_name` varchar(140),
`notify_tel` varchar(140),
`notify_address` text,
`notify_tin` varchar(140),
`shipping_mark` text,
`net_weight` varchar(140),
`net_weight_unit` varchar(140),
index `consignee_name`(`consignee_name`),
index `consignee_tel`(`consignee_tel`),
index `consignee_tin`(`consignee_tin`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:56,288 WARNING database DDL Query made to DB:
create table `tabICD TZ Service Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`size` varchar(140),
`cargo_type` varchar(140),
`port` varchar(140),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `size`(`size`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:56,699 WARNING database DDL Query made to DB:
create table `tabContainer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`container_no` varchar(140),
`type_of_container` varchar(140),
`no_of_packages` varchar(140),
`seal_no_1` varchar(140),
`seal_no_2` varchar(140),
`seal_no_3` varchar(140),
`freight_indicator` varchar(140),
`plug_type_of_reefer` varchar(140),
`volume` int(11) not null default 0,
`volume_unit` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`maximum_temperature` varchar(140),
`size` varchar(140),
`cargo_type` varchar(140),
`container_count` varchar(140),
`is_empty_container` int(1) not null default 0,
`has_hbl` int(1) not null default 0,
`minimum_temperature` varchar(140),
`consignee` varchar(140),
`sline` varchar(140),
`sline_code` varchar(140),
`ship` varchar(140),
`voyage_no` varchar(140),
`port_of_loading` varchar(140),
`port_of_destination` varchar(140),
`place_of_delivery` varchar(140),
`abbr_for_destination` varchar(140),
`place_of_destination` varchar(140),
`country_of_destination` varchar(140),
`gross_volume` decimal(21,9) not null default 0,
`gross_volume_unit` varchar(140),
`gross_weight` decimal(21,9) not null default 0,
`gross_weight_unit` varchar(140),
`net_weight` decimal(21,9) not null default 0,
`net_weight_unit` varchar(140),
`package_unit` text,
`cargo_description` text,
`arrival_date` date,
`departure_date` date,
`received_date` date,
`booking_date` date,
`posting_date` date,
`last_inspection_date` date,
`original_location` varchar(140),
`current_location` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`manifest` varchar(140),
`movement_order` varchar(140),
`container_reception` varchar(140),
`company` varchar(140),
`total_days` int(11) not null default 0,
`no_of_free_days` int(11) not null default 0,
`no_of_writeoff_days` int(11) not null default 0,
`no_of_billable_days` int(11) not null default 0,
`days_to_be_billed` int(11) not null default 0,
`no_of_billed_days` int(11) not null default 0,
`has_removal_charges` varchar(140) default 'Yes',
`r_sales_invoice` varchar(140),
`has_corridor_levy_charges` varchar(140) default 'Yes',
`c_sales_invoice` varchar(140),
`has_single_charge` int(1) not null default 0,
`has_double_charge` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `m_bl_no`(`m_bl_no`),
index `h_bl_no`(`h_bl_no`),
index `container_no`(`container_no`),
index `type_of_container`(`type_of_container`),
index `seal_no_1`(`seal_no_1`),
index `seal_no_2`(`seal_no_2`),
index `seal_no_3`(`seal_no_3`),
index `freight_indicator`(`freight_indicator`),
index `plug_type_of_reefer`(`plug_type_of_reefer`),
index `volume`(`volume`),
index `volume_unit`(`volume_unit`),
index `weight`(`weight`),
index `weight_unit`(`weight_unit`),
index `size`(`size`),
index `cargo_type`(`cargo_type`),
index `container_count`(`container_count`),
index `is_empty_container`(`is_empty_container`),
index `has_hbl`(`has_hbl`),
index `minimum_temperature`(`minimum_temperature`),
index `consignee`(`consignee`),
index `sline`(`sline`),
index `sline_code`(`sline_code`),
index `ship`(`ship`),
index `voyage_no`(`voyage_no`),
index `port_of_loading`(`port_of_loading`),
index `port_of_destination`(`port_of_destination`),
index `place_of_delivery`(`place_of_delivery`),
index `abbr_for_destination`(`abbr_for_destination`),
index `place_of_destination`(`place_of_destination`),
index `country_of_destination`(`country_of_destination`),
index `gross_volume`(`gross_volume`),
index `gross_volume_unit`(`gross_volume_unit`),
index `gross_weight`(`gross_weight`),
index `gross_weight_unit`(`gross_weight_unit`),
index `net_weight`(`net_weight`),
index `net_weight_unit`(`net_weight_unit`),
index `arrival_date`(`arrival_date`),
index `departure_date`(`departure_date`),
index `received_date`(`received_date`),
index `booking_date`(`booking_date`),
index `posting_date`(`posting_date`),
index `last_inspection_date`(`last_inspection_date`),
index `original_location`(`original_location`),
index `current_location`(`current_location`),
index `status`(`status`),
index `naming_series`(`naming_series`),
index `manifest`(`manifest`),
index `movement_order`(`movement_order`),
index `container_reception`(`container_reception`),
index `company`(`company`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:56,781 WARNING database DDL Query made to DB:
create table `tabService Order Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`qty` decimal(21,9) not null default 0,
`remarks` text,
index `qty`(`qty`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:56,937 WARNING database DDL Query made to DB:
create table `tabContainer Inspection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`in_yard_container_booking` varchar(140),
`driver_name` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`container_size` varchar(140),
`container_id` varchar(140),
`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`container_no` varchar(140),
`company` varchar(140),
`posting_datetime` datetime(6),
`current_container_location` varchar(140),
`inspector_name` varchar(140),
`inspection_results` text,
`inspection_date` date,
`inspection_comments` text,
`new_container_location` varchar(140),
`additional_note` text,
`amended_from` varchar(140),
`naming_series` varchar(140) default 'ICD-CI-.YYYY.-',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `container_size`(`container_size`),
index `container_id`(`container_id`),
index `m_bl_no`(`m_bl_no`),
index `h_bl_no`(`h_bl_no`),
index `container_no`(`container_no`),
index `company`(`company`),
index `posting_datetime`(`posting_datetime`),
index `current_container_location`(`current_container_location`),
index `inspector_name`(`inspector_name`),
index `inspection_date`(`inspection_date`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,106 WARNING database DDL Query made to DB:
create table `tabClearing and Forwarding Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`phone` varchar(140),
`email` varchar(140),
`license_no` varchar(140),
`tin` varchar(140),
`vrn` varchar(140),
`disabled` int(1) not null default 0,
`physical_address` text,
`person_name` varchar(140),
`title` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,240 WARNING database DDL Query made to DB:
create table `tabContainers Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`type_of_container` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`seal_no1` varchar(140),
`seal_no2` varchar(140),
`seal_no3` varchar(140),
`freight_indicator` varchar(140),
`no_of_packages` varchar(140),
`package_unit` text,
`volume` varchar(140),
`volume_unit` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`plug_type_of_reefer` varchar(140),
`minimum_temperature` varchar(140),
`maximum_temperature` varchar(140),
`has_order` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,354 WARNING database DDL Query made to DB:
create table `tabSecurity officer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`full_name` varchar(140) unique,
`employee_id` varchar(140),
`disabled` int(1) not null default 0,
`email` varchar(140),
`phone_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,457 WARNING database DDL Query made to DB:
create table `tabICD TZ Loose Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`size` varchar(140),
`cargo_type` varchar(140),
`port` varchar(140),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `size`(`size`),
index `cargo_type`(`cargo_type`),
index `port`(`port`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,553 WARNING database DDL Query made to DB:
create table `tabICD TZ Settings Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`destination` varchar(140),
`charge` varchar(140),
`from` int(11) not null default 0,
`to` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,655 WARNING database DDL Query made to DB:
create table `tabContainer Inspection Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service` varchar(140),
`tra_official` varchar(140),
`icd_official` varchar(140),
`clearing_agent` varchar(140),
`total_no_of_striping_and_stuffing` varchar(140),
`exam_status` varchar(140),
`new_seal_no` varchar(140),
`status_changed_to` varchar(140),
`volume` varchar(140),
`remarks` text,
`sales_invoice` varchar(140),
index `status_changed_to`(`status_changed_to`),
index `volume`(`volume`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:57,894 WARNING database DDL Query made to DB:
create table `tabContainer Reception` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`movement_order` varchar(140),
`manifest` varchar(140),
`company` varchar(140),
`ship` varchar(140),
`port` varchar(140),
`voyage_no` varchar(140),
`received_date` date,
`ship_dc_date` date,
`posting_date` date,
`icd_time_in` time(6),
`port_time_out` time(6),
`has_transport_charges` varchar(140) default 'Yes',
`t_sales_invoice` varchar(140),
`has_shore_handling_charges` varchar(140) default 'Yes',
`s_sales_invoice` varchar(140),
`m_bl_no` varchar(140),
`container_no` varchar(140),
`size` varchar(140),
`container_count` varchar(140),
`seal_no_2` varchar(140),
`volume` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`cargo_type` varchar(140),
`seal_no_1` varchar(140),
`seal_no_3` varchar(140),
`freight_indicator` varchar(140),
`transporter` varchar(140),
`truck` varchar(140),
`trailer` varchar(140),
`driver` varchar(140),
`driver_lisence` varchar(140),
`container_location` varchar(140),
`abbr_for_destination` varchar(140),
`place_of_destination` varchar(140),
`country_of_destination` varchar(140),
`clerk` varchar(140),
`cleck_name` varchar(140),
`security_officer` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `movement_order`(`movement_order`),
index `manifest`(`manifest`),
index `port`(`port`),
index `received_date`(`received_date`),
index `ship_dc_date`(`ship_dc_date`),
index `posting_date`(`posting_date`),
index `icd_time_in`(`icd_time_in`),
index `port_time_out`(`port_time_out`),
index `m_bl_no`(`m_bl_no`),
index `container_no`(`container_no`),
index `container_count`(`container_count`),
index `freight_indicator`(`freight_indicator`),
index `transporter`(`transporter`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:58,152 WARNING database DDL Query made to DB:
create table `tabService Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`posting_datetime` datetime(6),
`manifest` varchar(140),
`vessel_name` varchar(140),
`port` varchar(140),
`container_id` varchar(140),
`place_of_destination` varchar(140),
`country_of_destination` varchar(140),
`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`container_no` varchar(140),
`container_seal` varchar(140),
`container_size` varchar(140),
`container_status` varchar(140),
`container_location` varchar(140),
`gross_volume` decimal(21,9) not null default 0,
`sales_order` varchar(140),
`sales_invoice` varchar(140),
`get_pass` varchar(140),
`naming_series` varchar(140) default 'ICD-SO-.YYYY.-',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `c_and_f_company`(`c_and_f_company`),
index `clearing_agent`(`clearing_agent`),
index `consignee`(`consignee`),
index `posting_datetime`(`posting_datetime`),
index `manifest`(`manifest`),
index `vessel_name`(`vessel_name`),
index `port`(`port`),
index `container_id`(`container_id`),
index `place_of_destination`(`place_of_destination`),
index `country_of_destination`(`country_of_destination`),
index `m_bl_no`(`m_bl_no`),
index `h_bl_no`(`h_bl_no`),
index `container_no`(`container_no`),
index `container_seal`(`container_seal`),
index `container_size`(`container_size`),
index `container_status`(`container_status`),
index `container_location`(`container_location`),
index `gross_volume`(`gross_volume`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:58,309 WARNING database DDL Query made to DB:
create table `tabDocument Attachment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_reference_id` varchar(140),
`document_description` varchar(140),
`attach_document` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:58,387 WARNING database DDL Query made to DB:
create table `tabCondition state` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140),
`reference` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:58,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Location` ADD COLUMN `lft` int(11) not null default 0, ADD COLUMN `rgt` int(11) not null default 0, ADD COLUMN `is_group` int(1) not null default 0, ADD COLUMN `old_parent` varchar(140), ADD COLUMN `parent_container_location` varchar(140)
2025-04-24 11:06:58,711 WARNING database DDL Query made to DB:
create table `tabIn Yard Container Booking` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`posting_datetime` datetime(6),
`c_and_f_company` varchar(140),
`clearing_agent` varchar(140),
`consignee` varchar(140),
`container_id` varchar(140),
`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`cargo_description` text,
`inspection_date` date,
`inspection_location` varchar(140),
`has_stripping_charges` varchar(140),
`s_sales_invoice` varchar(140),
`has_custom_verification_charges` varchar(140) default 'Yes',
`cv_sales_invoice` varchar(140),
`inspection_log` text,
`movement_log` text,
`notify_c_and_f_agent` int(1) not null default 0,
`notify_consignee` int(1) not null default 0,
`notification_method` varchar(140),
`container_inspection` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `posting_datetime`(`posting_datetime`),
index `c_and_f_company`(`c_and_f_company`),
index `clearing_agent`(`clearing_agent`),
index `consignee`(`consignee`),
index `container_id`(`container_id`),
index `m_bl_no`(`m_bl_no`),
index `h_bl_no`(`h_bl_no`),
index `container_no`(`container_no`),
index `container_size`(`container_size`),
index `inspection_date`(`inspection_date`),
index `inspection_location`(`inspection_location`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:58,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Type` ADD COLUMN `document_type_name` varchar(140) unique
2025-04-24 11:06:58,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Type` ADD UNIQUE INDEX IF NOT EXISTS document_type_name (`document_type_name`)
2025-04-24 11:06:58,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabManifest` ADD COLUMN `manifest` text, ADD COLUMN `company` varchar(140), ADD COLUMN `mrn` varchar(140), ADD COLUMN `vessel_name` varchar(140), ADD COLUMN `call_sign` varchar(140), ADD COLUMN `voyage_no` varchar(140), ADD COLUMN `tpa_uid` varchar(140), ADD COLUMN `arrival_date` date, ADD COLUMN `port` varchar(140), ADD COLUMN `naming_series` varchar(140) default 'ICD-M-.YYYY.-'
2025-04-24 11:06:59,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabManifest` ADD INDEX `mrn_index`(`mrn`), ADD INDEX `vessel_name_index`(`vessel_name`), ADD INDEX `call_sign_index`(`call_sign`), ADD INDEX `voyage_no_index`(`voyage_no`), ADD INDEX `tpa_uid_index`(`tpa_uid`), ADD INDEX `arrival_date_index`(`arrival_date`), ADD INDEX `port_index`(`port`), ADD INDEX `amended_from_index`(`amended_from`)
2025-04-24 11:06:59,238 WARNING database DDL Query made to DB:
create table `tabContainer Service Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`is_billable` int(1) not null default 1,
`is_free` int(1) not null default 0,
`sales_invoice` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:59,329 WARNING database DDL Query made to DB:
create table `tabBooking detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`booking_id` varchar(140),
`booking_date` date,
`booking_time` time(6),
`container_number` varchar(140),
`cargo_description` text,
`c_and_f_agent` varchar(140),
`customer` varchar(140),
`inspection_location` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:59,456 WARNING database DDL Query made to DB:
create table `tabHBL Container` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`m_bl_no` varchar(140),
`h_bl_no` varchar(140),
`type_of_container` varchar(140),
`container_no` varchar(140),
`container_size` varchar(140),
`seal_no1` varchar(140),
`seal_no2` varchar(140),
`seal_no3` varchar(140),
`freight_indicator` varchar(140),
`no_of_packages` varchar(140),
`package_unit` text,
`volume` varchar(140),
`volume_unit` varchar(140),
`weight` varchar(140),
`weight_unit` varchar(140),
`plug_type_of_reefer` varchar(140),
`minimum_temperature` varchar(140),
`maximum_temperature` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:59,542 WARNING database DDL Query made to DB:
create table `tabContainer State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`state` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:59,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransporter` ADD COLUMN `phone` varchar(140), ADD COLUMN `email` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `vrn` varchar(140), ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `physical_address` text, ADD COLUMN `person_name` varchar(140), ADD COLUMN `title` varchar(140), ADD COLUMN `mobile_no` varchar(140), ADD COLUMN `email_id` varchar(140)
2025-04-24 11:06:59,762 WARNING database DDL Query made to DB:
create table `tabContainer Verification Movement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`movement_date` datetime(6),
`in_yard_container_booking` varchar(140),
`amended_from` varchar(140),
`driver_name` varchar(140),
`container` varchar(140),
`original_location` varchar(140),
`inspector_name` varchar(140),
`inspection_results` text,
`inspection_date` datetime(6),
`inspection_comments` text,
`yard_number` varchar(140),
`return_movement_date` date,
`row` varchar(140),
`position` varchar(140),
`additional_note` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-24 11:06:59,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Agent` ADD COLUMN `c_and_f_company` varchar(140), ADD COLUMN `tafer_id` varchar(140), ADD COLUMN `agent_name` varchar(140), ADD COLUMN `email` varchar(140), ADD COLUMN `phone` varchar(140), ADD COLUMN `introduction_letter` text, ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `other_details` text
2025-04-24 11:07:01,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `vehicle_owner` varchar(140), ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `is_truck` int(1) not null default 0, ADD COLUMN `is_trailer` int(1) not null default 0
2025-04-24 11:07:01,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `uom` varchar(140), MODIFY `vehicle_value` decimal(21,9) not null default 0
2025-04-24 11:07:01,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabDriver` ADD COLUMN `vehicle_owner` varchar(140)
2025-04-24 11:07:01,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `c_and_f_company` varchar(140), ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `consignee` varchar(140), ADD COLUMN `m_bl_no` varchar(140)
2025-04-24 11:07:01,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-04-24 11:07:02,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `consignee` varchar(140), ADD COLUMN `m_bl_no` varchar(140), ADD COLUMN `h_bl_no` varchar(140), ADD COLUMN `c_and_f_company` varchar(140)
