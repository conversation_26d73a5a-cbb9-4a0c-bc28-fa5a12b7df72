2025-06-02 10:37:59,077 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`mode_of_claim_approval` varchar(140),
`claim_posting_date` date,
`claim_status` varchar(140),
`approval_validity_end_date` date,
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`service_doctype` varchar(140),
`service_item` varchar(140),
`medical_code` varchar(140),
`price_list_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`coverage` decimal(21,9) not null default 0,
`coverage_amount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`sales_invoice_posting_date` date,
`billing_date` date,
`billing_amount` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,229 WARNING database DDL Query made to DB:
create table `tabTherapy Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`sessions_prescribed` int(11) not null default 0,
`sessions_cancelled` int(11) not null default 0,
`sessions_to_cancel` int(11) not null default 0,
`reason` varchar(140),
`encounter_no` varchar(140),
`therapy_plan` varchar(140),
`therapy_session` varchar(140),
`encounter_child_table_id` varchar(140),
`plan_child_table_id` varchar(140),
index `therapy_type`(`therapy_type`),
index `reason`(`reason`),
index `encounter_no`(`encounter_no`),
index `therapy_session`(`therapy_session`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,330 WARNING database DDL Query made to DB:
create table `tabDiet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,447 WARNING database DDL Query made to DB:
create table `tabSales Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 0,
`quantity_returned` decimal(21,9) not null default 0,
`quantity_serviced` decimal(21,9) not null default 0,
`delivery_note_no` varchar(140),
`dn_detail` varchar(140),
`warehouse` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,544 WARNING database DDL Query made to DB:
create table `tabHealthcare Company Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`service_unit` varchar(140),
`is_not_available` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,647 WARNING database DDL Query made to DB:
create table `tabAllowed Price List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`price_list` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,750 WARNING database DDL Query made to DB:
create table `tabResult Component Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140),
`result_component_option` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,874 WARNING database DDL Query made to DB:
create table `tabMTUHA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`mtuha` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:59,995 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_name` varchar(140) unique,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:00,156 WARNING database DDL Query made to DB:
create table `tabEpisode of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140),
`type_of_episode` varchar(140),
`company` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140) default 'Planned',
`primary_practitioner` varchar(140),
`care_cordinator` varchar(140),
`initiated_by` varchar(140),
`initiated_via` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:00,491 WARNING database DDL Query made to DB:
create table `tabInsurance Service Frequency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_frequency_name` varchar(140) unique,
`service_frequency_count` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:00,695 WARNING database DDL Query made to DB:
create table `tabMedication Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 1.0,
`qty_returned` decimal(21,9) not null default 0,
`quantity_to_return` decimal(21,9) not null default 0,
`reason` varchar(140),
`drug_condition` varchar(140),
`encounter_no` varchar(140),
`delivery_note_no` varchar(140),
`status` varchar(140),
`dn_detail` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:00,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD COLUMN `is_main` int(1) not null default 0, ADD COLUMN `main_department` varchar(140)
2025-06-02 10:38:01,076 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`posting_date_type` varchar(140),
`from_date` date,
`to_date` date,
`total_claim_amount` decimal(21,9) not null default 0,
`is_finished` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:01,235 WARNING database DDL Query made to DB:
create table `tabInsurance Coverage Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_assignment` varchar(140),
`insurance_company` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice__posting_date` date,
`service` varchar(140),
`service_name` varchar(140),
`medical_code` varchar(140),
`mode_of_service__approval` varchar(140),
`service_approval_date` date,
`serviceapproval_reference` text,
`approval_validity_end_date` date,
`requested_quantity` decimal(21,9) not null default 0,
`approved_quantity` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`claim_posting_date` date,
`insurance_claim_coverage` decimal(21,9) not null default 0,
`insurance_claim_amount` decimal(21,9) not null default 0,
`request_status` varchar(140),
`claim_status` varchar(140),
`approved_amount` decimal(21,9) not null default 0,
`rejected_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:01,362 WARNING database DDL Query made to DB:
create table `tabAllergy Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:01,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `triage` varchar(140), ADD COLUMN `nhif_employername` varchar(140)
2025-06-02 10:38:01,796 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Insurance Coverage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`healthcare_service` varchar(140),
`healthcare_service_template` varchar(140),
`is_active` int(1) not null default 0,
`healthcare_insurance_coverage_plan` varchar(140),
`company` varchar(140),
`has_copayment` int(1) not null default 0,
`approval_mandatory_for_claim` int(1) not null default 0,
`dosage` varchar(140),
`strength` varchar(140),
`maximum_quantity` int(11) not null default 0,
`maximum_quantity_outpatient` int(11) not null default 0,
`maximum_quantity_inpatient` int(11) not null default 0,
`is_auto_generated` int(1) not null default 0,
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `healthcare_service`(`healthcare_service`),
index `healthcare_service_template`(`healthcare_service_template`),
index `is_active`(`is_active`),
index `healthcare_insurance_coverage_plan`(`healthcare_insurance_coverage_plan`),
index `company`(`company`),
index `has_copayment`(`has_copayment`),
index `dosage`(`dosage`),
index `strength`(`strength`),
index `maximum_quantity`(`maximum_quantity`),
index `maximum_quantity_outpatient`(`maximum_quantity_outpatient`),
index `maximum_quantity_inpatient`(`maximum_quantity_inpatient`),
index `is_auto_generated`(`is_auto_generated`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:01,952 WARNING database DDL Query made to DB:
create table `tabHealthcare Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`task` varchar(140),
`task_order` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`service_unit` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`blood_group` varchar(140),
`gender` varchar(140),
`birth_date` date,
`description` text,
`date` date,
`time` time(6),
`inpatient_record` varchar(140),
`remarks` text,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:02,107 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Coverage Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`coverage_plan_name` varchar(140) unique,
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`is_active` int(1) not null default 0,
`hms_tz_has_nhif_coverage` int(1) not null default 0,
`is_exclusions` int(1) not null default 0,
`has_followup_charges` int(1) not null default 0,
`has_fasttrack_charges` int(1) not null default 0,
`price_list` varchar(140),
`secondary_price_list` varchar(140),
`nhif_scheme_id` varchar(140),
`nhif_employername` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `has_followup_charges`(`has_followup_charges`),
index `has_fasttrack_charges`(`has_fasttrack_charges`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:02,275 WARNING database DDL Query made to DB:
create table `tabHealthcare Ward Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ward_type_name` varchar(140) unique,
`ward_type_id` int(11) not null default 0,
`item_code` varchar(140),
`notification_required_after` int(11) not null default 0,
`alias` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ward_type_id`(`ward_type_id`),
index `item_code`(`item_code`),
index `notification_required_after`(`notification_required_after`),
index `alias`(`alias`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:02,388 WARNING database DDL Query made to DB:
create table `tabResult Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:02,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-02 10:38:02,841 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`encounter` varchar(140),
`referral_type` varchar(140),
`posting_date` datetime(6),
`appointment` varchar(140),
`patient` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`authorization_no` varchar(140),
`dob` date,
`attendance_date` date,
`patient_type_code` varchar(140),
`mobile_no` varchar(140),
`gender` varchar(140),
`insurance_company` varchar(140),
`source_facility` varchar(140),
`source_facility_code` varchar(140),
`referrer_facility` varchar(140),
`referrer_facility_code` varchar(140),
`referral_status` varchar(140),
`referral_date` datetime(6),
`practitioner` varchar(140),
`practitioner_no` varchar(140),
`reason_for_referral` longtext,
`referral_no` varchar(140),
`referral_id` varchar(140),
`naming_series` varchar(140),
`referral_submitted_by` varchar(140),
`referral_updated_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `encounter`(`encounter`),
index `referral_type`(`referral_type`),
index `posting_date`(`posting_date`),
index `appointment`(`appointment`),
index `patient`(`patient`),
index `first_name`(`first_name`),
index `last_name`(`last_name`),
index `patient_name`(`patient_name`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `authorization_no`(`authorization_no`),
index `dob`(`dob`),
index `attendance_date`(`attendance_date`),
index `patient_type_code`(`patient_type_code`),
index `mobile_no`(`mobile_no`),
index `insurance_company`(`insurance_company`),
index `source_facility`(`source_facility`),
index `source_facility_code`(`source_facility_code`),
index `referrer_facility`(`referrer_facility`),
index `referrer_facility_code`(`referrer_facility_code`),
index `referral_status`(`referral_status`),
index `referral_date`(`referral_date`),
index `practitioner`(`practitioner`),
index `practitioner_no`(`practitioner_no`),
index `referral_no`(`referral_no`),
index `referral_id`(`referral_id`),
index `referral_submitted_by`(`referral_submitted_by`),
index `referral_updated_by`(`referral_updated_by`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:02,983 WARNING database DDL Query made to DB:
create table `tabHealthcare Room Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`room_type_name` varchar(140) unique,
`alias` varchar(140),
`room_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `alias`(`alias`),
index `room_type_id`(`room_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,144 WARNING database DDL Query made to DB:
create table `tabRadiology Examination Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`procedure_name` varchar(140) unique,
`abbr` varchar(140) unique,
`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`description` text,
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`modality_type` varchar(140),
`healthcare_service_unit_type` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`accession_number` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,312 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`qty` int(11) not null default 0,
`approval_ref_no` varchar(140),
`notes` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `qty`(`qty`),
index `approval_ref_no`(`approval_ref_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,442 WARNING database DDL Query made to DB:
create table `tabAllergy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,565 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list` varchar(140),
`task` varchar(140),
`expected_time` int(11) not null default 0,
`nursing_task_reference` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,688 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`disease_code` varchar(140),
`description` text,
index `status`(`status`),
index `disease_code`(`disease_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,819 WARNING database DDL Query made to DB:
create table `tabLab Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_bundle_name` varchar(140) unique,
`public` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:03,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication Class` ADD COLUMN `prescribed_after` int(11) not null default 0
2025-06-02 10:38:04,136 WARNING database DDL Query made to DB:
create table `tabRadiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:04,469 WARNING database DDL Query made to DB:
create table `tabHospital Revenue Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`appointment` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`posting_date` varchar(140),
`company` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`price_list` varchar(140),
`currency` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`qty_returned` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`is_cancelled` int(1) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`sales_invoice` varchar(140),
`healthcare_practitioner` varchar(140),
`healthcare_service_unit` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`created_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `customer`(`customer`),
index `appointment`(`appointment`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `posting_date`(`posting_date`),
index `company`(`company`),
index `payment_type`(`payment_type`),
index `mode_of_payment`(`mode_of_payment`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `price_list`(`price_list`),
index `currency`(`currency`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `is_cancelled`(`is_cancelled`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `sales_invoice`(`sales_invoice`),
index `healthcare_practitioner`(`healthcare_practitioner`),
index `healthcare_service_unit`(`healthcare_service_unit`),
index `department`(`department`),
index `cost_center`(`cost_center`),
index `created_by`(`created_by`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:04,591 WARNING database DDL Query made to DB:
create table `tabLab Machine Message` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date_and_time` datetime(6),
`machine_make` varchar(140),
`machine_model` varchar(140),
`lab_test_name` varchar(140),
`lab_test` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:04,731 WARNING database DDL Query made to DB:
create table `tabLRPMT Returns` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`company` varchar(140),
`inpatient_record` varchar(140),
`status` varchar(140),
`admitted_datetime` datetime(6),
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:04,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `lab_test` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `note` text
2025-06-02 10:38:05,083 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`status` varchar(140) default 'Draft',
`order_date` date,
`ordered_by` varchar(140),
`order_group` varchar(140),
`replaces` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`order_doctype` varchar(140),
`order` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`order_description` text,
`intent` varchar(140),
`priority` varchar(140),
`reason` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`body_part` varchar(140),
`staff_role` varchar(140),
`healthcare_service_unit_type` varchar(140),
`note` text,
`patient_instruction` text,
`source` varchar(140),
`referring_practitioner` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:05,228 WARNING database DDL Query made to DB:
create table `tabDrug Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:06,165 WARNING database DDL Query made to DB:
create table `tabNHIF Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`product_id` varchar(140) unique,
`product_name` varchar(140),
`healthcare_insurance_coverage_plan` varchar(140),
`nhif_product_code` varchar(140),
`schemeid` varchar(140),
`productdescription` varchar(140),
`highestorderwithoutreferral` int(11) not null default 0,
`company` varchar(140),
`maximumadmissiondays` varchar(140),
`requiresnationalid` int(1) not null default 0,
`usespolicy` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `product_name`(`product_name`),
index `healthcare_insurance_coverage_plan`(`healthcare_insurance_coverage_plan`),
index `nhif_product_code`(`nhif_product_code`),
index `schemeid`(`schemeid`),
index `productdescription`(`productdescription`),
index `highestorderwithoutreferral`(`highestorderwithoutreferral`),
index `company`(`company`),
index `maximumadmissiondays`(`maximumadmissiondays`),
index `requiresnationalid`(`requiresnationalid`),
index `usespolicy`(`usespolicy`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:06,427 WARNING database DDL Query made to DB:
create table `tabHealthcare Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`package_image` text,
`description` longtext,
`price_list` varchar(140),
`total_actual_item_price` decimal(21,9) not null default 0,
`total_price_of_services` decimal(21,9) not null default 0,
`price_of_package` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:06,593 WARNING database DDL Query made to DB:
create table `tabPrevious Radiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:06,736 WARNING database DDL Query made to DB:
create table `tabCompany NHIF Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`username` varchar(140),
`password` text,
`facility_code` varchar(140),
`enable` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhifservice_url` varchar(140),
`nhifservice_token` text,
`nhifservice_expiry` datetime(6),
`claimsserver_url` varchar(140),
`claimsserver_token` text,
`claimsserver_expiry` datetime(6),
`nhifform_url` varchar(140),
`nhifform_expiry` datetime(6),
`nhifform_token` text,
`update_patient_history` int(1) not null default 1,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:06,864 WARNING database DDL Query made to DB:
create table `tabNHIF Facility Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_code` varchar(140) unique,
`facility_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,046 WARNING database DDL Query made to DB:
create table `tabNHIF Co-Payment Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`itemcode` varchar(140),
`scheduleitemid` int(11) not null default 0,
`schemeid` varchar(140),
`yearno` int(11) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `itemcode`(`itemcode`),
index `scheduleitemid`(`scheduleitemid`),
index `schemeid`(`schemeid`),
index `yearno`(`yearno`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,166 WARNING database DDL Query made to DB:
create table `tabVC Cash Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,314 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billno` varchar(140),
`foliono` varchar(140),
`datesubmitted` datetime(6),
`cardno` varchar(140),
`authorizationno` varchar(140),
`amountclaimed` decimal(21,9) not null default 0,
`submissionid` varchar(140),
`submissionno` varchar(140),
`remarks` text,
index `billno`(`billno`),
index `foliono`(`foliono`),
index `cardno`(`cardno`),
index `authorizationno`(`authorizationno`),
index `amountclaimed`(`amountclaimed`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,434 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card_type_id` varchar(140),
`card_type_name` varchar(140),
index `card_type_id`(`card_type_id`),
index `card_type_name`(`card_type_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,539 WARNING database DDL Query made to DB:
create table `tabVC Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healathcare_practitioner` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:07,932 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient_appointment` varchar(140),
`company` varchar(140),
`posting_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`telephone_no` varchar(140),
`date_of_birth` date,
`gender` varchar(140),
`cardno` varchar(140),
`authorization_no` varchar(140),
`coverage_plan_name` varchar(140),
`inpatient_record` varchar(140),
`allow_changes` int(1) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`patient_signature` longtext,
`is_ready_for_auto_submission` int(1) not null default 0,
`reviewed_by` varchar(140),
`confirmation_code_sent` int(1) not null default 0,
`confirmation_code` varchar(140),
`receipt_no` varchar(140),
`facility_code` varchar(140),
`claim_year` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`serial_no` varchar(140),
`practitioner_name` varchar(140),
`practitioner_no` text,
`patient_file_no` varchar(140),
`patient_type_code` varchar(140),
`attendance_date` date,
`attendance_time` time(6),
`item_crt_by` varchar(140),
`date_admitted` date,
`admitted_time` time(6),
`date_discharge` date,
`discharge_time` time(6),
`delayreason` text,
`patient_file` longtext,
`claim_file` longtext,
`clinical_notes` longtext,
`submission_id` varchar(140),
`hashcode` varchar(140),
`naming_series` varchar(140),
`submission_no` varchar(140),
`submission_channel` varchar(140),
`amended_from` varchar(140),
`date_submitted` datetime(6),
`submission_remarks` text,
`hms_tz_claim_appointment_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_appointment`(`patient_appointment`),
index `company`(`company`),
index `posting_date`(`posting_date`),
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `first_name`(`first_name`),
index `last_name`(`last_name`),
index `telephone_no`(`telephone_no`),
index `date_of_birth`(`date_of_birth`),
index `gender`(`gender`),
index `cardno`(`cardno`),
index `authorization_no`(`authorization_no`),
index `coverage_plan_name`(`coverage_plan_name`),
index `inpatient_record`(`inpatient_record`),
index `allow_changes`(`allow_changes`),
index `total_amount`(`total_amount`),
index `is_ready_for_auto_submission`(`is_ready_for_auto_submission`),
index `reviewed_by`(`reviewed_by`),
index `confirmation_code_sent`(`confirmation_code_sent`),
index `confirmation_code`(`confirmation_code`),
index `receipt_no`(`receipt_no`),
index `facility_code`(`facility_code`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `folio_no`(`folio_no`),
index `serial_no`(`serial_no`),
index `practitioner_name`(`practitioner_name`),
index `patient_type_code`(`patient_type_code`),
index `attendance_date`(`attendance_date`),
index `attendance_time`(`attendance_time`),
index `item_crt_by`(`item_crt_by`),
index `date_admitted`(`date_admitted`),
index `admitted_time`(`admitted_time`),
index `date_discharge`(`date_discharge`),
index `discharge_time`(`discharge_time`),
index `submission_id`(`submission_id`),
index `hashcode`(`hashcode`),
index `naming_series`(`naming_series`),
index `submission_no`(`submission_no`),
index `submission_channel`(`submission_channel`),
index `date_submitted`(`date_submitted`),
index `hms_tz_claim_appointment_list`(`hms_tz_claim_appointment_list`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:08,338 WARNING database DDL Query made to DB:
create table `tabOriginal Delivery Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`hms_tz_is_out_of_stock` int(1) not null default 0,
`barcode` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`grant_commission` int(1) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`against_sales_order` varchar(140),
`so_detail` varchar(140),
`against_sales_invoice` varchar(140),
`si_detail` varchar(140),
`batch_no` varchar(140),
`serial_no` text,
`actual_batch_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`installed_qty` decimal(21,9) not null default 0,
`item_tax_rate` text,
`expense_account` varchar(140),
`allow_zero_valuation_rate` int(1) not null default 0,
`healthcare_service_unit` varchar(140),
`healthcare_practitioner` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`original_item` varchar(140),
`original_stock_uom_qty` decimal(21,9) not null default 0,
`is_restricted` int(1) not null default 0,
`approval_number` varchar(140),
`approval_type` varchar(140),
`last_date_prescribed` date,
`last_qty_prescribed` decimal(21,9) not null default 0,
`recommended_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `so_detail`(`so_detail`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:08,452 WARNING database DDL Query made to DB:
create table `tabNHIF Custom Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`time_stamp` datetime(6),
`facilitycode` varchar(140),
`title` varchar(140),
`item` varchar(140),
`itemcode` varchar(140),
`excludedforscheme` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:08,611 WARNING database DDL Query made to DB:
create table `tabPrevious Lab Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`lab_test_name` varchar(140),
`invoiced` int(1) not null default 0,
`lab_test` varchar(140),
`lab_test_comment` text,
`lab_test_created` int(1) not null default 0,
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `lab_test_created`(`lab_test_created`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:08,779 WARNING database DDL Query made to DB:
create table `tabNHIF Monthly Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` datetime(6),
`folio_submitted` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`acknowledgement_no` varchar(140),
`date_submitted` datetime(6),
`submitted_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `status`(`status`),
index `posting_date`(`posting_date`),
index `folio_submitted`(`folio_submitted`),
index `total_amount_claimed`(`total_amount_claimed`),
index `acknowledgement_no`(`acknowledgement_no`),
index `date_submitted`(`date_submitted`),
index `submitted_by`(`submitted_by`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:08,895 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_type` varchar(140),
`healthcare_service` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,020 WARNING database DDL Query made to DB:
create table `tabHealthcare Points of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`point_of_care_name` varchar(140) unique,
`point_of_care_id` varchar(140),
`point_of_care_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `point_of_care_id`(`point_of_care_id`),
index `point_of_care_code`(`point_of_care_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,138 WARNING database DDL Query made to DB:
create table `tabNHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facilitycode` varchar(140),
`log_name` varchar(140),
`time_stamp` datetime(6),
`company` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,264 WARNING database DDL Query made to DB:
create table `tabNHIF Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type_name` varchar(140) unique,
`service_type_id` varchar(140),
`require_nhif_number` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type_id`(`service_type_id`),
index `require_nhif_number`(`require_nhif_number`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,394 WARNING database DDL Query made to DB:
create table `tabNHIF Scheme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheme_id` varchar(140) unique,
`scheme_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `scheme_name`(`scheme_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,498 WARNING database DDL Query made to DB:
create table `tabVC Insurance Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`coverage_plan` varchar(140),
`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,598 WARNING database DDL Query made to DB:
create table `tabVC Excluded Service Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`healthcare_service` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,798 WARNING database DDL Query made to DB:
create table `tabNHIF Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`itemcode` varchar(140),
`itemtypeid` int(11) not null default 0,
`itemname` varchar(140),
`subgroup` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`isactive` int(1) not null default 0,
`isrestricted` int(1) not null default 0,
`calculatedperday` varchar(140),
`servicetypeid` int(11) not null default 0,
`serviceinterval` varchar(140),
`typeofinterval` varchar(140),
`waitingperiod` int(11) not null default 0,
`typeofperiod` varchar(140),
`eligibility` varchar(140),
`commonprice` decimal(21,9) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`availableinlevels` varchar(140),
`practitionerqualifications` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `itemname`(`itemname`),
index `subgroup`(`subgroup`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `isactive`(`isactive`),
index `isrestricted`(`isrestricted`),
index `calculatedperday`(`calculatedperday`),
index `servicetypeid`(`servicetypeid`),
index `serviceinterval`(`serviceinterval`),
index `typeofinterval`(`typeofinterval`),
index `waitingperiod`(`waitingperiod`),
index `typeofperiod`(`typeofperiod`),
index `eligibility`(`eligibility`),
index `commonprice`(`commonprice`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:09,916 WARNING database DDL Query made to DB:
create table `tabNHIF Physician Qualification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140) unique,
`qualification` varchar(140),
`physicianqualificationid` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,043 WARNING database DDL Query made to DB:
create table `tabVisiting Comission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`valid_from` date,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,184 WARNING database DDL Query made to DB:
create table `tabNHIF Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`current_log` varchar(140),
`previous_log` varchar(140),
`company` varchar(140),
`user_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `timestamp`(`timestamp`),
index `current_log`(`current_log`),
index `company`(`company`),
index `user_id`(`user_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,302 WARNING database DDL Query made to DB:
create table `tabPatient Discount Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_category` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`actual_price` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`amount_after_discount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`si_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,406 WARNING database DDL Query made to DB:
create table `tabOrgan System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,557 WARNING database DDL Query made to DB:
create table `tabPatient Discount Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`insurance_subscription` varchar(140),
`insurance_coverage_plan` varchar(140),
`insurance_company` varchar(140),
`payment_type` varchar(140),
`apply_discount_on` varchar(140),
`item_category` varchar(140),
`appointment` varchar(140),
`sales_invoice` varchar(140),
`discount_criteria` varchar(140),
`discount_percent` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`total_actual_amount` decimal(21,9) not null default 0,
`total_discounted_amount` decimal(21,9) not null default 0,
`total_amount_after_discount` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`discount_reason` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,704 WARNING database DDL Query made to DB:
create table `tabHMS TZ Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`facility_code` varchar(140),
`nhif_user` varchar(140),
`nhif_client_secret` text,
`nhif_grant_type` varchar(140),
`nhif_scope` varchar(140),
`enable_nhif_api` int(1) not null default 0,
`check_patient_info_on_his` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhif_claim_url` varchar(140),
`nhifservice_url` varchar(140),
`nhif_token_url` varchar(140),
`nhif_token_expiry` datetime(6),
`nhif_token` text,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,812 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:10,920 WARNING database DDL Query made to DB:
create table `tabPrevious Diet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`medical_code` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,106 WARNING database DDL Query made to DB:
create table `tabNHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_stamp` datetime(6),
`log_name` varchar(140),
`company` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`itemtypeid` int(11) not null default 0,
`pricecode` varchar(140),
`itemname` text,
`schemeid` varchar(140),
`packageid` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`unitprice` decimal(21,9) not null default 0,
`maximumquantity` varchar(140),
`maximumquantityoutpatient` varchar(140),
`maximumquantityinpatient` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `pricecode`(`pricecode`),
index `schemeid`(`schemeid`),
index `packageid`(`packageid`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `unitprice`(`unitprice`),
index `maximumquantity`(`maximumquantity`),
index `maximumquantityoutpatient`(`maximumquantityoutpatient`),
index `maximumquantityinpatient`(`maximumquantityinpatient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,247 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner_availability` varchar(140),
`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` datetime(6),
`from_time` time(6),
`to_date` datetime(6),
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,411 WARNING database DDL Query made to DB:
create table `tabNHIF Folio Counter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`posting_date` datetime(6),
`claim_month` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,556 WARNING database DDL Query made to DB:
create table `tabNHIF Tracking Claim Change` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`claim_year` int(11) not null default 0,
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`status` varchar(140),
`previous_amount` decimal(21,9) not null default 0,
`current_amount` decimal(21,9) not null default 0,
`amount_changed` decimal(21,9) not null default 0,
`nhif_patient_claim` varchar(140),
`lrpmt_return` varchar(140),
`patient_appointment` varchar(140),
`medication_change_request` varchar(140),
`patient_encounter` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user_email` varchar(140),
`edited_by` varchar(140),
`comment` text,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,682 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`mct_code` varchar(140),
index `practitioner`(`practitioner`),
index `mct_code`(`mct_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,809 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`date_created` datetime(6),
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:11,933 WARNING database DDL Query made to DB:
create table `tabOriginal NHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`date_created` datetime(6),
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,103 WARNING database DDL Query made to DB:
create table `tabNHIF Response Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140),
`timestamp` datetime(6),
`company` varchar(140),
`naming_series` varchar(140),
`request_type` varchar(140),
`request_url` varchar(1000),
`status_code` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`card_no` varchar(140),
`authorization_no` varchar(140),
`request_header` text,
`request_body` longtext,
`response_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user_id`(`user_id`),
index `timestamp`(`timestamp`),
index `request_type`(`request_type`),
index `request_url`(`request_url`),
index `status_code`(`status_code`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `card_no`(`card_no`),
index `authorization_no`(`authorization_no`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,266 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` date,
`number_of_submitted_claims` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`erp_number_of_submitted_claims` int(11) not null default 0,
`erp_total_amount_claimed` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `status`(`status`),
index `posting_date`(`posting_date`),
index `number_of_submitted_claims`(`number_of_submitted_claims`),
index `total_amount_claimed`(`total_amount_claimed`),
index `erp_number_of_submitted_claims`(`erp_number_of_submitted_claims`),
index `erp_total_amount_claimed`(`erp_total_amount_claimed`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,405 WARNING database DDL Query made to DB:
create table `tabPrevious Therapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`no_of_sessions` int(11) not null default 0,
`sessions_completed` int(11) not null default 0,
`department_hsu` varchar(140),
`comment` text,
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 1.0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,554 WARNING database DDL Query made to DB:
create table `tabPrevious Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`clinical_procedure` varchar(140),
`department_hsu` varchar(140),
`override_insurance_subscription` int(1) not null default 0,
`hso_payment_method` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,706 WARNING database DDL Query made to DB:
create table `tabChronic Medications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,863 WARNING database DDL Query made to DB:
create table `tabPrevious Drug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`healthcare_service_unit` varchar(140),
`is_restricted` int(1) not null default 0,
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
`drug_prescription_created` int(1) not null default 0,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:12,997 WARNING database DDL Query made to DB:
create table `tabMedication Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`hms_tz_comment` text,
`company` varchar(140),
`patient_encounter` varchar(140),
`delivery_note` varchar(140),
`sales_order` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `delivery_note`(`delivery_note`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,116 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`consultation_fee` decimal(21,9) not null default 0,
`appointment` varchar(140),
`encounter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,315 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`service_type` varchar(140),
`service_name` varchar(140),
`itemname` varchar(140),
`itemcode` varchar(140),
`itemtypeid` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`schemeid` varchar(140),
`packageid` varchar(140),
`pricecode` varchar(140),
`unitprice` decimal(21,9) not null default 0,
`isrestricted` int(1) not null default 0,
`hascopayment` int(1) not null default 0,
`maximumquantity` varchar(140),
`maximumquantityoutpatient` varchar(140),
`maximumquantityinpatient` varchar(140),
`fields_changed` longtext,
`previous_item` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `schemeid`(`schemeid`),
index `packageid`(`packageid`),
index `pricecode`(`pricecode`),
index `unitprice`(`unitprice`),
index `isrestricted`(`isrestricted`),
index `hascopayment`(`hascopayment`),
index `maximumquantity`(`maximumquantity`),
index `maximumquantityoutpatient`(`maximumquantityoutpatient`),
index `maximumquantityinpatient`(`maximumquantityinpatient`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,428 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`record` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,542 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Disease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis_type` varchar(140),
`status` varchar(140),
`medical_code` varchar(140),
`disease_code` varchar(140),
`description` varchar(140),
`patient_encounter` varchar(140),
`codification_table` varchar(140),
`item_crt_by` varchar(140),
`date_created` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,678 WARNING database DDL Query made to DB:
create table `tabNHIF Item Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_type_id` int(11) not null default 0 unique,
`type_name` varchar(140),
`alias` varchar(140),
`item_group` varchar(140),
`display_item` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `type_name`(`type_name`),
index `alias`(`alias`),
index `item_group`(`item_group`),
index `display_item`(`display_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:13,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Machine Message` ADD COLUMN `sample_collection` varchar(140), ADD COLUMN `machine_id` varchar(140)
2025-06-02 10:38:13,990 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_package` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`patient` varchar(140),
`patient_name` varchar(140),
`non_consultation_appointment` varchar(140),
`non_consultation_encounter` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`authorization_number` varchar(140),
`total_price` decimal(21,9) not null default 0,
`paid` int(1) not null default 0,
`sales_invoice` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:14,118 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`verifier_name` varchar(140) unique,
`verifier_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `verifier_id`(`verifier_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:14,262 WARNING database DDL Query made to DB:
create table `tabVC LRPMT Submitter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_field` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:38:42,332 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:38:44,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 10:38:44,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0
2025-06-02 10:38:45,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-02 10:38:45,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 10:38:46,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 10:38:46,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-02 10:38:47,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 10:38:47,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-06-02 10:38:47,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
