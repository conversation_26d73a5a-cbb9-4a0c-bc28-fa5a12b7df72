2025-06-02 10:36:38,429 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Symptom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaint` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,526 WARNING database DDL Query made to DB:
create table `tabBody Part Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,647 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`template` varchar(140),
`qty` int(11) not null default 1,
`amount` decimal(21,9) not null default 0,
`service_request` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`instructions` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,757 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`interval` decimal(21,9),
`sessions_completed` int(11) not null default 0,
`service_request` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,859 WARNING database DDL Query made to DB:
create table `tabPrescription Duration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number` int(11) not null default 0,
`period` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,978 WARNING database DDL Query made to DB:
create table `tabDosage Strength` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`strength` decimal(21,9) not null default 0,
`strength_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,077 WARNING database DDL Query made to DB:
create table `tabLab Test UOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_uom` varchar(140) unique,
`uom_description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,200 WARNING database DDL Query made to DB:
create table `tabAppointment Type Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`dn` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,314 WARNING database DDL Query made to DB:
create table `tabAppointment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment_type` varchar(140) unique,
`default_duration` int(11) not null default 0,
`allow_booking_for` varchar(140) default 'Practitioner',
`color` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,451 WARNING database DDL Query made to DB:
create table `tabClinical Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`clinical_note_type` varchar(140),
`terms_and_conditions` varchar(140),
`posting_date` datetime(6),
`practitioner` varchar(140),
`user` varchar(140),
`note` longtext,
`reference_doc` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,560 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,742 WARNING database DDL Query made to DB:
create table `tabLab Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_name` varchar(140) unique,
`department` varchar(140),
`disabled` int(1) not null default 0,
`nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`lab_test_code` varchar(140),
`lab_test_group` varchar(140),
`is_billable` int(1) not null default 1,
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` longtext,
`lab_test_template_type` varchar(140),
`descriptive_result` longtext,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`lab_test_normal_range` longtext,
`sensitivity` int(1) not null default 0,
`sample` varchar(140),
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample_details` text,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`change_in_item` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item`(`item`),
index `lab_test_group`(`lab_test_group`),
index `is_billable`(`is_billable`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:39,892 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`description` text,
`medical_department` varchar(140),
`disabled` int(1) not null default 0,
`goal` text,
`order_group` varchar(140),
`patient_age_from` int(11) not null default 0,
`patient_age_to` int(11) not null default 0,
`gender` varchar(140),
`is_inpatient` int(1) not null default 0,
`treatment_counselling_required_for_ip` int(1) not null default 0,
`healthcare_service_unit_type` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,028 WARNING database DDL Query made to DB:
create table `tabOrganism Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,145 WARNING database DDL Query made to DB:
create table `tabPatient Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`therapy_session` varchar(140),
`patient` varchar(140),
`assessment_template` varchar(140),
`company` varchar(140),
`healthcare_practitioner` varchar(140),
`assessment_datetime` datetime(6),
`assessment_description` text,
`total_score_obtained` int(11) not null default 0,
`total_score` int(11) not null default 0,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,263 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,386 WARNING database DDL Query made to DB:
create table `tabDiagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140) unique,
`estimated_duration` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,595 WARNING database DDL Query made to DB:
create table `tabPatient Encounter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`appointment_type` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`company` varchar(140),
`status` varchar(140),
`encounter_date` date,
`encounter_time` time(6),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`google_meet_link` varchar(140),
`invoiced` int(1) not null default 0,
`submit_orders_on_save` int(1) not null default 0,
`symptoms_in_print` int(1) not null default 0,
`diagnosis_in_print` int(1) not null default 1,
`therapy_plan` varchar(140),
`encounter_comment` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `appointment`(`appointment`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,760 WARNING database DDL Query made to DB:
create sequence if not exists observation_sample_collection_id_seq nocache nocycle
2025-06-02 10:36:40,782 WARNING database DDL Query made to DB:
create table `tabObservation Sample Collection` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`has_component` int(1) not null default 0,
`sample` varchar(140) default 'Urine',
`sample_type` varchar(140),
`uom` varchar(140),
`status` varchar(140),
`container_closure_color` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`collection_date_time` datetime(6),
`collection_point` varchar(140),
`collected_user` varchar(140),
`collected_by` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`reference_child` varchar(140),
`service_request` varchar(140),
`specimen` varchar(140),
`component_observation_parent` varchar(140),
`component_observations` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:40,893 WARNING database DDL Query made to DB:
create table `tabFee Validity Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,044 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140),
`item_code` varchar(140),
`assigned_to_practitioner` varchar(140),
`patient` varchar(140),
`practitioner` varchar(140),
`service_unit` varchar(140),
`from_date` date,
`to_date` date,
`from_time` time(6),
`to_time` time(6),
`update_stock` int(1) not null default 1,
`warehouse` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,242 WARNING database DDL Query made to DB:
create table `tabObservation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`observation_template` varchar(140),
`observation_category` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Registered',
`medical_department` varchar(140),
`amended_from` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`has_component` int(1) not null default 0,
`preferred_display_name` varchar(140),
`sample_collection_required` int(1) not null default 0,
`permitted_unit` varchar(140),
`sample` varchar(140),
`sample_type` varchar(140),
`permitted_data_type` varchar(140),
`method` varchar(140),
`specimen` varchar(140),
`sample_collection_time` datetime(6),
`sample_status` varchar(140),
`result_template` varchar(140),
`result_attach` text,
`result_boolean` varchar(140),
`result_data` varchar(140),
`result_text` longtext,
`result_float` decimal(21,9) not null default 0,
`result_select` varchar(140),
`result_datetime` datetime(6),
`result_time` datetime(6),
`result_period_from` datetime(6),
`result_period_to` datetime(6),
`options` text,
`time_of_result` datetime(6),
`time_of_approval` datetime(6),
`interpretation_template` varchar(140),
`result_interpretation` longtext,
`observation_method` varchar(140),
`reference` text,
`note` longtext,
`description` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice_status` varchar(140),
`sales_invoice_item` varchar(140),
`service_request` varchar(140),
`disapproval_reason` text,
`parent_observation` varchar(140),
`observation_idx` int(11) not null default 0,
`days` int(11) not null default 0,
`invoiced` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,380 WARNING database DDL Query made to DB:
create table `tabExercise Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_name` varchar(140),
`difficulty_level` varchar(140),
`description` longtext,
`exercise_steps` text,
`exercise_video` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,516 WARNING database DDL Query made to DB:
create table `tabComplaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaints` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,642 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`interval` decimal(21,9),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,747 WARNING database DDL Query made to DB:
create table `tabService Request Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:41,934 WARNING database DDL Query made to DB:
create table `tabTreatment Counselling` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`company` varchar(140),
`status` varchar(140),
`gender` varchar(140),
`patient_age` varchar(140),
`treatment_plan_template` varchar(140),
`price_list` varchar(140),
`amount` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`encounter_status` varchar(140),
`medical_department` varchar(140),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`admission_instruction` text,
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`admission_encounter` varchar(140),
`referring_practitioner` varchar(140),
`inpatient_record` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,054 WARNING database DDL Query made to DB:
create table `tabClinical Note Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clinical_note_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,255 WARNING database DDL Query made to DB:
create table `tabPatient Care Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient_care_type` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,392 WARNING database DDL Query made to DB:
create table `tabPatient Relation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`relation` varchar(140),
`description` text,
index `relation`(`relation`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,508 WARNING database DDL Query made to DB:
create table `tabService Request Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_request_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,749 WARNING database DDL Query made to DB:
create table `tabMedication Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`medication` varchar(140),
`medication_item` varchar(140),
`order_date` date,
`order_time` time(6),
`expected_date` date,
`company` varchar(140),
`status` varchar(140) default 'draft-Medication Request Status',
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`dosage` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`order_description` text,
`period` varchar(140),
`occurrence_time` time(6),
`total_dispensable_quantity` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`qty_invoiced` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `medication`(`medication`),
index `status`(`status`),
index `patient`(`patient`),
index `patient_email`(`patient_email`),
index `patient_mobile`(`patient_mobile`),
index `inpatient_record`(`inpatient_record`),
index `practitioner`(`practitioner`),
index `staff_role`(`staff_role`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:42,907 WARNING database DDL Query made to DB:
create table `tabNormal Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_event` varchar(140),
`allow_blank` int(1) not null default 0,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,026 WARNING database DDL Query made to DB:
create table `tabAntibiotic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic_name` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,181 WARNING database DDL Query made to DB:
create table `tabPatient Medical Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`status` varchar(140),
`attach` text,
`subject` longtext,
`communication_date` date,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `patient`(`patient`),
index `reference_doctype`(`reference_doctype`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,283 WARNING database DDL Query made to DB:
create table `tabSensitivity Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic` varchar(140),
`antibiotic_sensitivity` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,387 WARNING database DDL Query made to DB:
create table `tabPractitioner Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`allow_video_conferencing` int(1) not null default 0,
`schedule_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,499 WARNING database DDL Query made to DB:
create table `tabMedical Department` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`department` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,646 WARNING database DDL Query made to DB:
create table `tabCodification Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system` varchar(140),
`is_fhir_defined` int(1) not null default 0,
`oid` varchar(140),
`code_value` varchar(140),
`code` varchar(140),
`display` varchar(140),
`definition` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,792 WARNING database DDL Query made to DB:
create table `tabSample Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:43,911 WARNING database DDL Query made to DB:
create table `tabHealthcare Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140) unique,
`description` text,
`activity_duration` decimal(21,9),
`role` varchar(140),
`task_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,084 WARNING database DDL Query made to DB:
create table `tabPatient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`sex` varchar(140),
`blood_group` varchar(140),
`dob` date,
`image` text,
`status` varchar(140),
`uid` varchar(140) unique,
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`report_preference` varchar(140),
`mobile` varchar(140),
`phone` varchar(140),
`email` varchar(140),
`invite_user` int(1) not null default 1,
`user_id` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`default_currency` varchar(140),
`default_price_list` varchar(140),
`language` varchar(140),
`patient_details` text,
`occupation` varchar(140),
`marital_status` varchar(140),
`allergies` text,
`medication` text,
`medical_history` text,
`surgical_history` text,
`tobacco_past_use` varchar(140),
`tobacco_current_use` varchar(140),
`alcohol_past_use` varchar(140),
`alcohol_current_use` varchar(140),
`surrounding_factors` text,
`other_risk_factors` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_name`(`patient_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,227 WARNING database DDL Query made to DB:
create table `tabSensitivity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sensitivity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,341 WARNING database DDL Query made to DB:
create table `tabExercise Difficulty Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`difficulty_level` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,440 WARNING database DDL Query made to DB:
create table `tabService Unit Type Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`is_stock_item` int(1) not null default 0,
`billing_type` varchar(140),
`charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,542 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`title` varchar(140) unique,
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,654 WARNING database DDL Query made to DB:
create table `tabPrescription Dosage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dosage` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,783 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`barcode` varchar(140),
`uom` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`batch_no` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `actual_qty`(`actual_qty`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,886 WARNING database DDL Query made to DB:
create table `tabPractitioner Service Unit Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule` varchar(140),
`service_unit` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:44,998 WARNING database DDL Query made to DB:
create table `tabLab Test Group Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_or_new_line` varchar(140) default 'Add Test',
`lab_test_template` varchar(140),
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` varchar(140),
`group_event` varchar(140),
`group_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`allow_blank` int(1) not null default 0,
`group_test_normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,100 WARNING database DDL Query made to DB:
create table `tabBody Part` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,228 WARNING database DDL Query made to DB:
create table `tabABDM Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_name` varchar(140),
`request_date` datetime(6),
`status` varchar(140),
`url` varchar(140),
`request` longtext,
`traceback` longtext,
`response` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,358 WARNING database DDL Query made to DB:
create table `tabTherapy Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`invoiced` int(1) not null default 0,
`company` varchar(140),
`status` varchar(140),
`start_date` date,
`therapy_plan_template` varchar(140),
`title` varchar(140) default '{patient_name}',
`total_sessions` int(11) not null default 0,
`total_sessions_completed` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,616 WARNING database DDL Query made to DB:
create table `tabDosage Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dosage_form` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,735 WARNING database DDL Query made to DB:
create table `tabMedication Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,859 WARNING database DDL Query made to DB:
create table `tabOrganism` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:45,983 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140),
`mandatory` int(1) not null default 0,
`type` varchar(140),
`time_offset` decimal(21,9),
`description` text,
`task_duration` decimal(21,9),
`task_doctype` varchar(140),
`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,162 WARNING database DDL Query made to DB:
create table `tabObservation Reference Range` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`permitted_data_type` varchar(140),
`reference_type` varchar(140),
`applies_to` varchar(140),
`age` varchar(140),
`gestational_age` decimal(21,9) not null default 0,
`from_age_type` varchar(140) default 'Years',
`age_from` varchar(140),
`to_age_type` varchar(140) default 'Years',
`age_to` varchar(140),
`reference_from` varchar(140),
`datetime` datetime(6),
`from_duration` decimal(21,9),
`boolean_value` varchar(140),
`ratio` varchar(140),
`options` varchar(140),
`from_datetime` datetime(6),
`conditions` longtext,
`reference_to` varchar(140),
`to_datetime` datetime(6),
`to_duration` decimal(21,9),
`short_interpretation` varchar(140),
`long_interpretation` text,
`normal_boolean_value` varchar(140),
`normal_ratio` varchar(140),
`normal_from` decimal(21,9) not null default 0,
`normal_select` varchar(140),
`normal_condition` longtext,
`normal_interpretation` varchar(140),
`normal_to` decimal(21,9) not null default 0,
`normal_long_interpretation` text,
`abnormal_boolean_value` varchar(140),
`abnormal_from` decimal(21,9) not null default 0,
`abnormal_ratio` varchar(140),
`abnormal_select` varchar(140),
`abnormal_condition` longtext,
`abnormal_interpretation` varchar(140),
`abnormal_to` decimal(21,9) not null default 0,
`abnormal_long_interpretation` text,
`critical_boolean_value` varchar(140),
`critical_from` decimal(21,9) not null default 0,
`critical_ratio` varchar(140),
`critical_select` varchar(140),
`critical_condition` longtext,
`critical_interpretation` varchar(140),
`critical_to` decimal(21,9) not null default 0,
`critical_long_interpretation` text,
`reference_text` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,275 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`service_unit` varchar(140),
`datetime` datetime(6),
`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`available_qty` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`against_imo` varchar(140),
`against_imoe` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,384 WARNING database DDL Query made to DB:
create table `tabNormal Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_name` varchar(140),
`lab_test_event` varchar(140),
`result_value` varchar(140),
`lab_test_uom` varchar(140),
`secondary_uom_result` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`allow_blank` int(1) not null default 1,
`normal_range` longtext,
`lab_test_comment` varchar(140),
`bold` int(1) not null default 0,
`italic` int(1) not null default 0,
`underline` int(1) not null default 0,
`template` varchar(140),
`require_result_value` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,479 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,598 WARNING database DDL Query made to DB:
create table `tabLab Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_code` varchar(140),
`observation_template` varchar(140),
`lab_test_name` varchar(140),
`invoiced` int(1) not null default 0,
`service_request` varchar(140),
`lab_test_comment` text,
`lab_test_created` int(1) not null default 0,
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
index `invoiced`(`invoiced`),
index `lab_test_created`(`lab_test_created`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:46,713 WARNING database DDL Query made to DB:
create table `tabInpatient Occupancy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_unit` varchar(140),
`check_in` datetime(6),
`transferred_for_procedure` int(1) not null default 0,
`left` int(1) not null default 0,
`check_out` datetime(6),
`invoiced` int(1) not null default 0,
`scheduled_billing_time` datetime(6),
index `left`(`left`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:52,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `reference_dt` varchar(140), ADD COLUMN `reference_dn` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-06-02 10:36:52,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-06-02 10:36:52,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `treatment_counselling` varchar(140)
2025-06-02 10:36:52,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0
2025-06-02 10:36:52,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `patient` varchar(140), ADD COLUMN `inpatient_medication_entry_child` varchar(140)
2025-06-02 10:36:52,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-06-02 10:36:52,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `patient` varchar(140), ADD COLUMN `patient_name` varchar(140), ADD COLUMN `ref_practitioner` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-06-02 10:36:52,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0
2025-06-02 10:36:52,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `inpatient_medication_entry` varchar(140)
2025-06-02 10:36:52,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-06-02 10:37:50,458 WARNING database DDL Query made to DB:
create table `tabLimit Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment_no` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`is_cash_inpatient` int(1) not null default 0,
`previous_cash_limit` decimal(21,9) not null default 0,
`current_total_deposit` decimal(21,9) not null default 0,
`inpatient_record` varchar(140),
`cash_limit` decimal(21,9) not null default 0,
`is_non_nhif_patient` int(1) not null default 0,
`previous_daily_limit` decimal(21,9) not null default 0,
`current_total_cost` decimal(21,9) not null default 0,
`insurance_company` varchar(140),
`daily_limit` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`naming_series` varchar(140),
`approved_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:50,668 WARNING database DDL Query made to DB:
create table `tabModality Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`modality_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:50,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `healthcare_practitioner_type` varchar(140)
2025-06-02 10:37:50,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 10:37:51,054 WARNING database DDL Query made to DB:
create table `tabLab Machine Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`machine_make` varchar(140),
`machine_model` varchar(140),
`msh` int(11) not null default 0,
`obr` int(11) not null default 0,
`obx_nm_start` int(11) not null default 0,
`obx_nm_end` int(11) not null default 0,
`lab_test_prefix` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:51,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `total_service_unit_capacity` int(11) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality_name` varchar(140)
2025-06-02 10:37:51,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 10:37:51,403 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:51,527 WARNING database DDL Query made to DB:
create table `tabHealthcare Return Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:51,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `inpatient_record` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 10:37:51,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:37:52,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140), ADD COLUMN `discharge_date` date
2025-06-02 10:37:52,147 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_category_name` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:52,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `clinical_procedure` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-06-02 10:37:52,433 WARNING database DDL Query made to DB:
create table `tabMedication Class Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:52,558 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Contract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`default_price_list` varchar(140),
`is_active` int(1) not null default 0,
`start_date` date,
`end_date` date,
`apply_coverage_on_amount_with_tax` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:52,806 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`mobile` varchar(140),
`company` varchar(140),
`appointment` varchar(140),
`posting_datetime` datetime(6),
`practitioner` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`payment_type` varchar(140) default 'Cash',
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`years_of_insurance` int(11) not null default 0,
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `gender`(`gender`),
index `mobile`(`mobile`),
index `company`(`company`),
index `appointment`(`appointment`),
index `posting_datetime`(`posting_datetime`),
index `practitioner`(`practitioner`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `payment_type`(`payment_type`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `naming_series`(`naming_series`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:52,918 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,032 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,190 WARNING database DDL Query made to DB:
create table `tabHealthcare Discharge Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`discharge_type_name` varchar(140) unique,
`alias` varchar(140),
`discharge_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `discharge_type_id`(`discharge_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,317 WARNING database DDL Query made to DB:
create table `tabEncounter Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`encounter_category` varchar(140) unique,
`default_healthcare_service_unit` varchar(140),
`default_healthcare_practitioner` varchar(140),
`default_appointment_type` varchar(140),
`encounter_fee` decimal(21,9) not null default 0,
`encounter_fee_item` varchar(140),
`create_sales_invoice` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,506 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`price_list` varchar(140),
`qty` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`department_hsu` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `price_list`(`price_list`),
index `qty`(`qty`),
index `rate`(`rate`),
index `amount`(`amount`),
index `department_hsu`(`department_hsu`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,644 WARNING database DDL Query made to DB:
create table `tabRadiology Examination` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`prescribe` int(1) not null default 0,
`company` varchar(140),
`practitioner` varchar(140),
`radiology_examination_template` varchar(140),
`medical_department` varchar(140),
`service_unit` varchar(140),
`radiology_procedure_prescription` varchar(140),
`modality_type` varchar(140),
`modality` varchar(140),
`source` varchar(140) default 'Direct',
`referring_practitioner` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`start_date` date,
`start_time` time(6),
`notes` text,
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:53,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140), ADD COLUMN `clinical_procedure_check_list_template` varchar(140)
2025-06-02 10:37:53,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:37:54,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` ADD COLUMN `medication_name` varchar(140), ADD COLUMN `medication_category` varchar(140), ADD COLUMN `is_generic` int(1) not null default 0, ADD COLUMN `item` varchar(140), ADD COLUMN `item_code` varchar(140), ADD COLUMN `item_group` varchar(140), ADD COLUMN `description` text, ADD COLUMN `stock_uom` varchar(140), ADD COLUMN `is_billable` int(1) not null default 0, ADD COLUMN `rate` decimal(21,9) not null default 0, ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `default_dosage_form` varchar(140), ADD COLUMN `bypass_medication_class_interaction_check` int(1) not null default 1, ADD COLUMN `bypass_allergy_check` int(1) not null default 1, ADD COLUMN `bypass_medical_coding_check` int(1) not null default 1
2025-06-02 10:37:54,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0
2025-06-02 10:37:54,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 10:37:54,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 10:37:54,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:37:54,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `triage` varchar(140), ADD COLUMN `color` varchar(140), ADD COLUMN `patient_referral` varchar(140), ADD COLUMN `radiology_examination_template` varchar(140), ADD COLUMN `radiology_procedure_prescription` varchar(140), ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality` varchar(140), ADD COLUMN `practitioner_availability` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 10:37:54,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:37:54,722 WARNING database DDL Query made to DB:
create table `tabMediciation Override Reason Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason_code` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:54,849 WARNING database DDL Query made to DB:
create table `tabHealthcare Notes Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`disabled` int(1) not null default 0,
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:55,025 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Subscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`is_active` int(1) not null default 1,
`patient` varchar(140),
`patient_name` varchar(140),
`mobile_number` varchar(140),
`customer` varchar(140),
`gender` varchar(140),
`country` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`subscription_end_date` date,
`healthcare_insurance_coverage_plan` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:55,173 WARNING database DDL Query made to DB:
create table `tabInsurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`insurance_coverage_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:55,278 WARNING database DDL Query made to DB:
create table `tabReferring Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:55,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `usage_interval` int(1) not null default 0, ADD COLUMN `quantity` int(11) not null default 0, ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `expected_date` date, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `occurrence` datetime(6), ADD COLUMN `occurence_period` decimal(21,9), ADD COLUMN `note` text, ADD COLUMN `drug_prescription_created` int(1) not null default 0
2025-06-02 10:37:55,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 10:37:55,819 WARNING database DDL Query made to DB:
create table `tabInpatient Consultancy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`consultation_item` varchar(140),
`rate` decimal(21,9) not null default 0,
`delivery_note` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:55,919 WARNING database DDL Query made to DB:
create table `tabLab Bundle Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_template` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:56,026 WARNING database DDL Query made to DB:
create table `tabSample Collection Lab Test Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test` varchar(140),
`lab_test_tempate` varchar(140),
`test_abbr` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:56,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `allow_procedures` int(1) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140)
2025-06-02 10:37:56,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:37:56,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-06-02 10:37:56,481 WARNING database DDL Query made to DB:
create table `tabPatient Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`naming_series` varchar(140),
`triage` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Pending',
`date` date,
`time` time(6),
`referring_practitioner` varchar(140),
`priority` varchar(140),
`patient_encounter` varchar(140),
`referring_reason` varchar(140),
`referred_to_practitioner` varchar(140),
`referral_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:56,606 WARNING database DDL Query made to DB:
create table `tabItem Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`quantity` int(11) not null default 1,
`reason` varchar(140),
`encounter_no` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:56,706 WARNING database DDL Query made to DB:
create table `tabMedication Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`condition` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:56,889 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`customer` varchar(140),
`website` varchar(140),
`billed_but_not_claimed_account` varchar(140),
`approved_claim_receivable_account` varchar(140),
`rejected_claims_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:57,039 WARNING database DDL Query made to DB:
create table `tabHealthcare Admission Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`admission_type_name` varchar(140) unique,
`alias` varchar(140),
`admission_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `admission_type_id`(`admission_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:57,345 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_name` varchar(140),
`service_type` varchar(140),
`item_code` varchar(140),
`payor_plan` varchar(140) default 'Cash',
`price_list` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` int(11) not null default 0,
`qty_returned` int(11) not null default 0,
`amount` decimal(21,9) not null default 0,
`payment_type` varchar(140) default 'Cash',
`years_of_insurance` int(11) not null default 0,
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`authorization_number` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`request_id` varchar(140),
`department_hsu` varchar(140),
`sales_invoice_number` varchar(140),
`lrpmt_doc_created` int(1) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_name`(`service_name`),
index `service_type`(`service_type`),
index `item_code`(`item_code`),
index `payor_plan`(`payor_plan`),
index `price_list`(`price_list`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `payment_type`(`payment_type`),
index `years_of_insurance`(`years_of_insurance`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `authorization_number`(`authorization_number`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `request_id`(`request_id`),
index `department_hsu`(`department_hsu`),
index `sales_invoice_number`(`sales_invoice_number`),
index `lrpmt_doc_created`(`lrpmt_doc_created`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:57,452 WARNING database DDL Query made to DB:
create table `tabMedication Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_category` varchar(140) unique,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`is_group` int(1) not null default 0,
`old_parent` varchar(140),
`parent_medication_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:57,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `medical_code` varchar(140), ADD COLUMN `medical_code_standard` varchar(140)
2025-06-02 10:37:57,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 10:37:57,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 10:37:57,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 10:37:57,858 WARNING database DDL Query made to DB:
create table `tabTriage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`triage_description` varchar(140) unique,
`category` varchar(140),
`attend_within` decimal(21,9),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:58,036 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` date,
`from_time` time(6),
`to_date` date,
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` int(1) not null default 0,
`tuesday` int(1) not null default 0,
`wednesday` int(1) not null default 0,
`thursday` int(1) not null default 0,
`friday` int(1) not null default 0,
`saturday` int(1) not null default 0,
`sunday` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:58,175 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`insurance_claim` varchar(140),
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`sales_invoice` varchar(140),
`discount` decimal(21,9) not null default 0,
`claim_coverage` decimal(21,9) not null default 0,
`claim_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:58,415 WARNING database DDL Query made to DB:
create table `tabHealthcare Facility` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_name` varchar(140) unique,
`facility_code` varchar(140),
`facility_level_code` varchar(140),
`certification_no` varchar(140),
`abbreviation_code` varchar(140),
`classification` varchar(140),
`classification_id` varchar(140),
`ward_code` varchar(140),
`postal_address` text,
`owner_code` varchar(140),
`ownership_type_code` varchar(140),
`pay_to_code` varchar(140),
`percent_sent` decimal(21,9) not null default 0,
`certification_application_date` date,
`status` varchar(140),
`has_eclaims` int(1) not null default 0,
`send_amount_to_msd` int(1) not null default 0,
`key_contact` varchar(140),
`email_address` varchar(140),
`telephone_no` varchar(140),
`fax` varchar(140),
`website` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `facility_code`(`facility_code`),
index `facility_level_code`(`facility_level_code`),
index `certification_no`(`certification_no`),
index `abbreviation_code`(`abbreviation_code`),
index `classification`(`classification`),
index `classification_id`(`classification_id`),
index `ward_code`(`ward_code`),
index `owner_code`(`owner_code`),
index `ownership_type_code`(`ownership_type_code`),
index `pay_to_code`(`pay_to_code`),
index `percent_sent`(`percent_sent`),
index `certification_application_date`(`certification_application_date`),
index `status`(`status`),
index `has_eclaims`(`has_eclaims`),
index `send_amount_to_msd`(`send_amount_to_msd`),
index `key_contact`(`key_contact`),
index `email_address`(`email_address`),
index `telephone_no`(`telephone_no`),
index `fax`(`fax`),
index `website`(`website`),
index `longitude`(`longitude`),
index `latitude`(`latitude`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:37:58,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 10:37:58,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:37:58,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `triage` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 10:37:58,906 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_priority` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
