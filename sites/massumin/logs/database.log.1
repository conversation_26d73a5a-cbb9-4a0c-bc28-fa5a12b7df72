2025-06-02 10:38:47,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-06-02 10:38:48,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 10:38:48,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-06-02 10:38:48,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 10:38:50,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0
2025-06-02 10:38:50,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 10:38:51,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-06-02 10:38:51,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-02 10:38:51,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:38:52,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:38:52,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:38:53,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 10:38:53,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:38:54,468 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:38:54,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:38:55,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:38:55,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 10:38:55,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:38:55,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-02 10:38:56,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:38:56,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-02 10:38:56,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:38:57,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:38:57,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0
2025-06-02 10:38:57,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:38:57,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `interval` decimal(21,9)
2025-06-02 10:38:58,181 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 10:38:58,211 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-02 10:38:58,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:38:59,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-02 10:39:00,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:39:00,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 10:39:00,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:01,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:39:01,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:01,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 10:39:02,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:02,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:39:02,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `occurence_period` decimal(21,9)
2025-06-02 10:39:03,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:03,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 10:39:03,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 10:39:03,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 10:39:03,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:39:04,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 10:39:07,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:39:10,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 10:39:13,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `insurance_coverage_plan` varchar(140), ADD COLUMN `cash_limit` decimal(21,9) not null default 0, ADD COLUMN `patient_appointment` varchar(140), ADD COLUMN `practitioner_name` varchar(140), ADD COLUMN `admission_type` varchar(140), ADD COLUMN `primary_practitioner_name` varchar(140), ADD COLUMN `secondary_practitioner_name` varchar(140), ADD COLUMN `inpatient_record_type` varchar(140), ADD COLUMN `duplicate` int(1) not null default 0, ADD COLUMN `reference_inpatient_record` varchar(140), ADD COLUMN `duplicated_from` varchar(140), ADD COLUMN `history` text, ADD COLUMN `medication` text, ADD COLUMN `when_to_obtain_urgent_care` text, ADD COLUMN `on_examination` text, ADD COLUMN `surgical_procedure` text, ADD COLUMN `review` text
2025-06-02 10:39:14,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 10:39:15,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `nhif_2c_form` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `hms_tz_is_out_of_stock` int(1) not null default 0, ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_discount_percent` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `quantity_returned` int(11) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `invoiced` int(1) not null default 0, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `dn_detail` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `delivery_note` varchar(140)
2025-06-02 10:39:15,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `occurence_period` decimal(21,9)
2025-06-02 10:39:15,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `ward_type` varchar(140), ADD COLUMN `is_service_chargeable` int(1) not null default 0, ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0
2025-06-02 10:39:15,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:17,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `medical_department` varchar(140), ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_name` varchar(140), ADD COLUMN `authorization_number` varchar(140), ADD COLUMN `hms_tz_all_items_out_of_stock` int(1) not null default 0, ADD COLUMN `shmh_organogram_department` varchar(140), ADD COLUMN `patient` varchar(140), ADD COLUMN `patient_name` varchar(140), ADD COLUMN `hms_tz_phone_no` varchar(140), ADD COLUMN `hms_tz_comment` text, ADD COLUMN `department` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `hms_tz_appointment_no` varchar(140)
2025-06-02 10:39:17,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-02 10:39:19,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `mtuha` varchar(140), ADD COLUMN `healthcare_notes_template` varchar(140), ADD COLUMN `procedure_notes` longtext, ADD COLUMN `pre_operative_notes_template` varchar(140), ADD COLUMN `pre_operative_note` longtext, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 10:39:19,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 10:39:21,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `approval_date` date, ADD COLUMN `hms_tz_is_out_of_stock` int(1) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `department` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_name` varchar(140), ADD COLUMN `original_item` varchar(140), ADD COLUMN `original_stock_uom_qty` decimal(21,9) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `last_date_prescribed` date, ADD COLUMN `last_qty_prescribed` decimal(21,9) not null default 0, ADD COLUMN `recommended_qty` decimal(21,9) not null default 0
2025-06-02 10:39:21,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-06-02 10:39:23,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `card_no` varchar(140), ADD COLUMN `insurance_card_detail` text, ADD COLUMN `product_code` varchar(140), ADD COLUMN `membership_no` varchar(140), ADD COLUMN `scheme_id` varchar(140), ADD COLUMN `national_id` varchar(140) unique, ADD COLUMN `insurance_provider` varchar(140), ADD COLUMN `common_occupation` varchar(140), ADD COLUMN `ethnicity` varchar(140), ADD COLUMN `nida_card_number` varchar(140), ADD COLUMN `area` varchar(140), ADD COLUMN `demography` varchar(140), ADD COLUMN `how_did_you_hear_about_us` varchar(140), ADD COLUMN `referred_from` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `patient_signature` longtext, ADD COLUMN `cash_limit` decimal(21,9) not null default 0, ADD COLUMN `next_to_kin_name` varchar(140), ADD COLUMN `next_to_kin_mobile_no` varchar(140), ADD COLUMN `next_to_kin_relationship` varchar(140), ADD COLUMN `patient_details_with_formatting` longtext
2025-06-02 10:39:23,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD UNIQUE INDEX IF NOT EXISTS national_id (`national_id`)
2025-06-02 10:39:24,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 10:39:26,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `mobile` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `ref_vital_signs` varchar(140), ADD COLUMN `ref_patient_encounter` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `has_no_consultation_charges` int(1) not null default 0, ADD COLUMN `healthcare_referrer_type` varchar(140), ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `referral_no` varchar(140), ADD COLUMN `remarks` text, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `coverage_plan_card_number` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `nhif_employer_name` varchar(140), ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `nhif_patient_claim` varchar(140), ADD COLUMN `jubilee_patient_claim` varchar(140), ADD COLUMN `authorization_number` varchar(140), ADD COLUMN `years_of_insurance` int(11) not null default 0, ADD COLUMN `apply_fasttrack_charge` int(1) not null default 0, ADD COLUMN `insurance_company_name` varchar(140), ADD COLUMN `require_fingerprint` int(1) not null default 0, ADD COLUMN `require_facial_recognation` int(1) not null default 0, ADD COLUMN `biometric_method` varchar(140), ADD COLUMN `fpcode` varchar(140), ADD COLUMN `payment_reference` varchar(140), ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `old_hms_number` varchar(140), ADD COLUMN `patient_image2` text, ADD COLUMN `sms_sent` int(1) not null default 0
2025-06-02 10:39:26,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:39:27,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `override_insurance_subscription` int(1) not null default 0, ADD COLUMN `hso_payment_method` varchar(140), ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 10:39:33,270 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `insurance_coverage_plan` varchar(140), ADD COLUMN `image` text, ADD COLUMN `blood_group` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `admission_service_unit_type` varchar(140), ADD COLUMN `encounter_category` varchar(140), ADD COLUMN `encounter_mode_of_payment` varchar(140), ADD COLUMN `sales_invoice` varchar(140), ADD COLUMN `price_list` varchar(140), ADD COLUMN `abbr` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `hms_tz_previous_examination_detail` longtext, ADD COLUMN `examination_detail` longtext, ADD COLUMN `encounter_type` varchar(140), ADD COLUMN `finalized` int(1) not null default 0, ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `lab_bundle` varchar(140), ADD COLUMN `default_healthcare_service_unit` varchar(140), ADD COLUMN `ed_reason_for_absence` varchar(140), ADD COLUMN `ed_addressed_to` text, ADD COLUMN `ed_no_of_days` int(11) not null default 0, ADD COLUMN `patient_signature` longtext, ADD COLUMN `healthcare_practitioner_signature` longtext, ADD COLUMN `previous_total` decimal(21,9) not null default 0, ADD COLUMN `current_total` decimal(21,9) not null default 0, ADD COLUMN `is_not_billable` int(1) not null default 0, ADD COLUMN `duplicated` int(1) not null default 0, ADD COLUMN `reference_encounter` varchar(140), ADD COLUMN `from_encounter` varchar(140), ADD COLUMN `has_preapproval` int(1) not null default 0
2025-06-02 10:39:34,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140)
2025-06-02 10:39:34,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:39:35,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` ADD COLUMN `image` text, ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `shm_coverage_plan_name` varchar(140), ADD COLUMN `patient_progress` varchar(140), ADD COLUMN `oxygen_saturation_spo2` varchar(140), ADD COLUMN `rbg` decimal(21,9) not null default 0, ADD COLUMN `visual_acuity_re` varchar(140), ADD COLUMN `visual_acuity_le` varchar(140), ADD COLUMN `intraocular_pressure_re` varchar(140), ADD COLUMN `intraocular_pressure_le` varchar(140), ADD COLUMN `eye_opening` varchar(140), ADD COLUMN `verbal_response` varchar(140), ADD COLUMN `motor_response` varchar(140), ADD COLUMN `height_in_cm` int(11) not null default 0
2025-06-02 10:39:35,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `height` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-06-02 10:39:37,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `hms_tz_patient_age` varchar(140), ADD COLUMN `hms_tz_patient_sex` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `healthcare_practitioner_name` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `radiology_report` varchar(140), ADD COLUMN `radiology_report_details` longtext, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 10:43:04,130 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:43:05,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 10:43:07,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 10:43:09,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-02 10:43:09,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-02 10:43:10,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:43:11,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:43:12,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-02 10:43:13,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:43:14,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:43:14,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:43:14,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:43:15,270 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 10:43:15,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:16,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:43:16,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:43:16,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:43:16,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-02 10:43:17,166 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:17,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:43:17,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 10:43:18,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:18,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `interval` decimal(21,9)
2025-06-02 10:43:19,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 10:43:19,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_code`, DROP INDEX `lab_test_name_index`
2025-06-02 10:43:19,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:43:19,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:43:20,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:43:20,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-02 10:43:21,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:43:21,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 10:43:21,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 10:43:22,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:22,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:43:22,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:43:23,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:43:23,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0
2025-06-02 10:43:23,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 10:43:23,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:24,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:43:24,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9)
2025-06-02 10:43:24,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:43:25,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 10:43:25,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 10:43:25,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 10:43:25,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:43:26,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0
2025-06-02 10:43:26,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:43:27,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 10:43:27,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:43:29,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-06-02 10:43:33,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140)
2025-06-02 11:30:18,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `height` decimal(21,9) not null default 0, MODIFY `rbg` decimal(21,9) not null default 0
2025-06-02 11:30:18,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 11:30:19,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Schedule Time Slot` MODIFY `duration` decimal(21,9) not null default 0
2025-06-02 11:30:19,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabNursing Task` MODIFY `task_duration` decimal(21,9), MODIFY `duration` decimal(21,9)
2025-06-02 11:30:19,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 11:30:19,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order Entry` MODIFY `dosage` decimal(21,9) not null default 0
2025-06-02 11:30:20,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:30:20,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `discharge_ordered_datetime` datetime(6), ADD COLUMN `reason_for_cancellation` text
2025-06-02 11:30:20,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:30:20,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:30:21,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template` MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-02 11:30:21,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 11:30:21,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order` MODIFY `completed_orders` decimal(21,9) not null default 0, MODIFY `total_orders` decimal(21,9) not null default 0
2025-06-02 11:30:21,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:30:22,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:30:22,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 11:30:22,718 WARNING database DDL Query made to DB:
create table `tabMedical Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medical_code_standard` varchar(140),
`code` varchar(140) unique,
`uri` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:23,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 11:30:23,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:30:24,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabDosage Strength` MODIFY `strength` decimal(21,9) not null default 0
2025-06-02 11:30:24,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type Service Item` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 11:30:24,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 11:30:24,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-02 11:30:25,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0
2025-06-02 11:30:25,917 WARNING database DDL Query made to DB:
create table `tabMedical Code Standard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medical_code` varchar(140) unique,
`uri` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:26,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:30:26,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:30:26,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` DROP INDEX `department`
2025-06-02 11:30:27,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabCodification Table` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `description` text
2025-06-02 11:30:27,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Activity` MODIFY `activity_duration` decimal(21,9)
2025-06-02 11:30:27,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:30:27,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:30:28,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Group Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:30:28,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabNursing Checklist Template Task` MODIFY `time_offset` decimal(21,9), MODIFY `task_duration` decimal(21,9)
2025-06-02 11:30:29,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Entry Detail` MODIFY `available_qty` decimal(21,9) not null default 0, MODIFY `dosage` decimal(21,9) not null default 0
2025-06-02 11:30:29,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Result` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:30:29,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:31:11,674 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 11:31:12,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 11:31:13,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 11:31:14,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:31:15,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 11:31:15,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:31:15,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:31:16,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:31:16,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-02 11:31:16,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:31:17,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:31:17,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 11:31:17,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 11:31:17,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:18,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:31:18,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:31:18,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 11:31:18,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:19,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:31:19,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9)
2025-06-02 11:31:19,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:20,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 11:31:20,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 11:31:20,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 11:31:20,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 11:31:20,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0
2025-06-02 11:31:20,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD UNIQUE INDEX IF NOT EXISTS department (`department`)
2025-06-02 11:31:20,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:31:21,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-02 11:31:21,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:23,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:27,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0
2025-06-02 11:31:28,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:31:29,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD COLUMN `is_non_specific` int(1) not null default 0
2025-06-02 11:31:29,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `hms_tz_appointment` varchar(140), ADD COLUMN `inpatient_record` varchar(140), ADD COLUMN `hms_tz_patient_age` varchar(140), ADD COLUMN `hms_tz_patient_sex` varchar(140), ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `total_sessions_cancelled` int(11) not null default 0, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140)
2025-06-02 11:31:30,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `room_type` varchar(140), ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0, ADD COLUMN `is_service_chargeable` int(1) not null default 0
2025-06-02 11:31:31,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `has_no_consultation_charges_for_cash` int(1) not null default 0, ADD COLUMN `description` text, ADD COLUMN `visit_type_id` varchar(140), ADD COLUMN `visit_type_name_alias` varchar(140), ADD COLUMN `super_specialist_fasttrack_item` varchar(140), ADD COLUMN `gp_fasttrack_item` varchar(140), ADD COLUMN `assistant_md_followup_item` varchar(140), ADD COLUMN `required_input` varchar(140), ADD COLUMN `specialist_followup_item` varchar(140), ADD COLUMN `assistant_md_fasttrack_item` varchar(140), ADD COLUMN `super_specialist_followup_item` varchar(140), ADD COLUMN `gp_followup_item` varchar(140), ADD COLUMN `specialist_fasttrack_item` varchar(140), ADD COLUMN `has_no_consultation_charges_for_insurance` int(1) not null default 0, ADD COLUMN `source` varchar(140), ADD COLUMN `requires_remarks` int(1) not null default 0, ADD COLUMN `has_followup_charges` int(1) not null default 0, ADD COLUMN `requires_referral_no` int(1) not null default 0, ADD COLUMN `has_fasttrack_charges` int(1) not null default 0, ADD COLUMN `maximum_visit_per_month` varchar(140)
2025-06-02 11:31:31,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `sessions_cancelled` int(11) not null default 0, ADD COLUMN `comment` text, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `invoiced` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `therapy_plan_created` int(1) not null default 0
2025-06-02 11:31:32,490 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `title_and_qualification` varchar(140), ADD COLUMN `abbreviation` varchar(140), ADD COLUMN `hms_tz_company` varchar(140), ADD COLUMN `tz_mct_code` varchar(140), ADD COLUMN `nhif_physician_qualification` varchar(140), ADD COLUMN `bypass_vitals` int(1) not null default 0, ADD COLUMN `doctors_signature` longtext, ADD COLUMN `national_id` varchar(140), ADD COLUMN `date_loggedin_to_nhif` date, ADD COLUMN `default_medication_healthcare_service_unit` varchar(140), ADD COLUMN `shm_doctor_organogram_department` varchar(140)
2025-06-02 11:31:32,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 11:31:33,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `dosage_info` text, ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0, ADD COLUMN `hms_tz_is_lrp_item_created` int(1) not null default 0, ADD COLUMN `service_request` varchar(140), ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `department` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:31:33,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0
2025-06-02 11:31:34,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `title` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `appointment` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `workflow_state` varchar(140)
2025-06-02 11:31:35,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `coverage_plan_card_number` varchar(140), ADD COLUMN `company` varchar(140), ADD COLUMN `verifier_id` varchar(140), ADD COLUMN `card_type_id` varchar(140), ADD COLUMN `card_type_name` varchar(140), ADD COLUMN `hms_tz_product_code` varchar(140), ADD COLUMN `hms_tz_product_name` varchar(140), ADD COLUMN `hms_tz_scheme_id` varchar(140), ADD COLUMN `hms_tz_scheme_name` varchar(140), ADD COLUMN `national_id` varchar(140)
2025-07-03 17:23:06,216 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_ee33738cf5e1ee6a'@'localhost'
2025-07-03 17:23:22,072 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_ee33738cf5e1ee6a`
2025-07-03 17:23:22,075 WARNING database DDL Query made to DB:
CREATE USER '_ee33738cf5e1ee6a'@'localhost' IDENTIFIED BY 'bbyNVUF21ZpfmflT'
2025-07-03 17:23:22,083 WARNING database DDL Query made to DB:
CREATE DATABASE `_ee33738cf5e1ee6a` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-07-03 17:29:06,365 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-03 17:29:06,387 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-07-13 11:29:16,628 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:29:18,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-07-13 11:29:18,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` ADD COLUMN `vfd_payment_type` varchar(140)
2025-07-13 11:29:18,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-13 11:29:18,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-07-13 11:29:19,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:29:19,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD COLUMN `company` varchar(140)
2025-07-13 11:29:19,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:20,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-07-13 11:29:21,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:29:21,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:29:21,666 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-13 11:29:21,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0
2025-07-13 11:29:21,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:29:22,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-13 11:29:22,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0
2025-07-13 11:29:22,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-13 11:29:22,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-13 11:29:22,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-13 11:29:23,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0
2025-07-13 11:29:24,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-13 11:29:24,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-07-13 11:29:25,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-13 11:29:25,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0
2025-07-13 11:29:25,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `price_list` varchar(140)
2025-07-13 11:29:25,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-13 11:29:25,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-13 11:29:25,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-07-13 11:29:26,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:26,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `expected_compensation` decimal(21,9) not null default 0, MODIFY `time_to_fill` decimal(21,9)
2025-07-13 11:29:26,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:26,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-07-13 11:29:26,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,294 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,609 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:27,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-07-13 11:29:28,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:28,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:29,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `longitude` decimal(21,9) not null default 0, MODIFY `latitude` decimal(21,9) not null default 0
2025-07-13 11:29:29,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:29,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:29:29,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:29,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0
2025-07-13 11:29:29,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:29,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:29,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-07-13 11:29:29,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `actual_cost` decimal(21,9) not null default 0
2025-07-13 11:29:30,490 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,627 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:30,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `sanctioned_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:29:31,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-07-13 11:29:31,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:31,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:29:32,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:32,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-07-13 11:29:32,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:32,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `average_rating` decimal(3,2), MODIFY `expected_average_rating` decimal(3,2)
2025-07-13 11:29:32,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:32,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-07-13 11:29:32,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:32,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-07-13 11:29:32,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:32,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-07-13 11:29:33,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-07-13 11:29:33,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:33,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:34,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:34,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-07-13 11:29:34,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:34,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `total_leave_days` decimal(21,9) not null default 0
2025-07-13 11:29:34,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:34,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-07-13 11:29:34,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:34,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-07-13 11:29:34,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-07-13 11:29:35,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0
2025-07-13 11:29:35,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-07-13 11:29:35,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-07-13 11:29:35,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0
2025-07-13 11:29:35,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
