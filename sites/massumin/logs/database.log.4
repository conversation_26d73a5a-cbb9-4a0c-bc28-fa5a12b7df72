2025-05-06 16:25:02,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, <PERSON>ODIF<PERSON> `total_net_weight` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0
2025-05-06 16:25:02,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-05-06 16:25:02,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-05-06 16:25:02,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-05-06 16:25:02,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-05-06 16:25:22,173 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_ee33738cf5e1ee6a'@'localhost'
2025-05-06 16:25:32,425 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_ee33738cf5e1ee6a`
2025-05-06 16:25:32,427 WARNING database DDL Query made to DB:
CREATE USER '_ee33738cf5e1ee6a'@'localhost' IDENTIFIED BY 'bbyNVUF21ZpfmflT'
2025-05-06 16:25:32,428 WARNING database DDL Query made to DB:
CREATE DATABASE `_ee33738cf5e1ee6a` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-05-06 16:31:30,538 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-05-06 16:31:30,544 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-05-14 19:16:43,950 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-14 19:16:45,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` ADD COLUMN `vfd_payment_type` varchar(140)
2025-05-14 19:16:45,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0
2025-05-14 19:16:45,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-14 19:16:45,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `change_to_return` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `amount_received` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-05-14 19:16:46,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-14 19:16:47,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-05-14 19:16:47,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0
2025-05-14 19:16:48,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-14 19:16:49,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-05-14 19:16:49,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0
2025-05-14 19:16:50,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-05-14 19:16:50,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `time_to_fill` decimal(21,9), MODIFY `expected_compensation` decimal(21,9) not null default 0
2025-05-14 19:16:50,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-05-14 19:16:51,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:51,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:52,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:53,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-05-14 19:16:53,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:53,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:53,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-05-14 19:16:53,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:53,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-14 19:16:53,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:53,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-05-14 19:16:53,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:54,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:54,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0
2025-05-14 19:16:54,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:54,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:54,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:54,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `actual_cost` decimal(21,9) not null default 0
2025-05-14 19:16:54,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:55,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:55,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:55,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:55,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:55,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `sanctioned_amount` decimal(21,9) not null default 0
2025-05-14 19:16:55,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `goal_completion` decimal(21,9) not null default 0
2025-05-14 19:16:56,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-05-14 19:16:56,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:56,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-05-14 19:16:56,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:57,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `expected_average_rating` decimal(3,2), MODIFY `average_rating` decimal(3,2)
2025-05-14 19:16:57,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:57,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-05-14 19:16:57,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:57,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-05-14 19:16:57,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:57,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-05-14 19:16:57,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:57,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-05-14 19:16:58,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:58,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:58,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:58,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:58,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:58,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:59,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:59,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-05-14 19:16:59,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:59,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `total_leave_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-05-14 19:16:59,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:59,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-05-14 19:16:59,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-05-14 19:16:59,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-05-14 19:16:59,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:00,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-05-14 19:17:00,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:00,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0
2025-05-14 19:17:00,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:00,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:00,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-05-14 19:17:00,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:01,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:01,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `score` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0
2025-05-14 19:17:01,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:01,727 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:01,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:02,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-05-14 19:17:02,089 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:02,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-05-14 19:17:02,299 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:02,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:02,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:02,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:03,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `applicant_rating` decimal(3,2), MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-05-14 19:17:03,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:03,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:03,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-05-14 19:17:03,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:03,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:03,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-05-14 19:17:04,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-05-14 19:17:04,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:04,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-05-14 19:17:05,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-05-14 19:17:05,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:05,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0, MODIFY `total_estimated_cost` decimal(21,9) not null default 0
2025-05-14 19:17:06,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:06,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:06,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-05-14 19:17:06,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:06,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:06,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `current_ctc` decimal(21,9) not null default 0, MODIFY `revised_ctc` decimal(21,9) not null default 0
2025-05-14 19:17:06,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:06,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0, MODIFY `max_leaves_allowed` decimal(21,9) not null default 0, MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0
2025-05-14 19:17:06,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-05-14 19:17:07,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:07,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_payable_amount` decimal(21,9) not null default 0, MODIFY `total_receivable_amount` decimal(21,9) not null default 0, MODIFY `total_asset_recovery_cost` decimal(21,9) not null default 0
2025-05-14 19:17:07,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:08,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-14 19:17:08,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:08,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-05-14 19:17:08,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:08,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` MODIFY `message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>'
2025-05-14 19:17:08,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:08,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-05-14 19:17:08,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:09,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:09,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `rating` decimal(3,2)
2025-05-14 19:17:09,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:09,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-05-14 19:17:09,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:09,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-05-14 19:17:09,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:10,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-14 19:17:10,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:10,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-14 19:17:10,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:10,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:10,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-05-14 19:17:10,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:11,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `total_actual_amount` decimal(21,9) not null default 0, MODIFY `exemption_amount` decimal(21,9) not null default 0
2025-05-14 19:17:11,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:11,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:11,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-14 19:17:11,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:11,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-14 19:17:11,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:11,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `base` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0
2025-05-14 19:17:11,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:12,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-05-14 19:17:12,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:12,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `custom_actual_total_deduction` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `custom_overtime_normal` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `custom_actual_gross_pay` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `custom_actual_taxable_salary` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0
2025-05-14 19:17:12,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:12,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-05-14 19:17:12,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:12,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-05-14 19:17:13,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:13,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:13,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:13,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:13,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-05-14 19:17:13,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:14,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-05-14 19:17:14,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:14,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_declared_amount` decimal(21,9) not null default 0
2025-05-14 19:17:14,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:14,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-05-14 19:17:14,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:14,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-05-14 19:17:14,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:14,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0
2025-05-14 19:17:15,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:15,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0
2025-05-14 19:17:15,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:15,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:15,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:15,716 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0
2025-05-14 19:17:15,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:15,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0
2025-05-14 19:17:15,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:16,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-05-14 19:17:16,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:16,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD COLUMN `tax_relief_limit` decimal(21,9) not null default 0
2025-05-14 19:17:16,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-05-14 19:17:16,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:16,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-05-14 19:17:16,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:16,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-05-14 19:17:16,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:17,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0
2025-05-14 19:17:17,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `creation`(`creation`)
2025-05-14 19:17:17,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-14 19:17:17,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-14 19:17:18,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-14 19:17:18,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-14 19:17:19,495 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-14 19:17:19,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-14 19:17:24,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-14 19:17:27,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0
2025-06-02 10:36:32,196 WARNING database DDL Query made to DB:
create table `tabPatient History Standard Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`date_fieldname` varchar(140),
`selected_fields` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:32,416 WARNING database DDL Query made to DB:
create table `tabVital Signs` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`appointment` varchar(140),
`encounter` varchar(140),
`company` varchar(140),
`signs_date` date,
`signs_time` time(6),
`temperature` varchar(140),
`pulse` varchar(140),
`respiratory_rate` varchar(140),
`tongue` varchar(140),
`abdomen` varchar(140),
`reflexes` varchar(140),
`bp_systolic` varchar(140),
`bp_diastolic` varchar(140),
`bp` varchar(140),
`vital_signs_note` text,
`height` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`bmi` decimal(21,9) not null default 0,
`nutrition_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:32,592 WARNING database DDL Query made to DB:
create table `tabLab Test Sample` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140),
`sample` varchar(140) unique,
`container_closure_color` varchar(140),
`sample_uom` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:32,783 WARNING database DDL Query made to DB:
create table `tabHealthcare Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`practitioner_name` varchar(140),
`gender` varchar(140),
`image` text,
`status` varchar(140) default 'Active',
`mobile_phone` varchar(140),
`residence_phone` varchar(140),
`office_phone` varchar(140),
`practitioner_type` varchar(140) default 'Internal',
`employee` varchar(140),
`supplier` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`user_id` varchar(140),
`hospital` varchar(140),
`google_calendar` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`enable_free_follow_ups` int(1) not null default 0,
`max_visits` int(11) not null default 0,
`valid_days` int(11) not null default 0,
`default_currency` varchar(140),
`practitioner_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`practitioner_primary_address` varchar(140),
`primary_address` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner_name`(`practitioner_name`),
index `user_id`(`user_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:32,911 WARNING database DDL Query made to DB:
create table `tabExercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_type` varchar(140),
`difficulty_level` varchar(140),
`counts_target` int(11) not null default 0,
`counts_completed` int(11) not null default 0,
`assistance_level` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,012 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`score` varchar(140),
`time` time(6),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,191 WARNING database DDL Query made to DB:
create table `tabService Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`order_date` date,
`order_time` time(6),
`status` varchar(140),
`company` varchar(140),
`expected_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`source_doc` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`as_needed` int(1) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
`occurrence_date` date,
`occurrence_time` time(6),
`interval` decimal(21,9),
`healthcare_service_unit_type` varchar(140),
`order_description` text,
`patient_instructions` text,
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`source` varchar(140),
`referring_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group_doctype` varchar(140) default 'Patient Encounter',
`amended_from` varchar(140),
`template_dt` varchar(140),
`template_dn` varchar(140),
`sample_collection_required` int(1) not null default 0,
`qty_invoiced` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `template_dt`(`template_dt`),
index `template_dn`(`template_dn`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,320 WARNING database DDL Query made to DB:
create table `tabHealthcare Schedule Time Slot` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
`from_time` time(6),
`to_time` time(6),
`duration` decimal(21,9) not null default 0,
`maximum_appointments` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,433 WARNING database DDL Query made to DB:
create table `tabCode Value Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140) unique,
`value_set` varchar(140) unique,
`description` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,579 WARNING database DDL Query made to DB:
create table `tabNursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`date` date,
`user` varchar(140),
`company` varchar(140),
`service_unit` varchar(140),
`medical_department` varchar(140),
`status` varchar(140) default 'Draft',
`activity` varchar(140),
`mandatory` int(1) not null default 0,
`description` text,
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`requested_start_time` datetime(6),
`requested_end_time` datetime(6),
`duration` decimal(21,9),
`task_start_time` datetime(6),
`task_end_time` datetime(6),
`task_duration` decimal(21,9),
`reference_doctype` varchar(140),
`amended_from` varchar(140),
`reference_name` varchar(140),
`task_doctype` varchar(140),
`task_document_name` varchar(140),
`notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,729 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_unit_name` varchar(140),
`is_group` int(1) not null default 0,
`service_unit_type` varchar(140),
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`service_unit_capacity` int(11) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`occupancy_status` varchar(140),
`company` varchar(140),
`warehouse` varchar(140),
`parent_healthcare_service_unit` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `inpatient_occupancy`(`inpatient_occupancy`),
index `company`(`company`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:33,817 WARNING database DDL Query made to DB:
alter table `tabHealthcare Service Unit`
					add unique `unique_service_unit_company`(healthcare_service_unit_name, company)
2025-06-02 10:36:33,882 WARNING database DDL Query made to DB:
create table `tabMedication Linked Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`brand` varchar(140),
`manufacturer` varchar(140),
`description` text,
`is_billable` int(1) not null default 1,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,020 WARNING database DDL Query made to DB:
create table `tabDischarge Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'HLC-DS-.YYYY.-',
`inpatient_record` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
`discharge_practitioner` varchar(140),
`posting_date` date,
`company` varchar(140),
`status` varchar(140),
`followup_date` date,
`review_date` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`scheduled_date` date,
`admitted_datetime` datetime(6),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`physical_examination` longtext,
`treatment_done` longtext,
`advice_on_discharge` longtext,
`diet_adviced` longtext,
`current_issues` longtext,
`instructions` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,129 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`date` date,
`time` time(6),
`is_completed` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,226 WARNING database DDL Query made to DB:
create table `tabExercise Type Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`image` text,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,360 WARNING database DDL Query made to DB:
create table `tabTherapy Session` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`appointment` varchar(140),
`therapy_plan` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`gender` varchar(140),
`company` varchar(140),
`therapy_type` varchar(140),
`practitioner` varchar(140),
`department` varchar(140),
`duration` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`location` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`total_counts_targeted` int(11) not null default 0,
`total_counts_completed` int(11) not null default 0,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_request`(`service_request`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,559 WARNING database DDL Query made to DB:
create table `tabInpatient Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`dob` date,
`mobile` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Admission Scheduled',
`scheduled_date` date,
`admitted_datetime` datetime(6),
`expected_discharge` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`discharge_nursing_checklist_template` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_instruction` text,
`therapy_plan` varchar(140),
`currency` varchar(140),
`price_list` varchar(140),
`total` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`discharge_ordered_date` date,
`discharge_practitioner` varchar(140),
`discharge_encounter` varchar(140),
`discharge_datetime` datetime(6),
`discharge_instructions` text,
`followup_date` date,
`discharge_note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,674 WARNING database DDL Query made to DB:
create table `tabOrganism Test Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,797 WARNING database DDL Query made to DB:
create table `tabProcedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`service_request` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:34,919 WARNING database DDL Query made to DB:
create table `tabDiagnostic Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`age` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`ref_doctype` varchar(140),
`docname` varchar(140),
`reference_posting_date` date,
`sample_collection` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `sample_collection`(`sample_collection`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,043 WARNING database DDL Query made to DB:
create table `tabFee Validity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`patient` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`patient_appointment` varchar(140),
`sales_invoice_ref` varchar(140),
`max_visits` int(11) not null default 0,
`visited` int(11) not null default 0,
`start_date` date,
`valid_till` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner`(`practitioner`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,149 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_particulars` varchar(140),
`result_value` text,
`allow_blank` int(1) not null default 1,
`template` varchar(140),
`require_result_value` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,277 WARNING database DDL Query made to DB:
create table `tabCode Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140),
`experimental` int(1) not null default 0,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`code_value` varchar(140),
`value_set` varchar(140),
`display` text,
`status` varchar(140),
`version` varchar(140),
`level` int(10) not null default 0,
`definition` text,
`official_url` varchar(140),
`canonical_mapping` varchar(140),
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_system`(`code_system`),
index `system_uri`(`system_uri`),
index `code_value`(`code_value`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,411 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_name` varchar(140) unique,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`assessment_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,527 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,642 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`plan_name` varchar(140) unique,
`link_existing_item` int(1) not null default 0,
`linked_item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` text,
`total_sessions` int(11) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,808 WARNING database DDL Query made to DB:
create table `tabObservation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation` varchar(140) unique,
`observation_category` varchar(140),
`preferred_display_name` varchar(140),
`abbr` varchar(140),
`has_component` int(1) not null default 0,
`medical_department` varchar(140),
`description` longtext,
`method` varchar(140),
`method_value` varchar(140),
`service_unit` varchar(140),
`result_template` varchar(140),
`interpretation_template` varchar(140),
`permitted_data_type` varchar(140),
`permitted_unit` varchar(140),
`options` text,
`template` varchar(140),
`sample_collection_required` int(1) not null default 0,
`sample` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`sample_type` varchar(140),
`container_closure_color` varchar(140),
`sample_details` text,
`is_billable` int(1) not null default 0,
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_billable`(`is_billable`),
index `item`(`item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:35,955 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`disabled` int(1) not null default 0,
`pre_op_nursing_checklist_template` varchar(140),
`post_op_nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`consume_stock` int(1) not null default 0,
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample` varchar(140),
`sample_details` text,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `consume_stock`(`consume_stock`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,096 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient_encounter` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`status` varchar(140),
`practitioner` varchar(140),
`start_date` date,
`end_date` date,
`total_orders` decimal(21,9) not null default 0,
`completed_orders` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,242 WARNING database DDL Query made to DB:
create table `tabMedication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`generic_name` varchar(140) unique,
`medication_class` varchar(140),
`abbr` varchar(140),
`national_drug_code` varchar(140),
`is_combination` int(1) not null default 0,
`disabled` int(1) not null default 0,
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`price_list` varchar(140),
`default_prescription_dosage` varchar(140),
`default_prescription_duration` varchar(140),
`dosage_form` varchar(140),
`default_interval` int(11) not null default 0,
`default_interval_uom` varchar(140),
`reference_url` longtext,
`change_in_item` int(1) not null default 0,
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,365 WARNING database DDL Query made to DB:
create table `tabInpatient Record Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` decimal(21,9) not null default 0,
`uom` varchar(140),
`invoiced` int(1) not null default 0,
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,467 WARNING database DDL Query made to DB:
create table `tabObservation Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`abbr` varchar(140),
`condition` longtext,
`based_on_formula` int(1) not null default 0,
`formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,596 WARNING database DDL Query made to DB:
create table `tabTherapy Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`therapy_type` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`default_duration` int(11) not null default 0,
`healthcare_service_unit` varchar(140),
`nursing_checklist_template` varchar(140),
`item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,793 WARNING database DDL Query made to DB:
create table `tabPatient Appointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`status` varchar(140),
`appointment_type` varchar(140),
`appointment_for` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`service_unit` varchar(140),
`appointment_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`duration` int(11) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`service_request` varchar(140),
`procedure_template` varchar(140),
`procedure_prescription` varchar(140),
`therapy_plan` varchar(140),
`therapy_type` varchar(140),
`appointment_time` time(6),
`appointment_datetime` datetime(6),
`add_video_conferencing` int(1) not null default 0,
`event` varchar(140),
`google_meet_link` varchar(140),
`mode_of_payment` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`ref_sales_invoice` varchar(140),
`referring_practitioner` varchar(140),
`position_in_queue` int(11) not null default 0,
`appointment_based_on_check_in` int(1) not null default 0,
`reminded` int(1) not null default 0,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `practitioner`(`practitioner`),
index `department`(`department`),
index `appointment_date`(`appointment_date`),
index `patient`(`patient`),
index `service_request`(`service_request`),
index `appointment_datetime`(`appointment_datetime`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:36,918 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`particulars` varchar(140),
`allow_blank` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,010 WARNING database DDL Query made to DB:
create table `tabPatient History Custom Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`date_fieldname` varchar(140),
`selected_fields` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,130 WARNING database DDL Query made to DB:
create table `tabDrug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`intent` varchar(140),
`priority` varchar(140),
`medication_request` varchar(140),
`comment` text,
`update_schedule` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,248 WARNING database DDL Query made to DB:
create table `tabABDM Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`default` int(1) not null default 0,
`company` varchar(140),
`auth_base_url` varchar(140),
`client_id` varchar(140),
`client_secret` varchar(140),
`health_id_base_url` varchar(140),
`consent_base_url` varchar(140),
`patient_aadhaar_consent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,472 WARNING database DDL Query made to DB:
create table `tabLab Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`template` varchar(140),
`lab_test_name` varchar(140),
`lab_test_group` varchar(140),
`department` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`time` time(6),
`submitted_date` datetime(6),
`result_date` date,
`approved_date` datetime(6),
`expected_result_date` date,
`expected_result_time` time(6),
`printed_on` datetime(6),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`inpatient_record` varchar(140),
`report_preference` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`service_unit` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`requesting_department` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`employee_designation` varchar(140),
`user` varchar(140),
`sample` varchar(140),
`descriptive_result` longtext,
`lab_test_comment` text,
`custom_result` longtext,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`email_sent` int(1) not null default 0,
`sms_sent` int(1) not null default 0,
`printed` int(1) not null default 0,
`normal_toggle` int(1) not null default 0,
`imaging_toggle` int(1) not null default 0,
`descriptive_toggle` int(1) not null default 0,
`sensitivity_toggle` int(1) not null default 0,
`amended_from` varchar(140),
`prescription` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `lab_test_name`(`lab_test_name`),
index `department`(`department`),
index `status`(`status`),
index `result_date`(`result_date`),
index `patient`(`patient`),
index `mobile`(`mobile`),
index `practitioner`(`practitioner`),
index `service_request`(`service_request`),
index `invoiced`(`invoiced`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,651 WARNING database DDL Query made to DB:
create table `tabSample Collection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`referring_practitioner` varchar(140),
`company` varchar(140),
`status` varchar(140),
`inpatient_record` varchar(140),
`collection_point` varchar(140),
`invoiced` int(1) not null default 0,
`sample` varchar(140),
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`collected_by` varchar(140),
`collected_time` datetime(6),
`num_print` int(11) not null default 1,
`sample_details` longtext,
`service_request` varchar(140),
`reference_doc` varchar(140),
`reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `patient`(`patient`),
index `invoiced`(`invoiced`),
index `sample`(`sample`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,812 WARNING database DDL Query made to DB:
create table `tabCode System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`code_system` varchar(140) unique,
`uri` varchar(140),
`description` text,
`status` varchar(140),
`version` varchar(140),
`is_fhir_defined` int(1) not null default 1,
`oid` varchar(140) unique,
`experimental` int(1) not null default 1,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `disabled`(`disabled`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:37,977 WARNING database DDL Query made to DB:
create table `tabClinical Procedure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`procedure_template` varchar(140),
`company` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`notes` text,
`status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`sample` varchar(140),
`consume_stock` int(1) not null default 0,
`warehouse` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`consumption_invoiced` int(1) not null default 0,
`consumable_total_amount` decimal(21,9) not null default 0,
`consumption_details` text,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`prescription` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,095 WARNING database DDL Query made to DB:
create table `tabMedication Ingredient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,214 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`service_unit_type` varchar(140) unique,
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`is_billable` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`uom` varchar(140),
`no_of_hours` int(11) not null default 0,
`minimum_billable_qty` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`description` text,
`change_in_item` int(1) not null default 0,
`is_ot` int(1) not null default 0,
`medical_department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:38,332 WARNING database DDL Query made to DB:
create table `tabSpecimen` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specimen_type` varchar(140),
`received_time` datetime(6),
`status` varchar(140),
`barcode` longtext,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_gender` varchar(140),
`note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
