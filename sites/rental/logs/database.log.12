2025-06-02 11:04:53,948 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,049 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) not null default 0,
`expired_leaves` decimal(21,9) not null default 0,
`used_leaves` decimal(21,9) not null default 0,
`pending_leaves` decimal(21,9) not null default 0,
`available_leaves` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,160 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) not null default 0,
`remaining_benefit` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`pro_rata_dispensed_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,321 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140) unique,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` int(1) not null default 1,
`is_tax_applicable` int(1) not null default 1,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`is_income_tax_component` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`round_to_the_nearest_integer` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`remove_if_zero_valued` int(1) not null default 1,
`disabled` int(1) not null default 0,
`condition` longtext,
`amount` decimal(21,9) not null default 0,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`is_flexible_benefit` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`only_tax_impact` int(1) not null default 0,
`create_separate_payment_entry_against_benefit_claim` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,452 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,570 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,667 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,758 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:54,875 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:04:56,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-06-02 11:04:56,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:04:56,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` int(1) not null default 1
2025-06-02 11:04:56,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-06-02 11:04:56,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0
2025-06-02 11:04:56,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-06-02 11:04:56,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-06-02 11:04:56,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0
2025-06-02 11:04:56,476 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-06-02 11:04:56,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-06-02 11:04:56,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-02 11:04:56,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-06-02 11:04:56,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `base_total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_billed_hours` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_billable_hours` decimal(21,9) not null default 0, MODIFY `base_total_billed_amount` decimal(21,9) not null default 0, MODIFY `base_total_costing_amount` decimal(21,9) not null default 0
2025-06-02 11:05:23,702 WARNING database DDL Query made to DB:
create table `tabPatient History Standard Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`date_fieldname` varchar(140),
`selected_fields` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:23,924 WARNING database DDL Query made to DB:
create table `tabVital Signs` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`appointment` varchar(140),
`encounter` varchar(140),
`company` varchar(140),
`signs_date` date,
`signs_time` time(6),
`temperature` varchar(140),
`pulse` varchar(140),
`respiratory_rate` varchar(140),
`tongue` varchar(140),
`abdomen` varchar(140),
`reflexes` varchar(140),
`bp_systolic` varchar(140),
`bp_diastolic` varchar(140),
`bp` varchar(140),
`vital_signs_note` text,
`height` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`bmi` decimal(21,9) not null default 0,
`nutrition_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,080 WARNING database DDL Query made to DB:
create table `tabLab Test Sample` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140),
`sample` varchar(140) unique,
`container_closure_color` varchar(140),
`sample_uom` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,247 WARNING database DDL Query made to DB:
create table `tabHealthcare Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`practitioner_name` varchar(140),
`gender` varchar(140),
`image` text,
`status` varchar(140) default 'Active',
`mobile_phone` varchar(140),
`residence_phone` varchar(140),
`office_phone` varchar(140),
`practitioner_type` varchar(140) default 'Internal',
`employee` varchar(140),
`supplier` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`user_id` varchar(140),
`hospital` varchar(140),
`google_calendar` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`enable_free_follow_ups` int(1) not null default 0,
`max_visits` int(11) not null default 0,
`valid_days` int(11) not null default 0,
`default_currency` varchar(140),
`practitioner_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`practitioner_primary_address` varchar(140),
`primary_address` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner_name`(`practitioner_name`),
index `user_id`(`user_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,386 WARNING database DDL Query made to DB:
create table `tabExercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_type` varchar(140),
`difficulty_level` varchar(140),
`counts_target` int(11) not null default 0,
`counts_completed` int(11) not null default 0,
`assistance_level` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,535 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parameter` varchar(140),
`score` varchar(140),
`time` time(6),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,738 WARNING database DDL Query made to DB:
create table `tabService Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`order_date` date,
`order_time` time(6),
`status` varchar(140),
`company` varchar(140),
`expected_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`source_doc` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`as_needed` int(1) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
`occurrence_date` date,
`occurrence_time` time(6),
`interval` decimal(21,9),
`healthcare_service_unit_type` varchar(140),
`order_description` text,
`patient_instructions` text,
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`source` varchar(140),
`referring_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group_doctype` varchar(140) default 'Patient Encounter',
`amended_from` varchar(140),
`template_dt` varchar(140),
`template_dn` varchar(140),
`sample_collection_required` int(1) not null default 0,
`qty_invoiced` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `template_dt`(`template_dt`),
index `template_dn`(`template_dn`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:24,904 WARNING database DDL Query made to DB:
create table `tabHealthcare Schedule Time Slot` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
`from_time` time(6),
`to_time` time(6),
`duration` decimal(21,9) not null default 0,
`maximum_appointments` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,034 WARNING database DDL Query made to DB:
create table `tabCode Value Set` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140) unique,
`value_set` varchar(140) unique,
`description` text,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,208 WARNING database DDL Query made to DB:
create table `tabNursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`date` date,
`user` varchar(140),
`company` varchar(140),
`service_unit` varchar(140),
`medical_department` varchar(140),
`status` varchar(140) default 'Draft',
`activity` varchar(140),
`mandatory` int(1) not null default 0,
`description` text,
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`requested_start_time` datetime(6),
`requested_end_time` datetime(6),
`duration` decimal(21,9),
`task_start_time` datetime(6),
`task_end_time` datetime(6),
`task_duration` decimal(21,9),
`reference_doctype` varchar(140),
`amended_from` varchar(140),
`reference_name` varchar(140),
`task_doctype` varchar(140),
`task_document_name` varchar(140),
`notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,423 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_unit_name` varchar(140),
`is_group` int(1) not null default 0,
`service_unit_type` varchar(140),
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`service_unit_capacity` int(11) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`occupancy_status` varchar(140),
`company` varchar(140),
`warehouse` varchar(140),
`parent_healthcare_service_unit` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `inpatient_occupancy`(`inpatient_occupancy`),
index `company`(`company`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,567 WARNING database DDL Query made to DB:
alter table `tabHealthcare Service Unit`
					add unique `unique_service_unit_company`(healthcare_service_unit_name, company)
2025-06-02 11:05:25,643 WARNING database DDL Query made to DB:
create table `tabMedication Linked Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`brand` varchar(140),
`manufacturer` varchar(140),
`description` text,
`is_billable` int(1) not null default 1,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,814 WARNING database DDL Query made to DB:
create table `tabDischarge Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'HLC-DS-.YYYY.-',
`inpatient_record` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
`discharge_practitioner` varchar(140),
`posting_date` date,
`company` varchar(140),
`status` varchar(140),
`followup_date` date,
`review_date` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`scheduled_date` date,
`admitted_datetime` datetime(6),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`physical_examination` longtext,
`treatment_done` longtext,
`advice_on_discharge` longtext,
`diet_adviced` longtext,
`current_issues` longtext,
`instructions` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:25,952 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`date` date,
`time` time(6),
`is_completed` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,072 WARNING database DDL Query made to DB:
create table `tabExercise Type Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`image` text,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,240 WARNING database DDL Query made to DB:
create table `tabTherapy Session` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`appointment` varchar(140),
`therapy_plan` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`gender` varchar(140),
`company` varchar(140),
`therapy_type` varchar(140),
`practitioner` varchar(140),
`department` varchar(140),
`duration` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`location` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`total_counts_targeted` int(11) not null default 0,
`total_counts_completed` int(11) not null default 0,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_request`(`service_request`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,491 WARNING database DDL Query made to DB:
create table `tabInpatient Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`dob` date,
`mobile` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Admission Scheduled',
`scheduled_date` date,
`admitted_datetime` datetime(6),
`expected_discharge` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`discharge_nursing_checklist_template` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_instruction` text,
`therapy_plan` varchar(140),
`currency` varchar(140),
`price_list` varchar(140),
`total` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`discharge_ordered_date` date,
`discharge_practitioner` varchar(140),
`discharge_encounter` varchar(140),
`discharge_datetime` datetime(6),
`discharge_instructions` text,
`followup_date` date,
`discharge_note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,631 WARNING database DDL Query made to DB:
create table `tabOrganism Test Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,758 WARNING database DDL Query made to DB:
create table `tabProcedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`service_request` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,878 WARNING database DDL Query made to DB:
create table `tabDiagnostic Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`age` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`ref_doctype` varchar(140),
`docname` varchar(140),
`reference_posting_date` date,
`sample_collection` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `sample_collection`(`sample_collection`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:26,990 WARNING database DDL Query made to DB:
create table `tabFee Validity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`patient` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`patient_appointment` varchar(140),
`sales_invoice_ref` varchar(140),
`max_visits` int(11) not null default 0,
`visited` int(11) not null default 0,
`start_date` date,
`valid_till` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner`(`practitioner`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,082 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_particulars` varchar(140),
`result_value` text,
`allow_blank` int(1) not null default 1,
`template` varchar(140),
`require_result_value` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,210 WARNING database DDL Query made to DB:
create table `tabCode Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140),
`experimental` int(1) not null default 0,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`code_value` varchar(140),
`value_set` varchar(140),
`display` text,
`status` varchar(140),
`version` varchar(140),
`level` int(10) not null default 0,
`definition` text,
`official_url` varchar(140),
`canonical_mapping` varchar(140),
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_system`(`code_system`),
index `system_uri`(`system_uri`),
index `code_value`(`code_value`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,323 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_name` varchar(140) unique,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`assessment_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,428 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,540 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`plan_name` varchar(140) unique,
`link_existing_item` int(1) not null default 0,
`linked_item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` text,
`total_sessions` int(11) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,719 WARNING database DDL Query made to DB:
create table `tabObservation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation` varchar(140) unique,
`observation_category` varchar(140),
`preferred_display_name` varchar(140),
`abbr` varchar(140),
`has_component` int(1) not null default 0,
`medical_department` varchar(140),
`description` longtext,
`method` varchar(140),
`method_value` varchar(140),
`service_unit` varchar(140),
`result_template` varchar(140),
`interpretation_template` varchar(140),
`permitted_data_type` varchar(140),
`permitted_unit` varchar(140),
`options` text,
`template` varchar(140),
`sample_collection_required` int(1) not null default 0,
`sample` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`sample_type` varchar(140),
`container_closure_color` varchar(140),
`sample_details` text,
`is_billable` int(1) not null default 0,
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_billable`(`is_billable`),
index `item`(`item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:27,886 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`disabled` int(1) not null default 0,
`pre_op_nursing_checklist_template` varchar(140),
`post_op_nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`consume_stock` int(1) not null default 0,
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample` varchar(140),
`sample_details` text,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `consume_stock`(`consume_stock`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,022 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient_encounter` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`status` varchar(140),
`practitioner` varchar(140),
`start_date` date,
`end_date` date,
`total_orders` decimal(21,9) not null default 0,
`completed_orders` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,141 WARNING database DDL Query made to DB:
create table `tabMedication` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`generic_name` varchar(140) unique,
`medication_class` varchar(140),
`abbr` varchar(140),
`national_drug_code` varchar(140),
`is_combination` int(1) not null default 0,
`disabled` int(1) not null default 0,
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`price_list` varchar(140),
`default_prescription_dosage` varchar(140),
`default_prescription_duration` varchar(140),
`dosage_form` varchar(140),
`default_interval` int(11) not null default 0,
`default_interval_uom` varchar(140),
`reference_url` longtext,
`change_in_item` int(1) not null default 0,
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,265 WARNING database DDL Query made to DB:
create table `tabInpatient Record Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` decimal(21,9) not null default 0,
`uom` varchar(140),
`invoiced` int(1) not null default 0,
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`stock_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,363 WARNING database DDL Query made to DB:
create table `tabObservation Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`abbr` varchar(140),
`condition` longtext,
`based_on_formula` int(1) not null default 0,
`formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,498 WARNING database DDL Query made to DB:
create table `tabTherapy Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`therapy_type` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`default_duration` int(11) not null default 0,
`healthcare_service_unit` varchar(140),
`nursing_checklist_template` varchar(140),
`item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,691 WARNING database DDL Query made to DB:
create table `tabPatient Appointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`status` varchar(140),
`appointment_type` varchar(140),
`appointment_for` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`service_unit` varchar(140),
`appointment_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`duration` int(11) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`service_request` varchar(140),
`procedure_template` varchar(140),
`procedure_prescription` varchar(140),
`therapy_plan` varchar(140),
`therapy_type` varchar(140),
`appointment_time` time(6),
`appointment_datetime` datetime(6),
`add_video_conferencing` int(1) not null default 0,
`event` varchar(140),
`google_meet_link` varchar(140),
`mode_of_payment` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`ref_sales_invoice` varchar(140),
`referring_practitioner` varchar(140),
`position_in_queue` int(11) not null default 0,
`appointment_based_on_check_in` int(1) not null default 0,
`reminded` int(1) not null default 0,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `practitioner`(`practitioner`),
index `department`(`department`),
index `appointment_date`(`appointment_date`),
index `patient`(`patient`),
index `service_request`(`service_request`),
index `appointment_datetime`(`appointment_datetime`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,810 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`particulars` varchar(140),
`allow_blank` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,898 WARNING database DDL Query made to DB:
create table `tabPatient History Custom Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`date_fieldname` varchar(140),
`selected_fields` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:28,998 WARNING database DDL Query made to DB:
create table `tabDrug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`intent` varchar(140),
`priority` varchar(140),
`medication_request` varchar(140),
`comment` text,
`update_schedule` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,102 WARNING database DDL Query made to DB:
create table `tabABDM Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`default` int(1) not null default 0,
`company` varchar(140),
`auth_base_url` varchar(140),
`client_id` varchar(140),
`client_secret` varchar(140),
`health_id_base_url` varchar(140),
`consent_base_url` varchar(140),
`patient_aadhaar_consent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,312 WARNING database DDL Query made to DB:
create table `tabLab Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`template` varchar(140),
`lab_test_name` varchar(140),
`lab_test_group` varchar(140),
`department` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`time` time(6),
`submitted_date` datetime(6),
`result_date` date,
`approved_date` datetime(6),
`expected_result_date` date,
`expected_result_time` time(6),
`printed_on` datetime(6),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`inpatient_record` varchar(140),
`report_preference` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`service_unit` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`requesting_department` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`employee_designation` varchar(140),
`user` varchar(140),
`sample` varchar(140),
`descriptive_result` longtext,
`lab_test_comment` text,
`custom_result` longtext,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`email_sent` int(1) not null default 0,
`sms_sent` int(1) not null default 0,
`printed` int(1) not null default 0,
`normal_toggle` int(1) not null default 0,
`imaging_toggle` int(1) not null default 0,
`descriptive_toggle` int(1) not null default 0,
`sensitivity_toggle` int(1) not null default 0,
`amended_from` varchar(140),
`prescription` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `lab_test_name`(`lab_test_name`),
index `department`(`department`),
index `status`(`status`),
index `result_date`(`result_date`),
index `patient`(`patient`),
index `mobile`(`mobile`),
index `practitioner`(`practitioner`),
index `service_request`(`service_request`),
index `invoiced`(`invoiced`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,482 WARNING database DDL Query made to DB:
create table `tabSample Collection` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`referring_practitioner` varchar(140),
`company` varchar(140),
`status` varchar(140),
`inpatient_record` varchar(140),
`collection_point` varchar(140),
`invoiced` int(1) not null default 0,
`sample` varchar(140),
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`collected_by` varchar(140),
`collected_time` datetime(6),
`num_print` int(11) not null default 1,
`sample_details` longtext,
`service_request` varchar(140),
`reference_doc` varchar(140),
`reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `patient`(`patient`),
index `invoiced`(`invoiced`),
index `sample`(`sample`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,630 WARNING database DDL Query made to DB:
create table `tabCode System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`code_system` varchar(140) unique,
`uri` varchar(140),
`description` text,
`status` varchar(140),
`version` varchar(140),
`is_fhir_defined` int(1) not null default 1,
`oid` varchar(140) unique,
`experimental` int(1) not null default 1,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `disabled`(`disabled`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,788 WARNING database DDL Query made to DB:
create table `tabClinical Procedure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`procedure_template` varchar(140),
`company` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`notes` text,
`status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`sample` varchar(140),
`consume_stock` int(1) not null default 0,
`warehouse` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`consumption_invoiced` int(1) not null default 0,
`consumable_total_amount` decimal(21,9) not null default 0,
`consumption_details` text,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`prescription` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:29,892 WARNING database DDL Query made to DB:
create table `tabMedication Ingredient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,004 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`service_unit_type` varchar(140) unique,
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`is_billable` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`uom` varchar(140),
`no_of_hours` int(11) not null default 0,
`minimum_billable_qty` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`description` text,
`change_in_item` int(1) not null default 0,
`is_ot` int(1) not null default 0,
`medical_department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,109 WARNING database DDL Query made to DB:
create table `tabSpecimen` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`specimen_type` varchar(140),
`received_time` datetime(6),
`status` varchar(140),
`barcode` longtext,
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_gender` varchar(140),
`note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,200 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Symptom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaint` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,320 WARNING database DDL Query made to DB:
create table `tabBody Part Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,431 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`template` varchar(140),
`qty` int(11) not null default 1,
`amount` decimal(21,9) not null default 0,
`service_request` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`instructions` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,522 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`interval` decimal(21,9),
`sessions_completed` int(11) not null default 0,
`service_request` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,607 WARNING database DDL Query made to DB:
create table `tabPrescription Duration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number` int(11) not null default 0,
`period` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,710 WARNING database DDL Query made to DB:
create table `tabDosage Strength` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`strength` decimal(21,9) not null default 0,
`strength_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,811 WARNING database DDL Query made to DB:
create table `tabLab Test UOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_uom` varchar(140) unique,
`uom_description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:30,912 WARNING database DDL Query made to DB:
create table `tabAppointment Type Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`dn` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,013 WARNING database DDL Query made to DB:
create table `tabAppointment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment_type` varchar(140) unique,
`default_duration` int(11) not null default 0,
`allow_booking_for` varchar(140) default 'Practitioner',
`color` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,133 WARNING database DDL Query made to DB:
create table `tabClinical Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`clinical_note_type` varchar(140),
`terms_and_conditions` varchar(140),
`posting_date` datetime(6),
`practitioner` varchar(140),
`user` varchar(140),
`note` longtext,
`reference_doc` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,227 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,391 WARNING database DDL Query made to DB:
create table `tabLab Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_name` varchar(140) unique,
`department` varchar(140),
`disabled` int(1) not null default 0,
`nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`lab_test_code` varchar(140),
`lab_test_group` varchar(140),
`is_billable` int(1) not null default 1,
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` longtext,
`lab_test_template_type` varchar(140),
`descriptive_result` longtext,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`lab_test_normal_range` longtext,
`sensitivity` int(1) not null default 0,
`sample` varchar(140),
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample_details` text,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`change_in_item` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item`(`item`),
index `lab_test_group`(`lab_test_group`),
index `is_billable`(`is_billable`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,514 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`description` text,
`medical_department` varchar(140),
`disabled` int(1) not null default 0,
`goal` text,
`order_group` varchar(140),
`patient_age_from` int(11) not null default 0,
`patient_age_to` int(11) not null default 0,
`gender` varchar(140),
`is_inpatient` int(1) not null default 0,
`treatment_counselling_required_for_ip` int(1) not null default 0,
`healthcare_service_unit_type` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,616 WARNING database DDL Query made to DB:
create table `tabOrganism Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,718 WARNING database DDL Query made to DB:
create table `tabPatient Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`therapy_session` varchar(140),
`patient` varchar(140),
`assessment_template` varchar(140),
`company` varchar(140),
`healthcare_practitioner` varchar(140),
`assessment_datetime` datetime(6),
`assessment_description` text,
`total_score_obtained` int(11) not null default 0,
`total_score` int(11) not null default 0,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,811 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:31,919 WARNING database DDL Query made to DB:
create table `tabDiagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140) unique,
`estimated_duration` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,100 WARNING database DDL Query made to DB:
create table `tabPatient Encounter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`appointment_type` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`company` varchar(140),
`status` varchar(140),
`encounter_date` date,
`encounter_time` time(6),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`google_meet_link` varchar(140),
`invoiced` int(1) not null default 0,
`submit_orders_on_save` int(1) not null default 0,
`symptoms_in_print` int(1) not null default 0,
`diagnosis_in_print` int(1) not null default 1,
`therapy_plan` varchar(140),
`encounter_comment` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `appointment`(`appointment`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,204 WARNING database DDL Query made to DB:
create sequence if not exists observation_sample_collection_id_seq nocache nocycle
2025-06-02 11:05:32,224 WARNING database DDL Query made to DB:
create table `tabObservation Sample Collection` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`has_component` int(1) not null default 0,
`sample` varchar(140) default 'Urine',
`sample_type` varchar(140),
`uom` varchar(140),
`status` varchar(140),
`container_closure_color` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`collection_date_time` datetime(6),
`collection_point` varchar(140),
`collected_user` varchar(140),
`collected_by` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`reference_child` varchar(140),
`service_request` varchar(140),
`specimen` varchar(140),
`component_observation_parent` varchar(140),
`component_observations` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,311 WARNING database DDL Query made to DB:
create table `tabFee Validity Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,416 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140),
`item_code` varchar(140),
`assigned_to_practitioner` varchar(140),
`patient` varchar(140),
`practitioner` varchar(140),
`service_unit` varchar(140),
`from_date` date,
`to_date` date,
`from_time` time(6),
`to_time` time(6),
`update_stock` int(1) not null default 1,
`warehouse` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,582 WARNING database DDL Query made to DB:
create table `tabObservation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`observation_template` varchar(140),
`observation_category` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Registered',
`medical_department` varchar(140),
`amended_from` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`has_component` int(1) not null default 0,
`preferred_display_name` varchar(140),
`sample_collection_required` int(1) not null default 0,
`permitted_unit` varchar(140),
`sample` varchar(140),
`sample_type` varchar(140),
`permitted_data_type` varchar(140),
`method` varchar(140),
`specimen` varchar(140),
`sample_collection_time` datetime(6),
`sample_status` varchar(140),
`result_template` varchar(140),
`result_attach` text,
`result_boolean` varchar(140),
`result_data` varchar(140),
`result_text` longtext,
`result_float` decimal(21,9) not null default 0,
`result_select` varchar(140),
`result_datetime` datetime(6),
`result_time` datetime(6),
`result_period_from` datetime(6),
`result_period_to` datetime(6),
`options` text,
`time_of_result` datetime(6),
`time_of_approval` datetime(6),
`interpretation_template` varchar(140),
`result_interpretation` longtext,
`observation_method` varchar(140),
`reference` text,
`note` longtext,
`description` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice_status` varchar(140),
`sales_invoice_item` varchar(140),
`service_request` varchar(140),
`disapproval_reason` text,
`parent_observation` varchar(140),
`observation_idx` int(11) not null default 0,
`days` int(11) not null default 0,
`invoiced` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,724 WARNING database DDL Query made to DB:
create table `tabExercise Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_name` varchar(140),
`difficulty_level` varchar(140),
`description` longtext,
`exercise_steps` text,
`exercise_video` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,829 WARNING database DDL Query made to DB:
create table `tabComplaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaints` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:32,929 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`interval` decimal(21,9),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,026 WARNING database DDL Query made to DB:
create table `tabService Request Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,164 WARNING database DDL Query made to DB:
create table `tabTreatment Counselling` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`company` varchar(140),
`status` varchar(140),
`gender` varchar(140),
`patient_age` varchar(140),
`treatment_plan_template` varchar(140),
`price_list` varchar(140),
`amount` decimal(21,9) not null default 0,
`outstanding_amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`encounter_status` varchar(140),
`medical_department` varchar(140),
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`admission_instruction` text,
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`admission_encounter` varchar(140),
`referring_practitioner` varchar(140),
`inpatient_record` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,255 WARNING database DDL Query made to DB:
create table `tabClinical Note Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clinical_note_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,450 WARNING database DDL Query made to DB:
create table `tabPatient Care Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient_care_type` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,582 WARNING database DDL Query made to DB:
create table `tabPatient Relation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`relation` varchar(140),
`description` text,
index `relation`(`relation`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,683 WARNING database DDL Query made to DB:
create table `tabService Request Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_request_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:33,994 WARNING database DDL Query made to DB:
create table `tabMedication Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`medication` varchar(140),
`medication_item` varchar(140),
`order_date` date,
`order_time` time(6),
`expected_date` date,
`company` varchar(140),
`status` varchar(140) default 'draft-Medication Request Status',
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`dosage` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`order_description` text,
`period` varchar(140),
`occurrence_time` time(6),
`total_dispensable_quantity` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`qty_invoiced` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `medication`(`medication`),
index `status`(`status`),
index `patient`(`patient`),
index `patient_email`(`patient_email`),
index `patient_mobile`(`patient_mobile`),
index `inpatient_record`(`inpatient_record`),
index `practitioner`(`practitioner`),
index `staff_role`(`staff_role`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,114 WARNING database DDL Query made to DB:
create table `tabNormal Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_event` varchar(140),
`allow_blank` int(1) not null default 0,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,205 WARNING database DDL Query made to DB:
create table `tabAntibiotic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic_name` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,343 WARNING database DDL Query made to DB:
create table `tabPatient Medical Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`status` varchar(140),
`attach` text,
`subject` longtext,
`communication_date` date,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `patient`(`patient`),
index `reference_doctype`(`reference_doctype`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,443 WARNING database DDL Query made to DB:
create table `tabSensitivity Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic` varchar(140),
`antibiotic_sensitivity` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,540 WARNING database DDL Query made to DB:
create table `tabPractitioner Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`allow_video_conferencing` int(1) not null default 0,
`schedule_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,660 WARNING database DDL Query made to DB:
create table `tabMedical Department` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`department` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,828 WARNING database DDL Query made to DB:
create table `tabCodification Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system` varchar(140),
`is_fhir_defined` int(1) not null default 0,
`oid` varchar(140),
`code_value` varchar(140),
`code` varchar(140),
`display` varchar(140),
`definition` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:34,950 WARNING database DDL Query made to DB:
create table `tabSample Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,051 WARNING database DDL Query made to DB:
create table `tabHealthcare Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140) unique,
`description` text,
`activity_duration` decimal(21,9),
`role` varchar(140),
`task_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,238 WARNING database DDL Query made to DB:
create table `tabPatient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`sex` varchar(140),
`blood_group` varchar(140),
`dob` date,
`image` text,
`status` varchar(140),
`uid` varchar(140) unique,
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`report_preference` varchar(140),
`mobile` varchar(140),
`phone` varchar(140),
`email` varchar(140),
`invite_user` int(1) not null default 1,
`user_id` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`default_currency` varchar(140),
`default_price_list` varchar(140),
`language` varchar(140),
`patient_details` text,
`occupation` varchar(140),
`marital_status` varchar(140),
`allergies` text,
`medication` text,
`medical_history` text,
`surgical_history` text,
`tobacco_past_use` varchar(140),
`tobacco_current_use` varchar(140),
`alcohol_past_use` varchar(140),
`alcohol_current_use` varchar(140),
`surrounding_factors` text,
`other_risk_factors` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_name`(`patient_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,356 WARNING database DDL Query made to DB:
create table `tabSensitivity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sensitivity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,458 WARNING database DDL Query made to DB:
create table `tabExercise Difficulty Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`difficulty_level` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,604 WARNING database DDL Query made to DB:
create table `tabService Unit Type Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`is_stock_item` int(1) not null default 0,
`billing_type` varchar(140),
`charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,718 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`title` varchar(140) unique,
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,836 WARNING database DDL Query made to DB:
create table `tabPrescription Dosage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dosage` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:35,983 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`barcode` varchar(140),
`uom` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`batch_no` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `actual_qty`(`actual_qty`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:36,077 WARNING database DDL Query made to DB:
create table `tabPractitioner Service Unit Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule` varchar(140),
`service_unit` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:36,181 WARNING database DDL Query made to DB:
create table `tabLab Test Group Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_or_new_line` varchar(140) default 'Add Test',
`lab_test_template` varchar(140),
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` varchar(140),
`group_event` varchar(140),
`group_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`allow_blank` int(1) not null default 0,
`group_test_normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:05:36,280 WARNING database DDL Query made to DB:
create table `tabBody Part` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
