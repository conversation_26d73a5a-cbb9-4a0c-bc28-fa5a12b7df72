2025-06-02 09:51:03,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-02 09:51:05,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `card_no` varchar(140), ADD COLUMN `insurance_card_detail` text, ADD COLUMN `product_code` varchar(140), ADD COLUMN `membership_no` varchar(140), ADD COLUMN `scheme_id` varchar(140), ADD COLUMN `national_id` varchar(140) unique, ADD COLUMN `insurance_provider` varchar(140), ADD COLUMN `common_occupation` varchar(140), ADD COLUMN `ethnicity` varchar(140), ADD COLUMN `nida_card_number` varchar(140), ADD COLUMN `area` varchar(140), ADD COLUMN `demography` varchar(140), ADD COLUMN `how_did_you_hear_about_us` varchar(140), ADD COLUMN `referred_from` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `patient_signature` longtext, ADD COLUMN `cash_limit` decimal(21,9) not null default 0, ADD COLUMN `next_to_kin_name` varchar(140), ADD COLUMN `next_to_kin_mobile_no` varchar(140), ADD COLUMN `next_to_kin_relationship` varchar(140), ADD COLUMN `patient_details_with_formatting` longtext
2025-06-02 09:51:05,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD UNIQUE INDEX IF NOT EXISTS national_id (`national_id`)
2025-06-02 09:51:05,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 09:51:07,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `mobile` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `ref_vital_signs` varchar(140), ADD COLUMN `ref_patient_encounter` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `has_no_consultation_charges` int(1) not null default 0, ADD COLUMN `healthcare_referrer_type` varchar(140), ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `referral_no` varchar(140), ADD COLUMN `remarks` text, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `coverage_plan_card_number` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `nhif_employer_name` varchar(140), ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `nhif_patient_claim` varchar(140), ADD COLUMN `jubilee_patient_claim` varchar(140), ADD COLUMN `authorization_number` varchar(140), ADD COLUMN `years_of_insurance` int(11) not null default 0, ADD COLUMN `apply_fasttrack_charge` int(1) not null default 0, ADD COLUMN `insurance_company_name` varchar(140), ADD COLUMN `require_fingerprint` int(1) not null default 0, ADD COLUMN `require_facial_recognation` int(1) not null default 0, ADD COLUMN `biometric_method` varchar(140), ADD COLUMN `fpcode` varchar(140), ADD COLUMN `payment_reference` varchar(140), ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `old_hms_number` varchar(140), ADD COLUMN `patient_image2` text, ADD COLUMN `sms_sent` int(1) not null default 0
2025-06-02 09:51:07,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 09:51:08,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `override_insurance_subscription` int(1) not null default 0, ADD COLUMN `hso_payment_method` varchar(140), ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 09:51:12,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `insurance_coverage_plan` varchar(140), ADD COLUMN `image` text, ADD COLUMN `blood_group` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `admission_service_unit_type` varchar(140), ADD COLUMN `encounter_category` varchar(140), ADD COLUMN `encounter_mode_of_payment` varchar(140), ADD COLUMN `sales_invoice` varchar(140), ADD COLUMN `price_list` varchar(140), ADD COLUMN `abbr` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `hms_tz_previous_examination_detail` longtext, ADD COLUMN `examination_detail` longtext, ADD COLUMN `encounter_type` varchar(140), ADD COLUMN `finalized` int(1) not null default 0, ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `lab_bundle` varchar(140), ADD COLUMN `default_healthcare_service_unit` varchar(140), ADD COLUMN `ed_reason_for_absence` varchar(140), ADD COLUMN `ed_addressed_to` text, ADD COLUMN `ed_no_of_days` int(11) not null default 0, ADD COLUMN `patient_signature` longtext, ADD COLUMN `healthcare_practitioner_signature` longtext, ADD COLUMN `previous_total` decimal(21,9) not null default 0, ADD COLUMN `current_total` decimal(21,9) not null default 0, ADD COLUMN `is_not_billable` int(1) not null default 0, ADD COLUMN `duplicated` int(1) not null default 0, ADD COLUMN `reference_encounter` varchar(140), ADD COLUMN `from_encounter` varchar(140), ADD COLUMN `has_preapproval` int(1) not null default 0
2025-06-02 09:51:13,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140)
2025-06-02 09:51:13,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 09:51:14,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` ADD COLUMN `image` text, ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `shm_coverage_plan_name` varchar(140), ADD COLUMN `patient_progress` varchar(140), ADD COLUMN `oxygen_saturation_spo2` varchar(140), ADD COLUMN `rbg` decimal(21,9) not null default 0, ADD COLUMN `visual_acuity_re` varchar(140), ADD COLUMN `visual_acuity_le` varchar(140), ADD COLUMN `intraocular_pressure_re` varchar(140), ADD COLUMN `intraocular_pressure_le` varchar(140), ADD COLUMN `eye_opening` varchar(140), ADD COLUMN `verbal_response` varchar(140), ADD COLUMN `motor_response` varchar(140), ADD COLUMN `height_in_cm` int(11) not null default 0
2025-06-02 09:51:14,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `height` decimal(21,9) not null default 0
2025-06-02 09:51:15,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `hms_tz_patient_age` varchar(140), ADD COLUMN `hms_tz_patient_sex` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `healthcare_practitioner_name` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `radiology_report` varchar(140), ADD COLUMN `radiology_report_details` longtext, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 10:03:10,390 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:03:11,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 10:03:12,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 10:03:13,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 10:03:13,490 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-02 10:03:13,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:03:14,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 10:03:14,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:14,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:03:14,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:03:14,944 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:03:15,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-02 10:03:15,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:15,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:03:15,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 10:03:16,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:16,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `interval` decimal(21,9)
2025-06-02 10:03:16,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 10:03:16,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-02 10:03:16,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:03:17,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0
2025-06-02 10:03:17,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:03:18,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-02 10:03:18,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:03:18,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:03:18,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:03:19,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 10:03:19,834 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:03:20,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:03:20,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:03:20,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:03:21,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 10:03:21,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:21,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:03:21,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:03:21,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:22,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:22,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 10:03:22,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:22,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:03:23,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:03:23,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:03:23,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 10:03:23,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 10:03:23,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 10:03:24,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:03:24,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:03:24,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:03:25,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-02 10:03:25,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:03:26,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:03:29,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-02 10:14:07,828 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:14:08,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 10:14:10,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 10:14:11,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-02 10:14:12,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:14:12,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 10:14:13,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 10:14:13,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:13,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 10:14:13,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:14:14,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 10:14:14,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS generic_name (`generic_name`)
2025-06-02 10:14:14,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:15,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:14:15,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `number_of_repeats_allowed` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `strength` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 10:14:15,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:15,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `interval` decimal(21,9)
2025-06-02 10:14:16,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 10:14:16,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name_index`, DROP INDEX `lab_test_code`
2025-06-02 10:14:16,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:14:16,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:14:17,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:14:17,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-02 10:14:17,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:14:18,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:14:18,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:14:19,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-02 10:14:19,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 10:14:20,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:14:20,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 10:14:21,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 10:14:21,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 10:14:21,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:21,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:14:22,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:14:22,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:22,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:22,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 10:14:22,878 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:23,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:14:23,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 10:14:23,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 10:14:24,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 10:14:24,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 10:14:24,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 10:14:24,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 10:14:24,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:14:25,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 10:14:25,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-02 10:14:26,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 10:14:28,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-02 10:14:31,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-06-02 10:14:32,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 10:35:03,716 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_4e5b33133c0fbbea'@'localhost'
2025-06-02 10:35:12,616 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_4e5b33133c0fbbea`
2025-06-02 10:35:12,619 WARNING database DDL Query made to DB:
CREATE USER '_4e5b33133c0fbbea'@'localhost' IDENTIFIED BY 'CfIdPkiZZJ4qSUSA'
2025-06-02 10:35:12,621 WARNING database DDL Query made to DB:
CREATE DATABASE `_4e5b33133c0fbbea` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-02 10:36:24,180 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:24,190 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 10:36:50,720 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:36:53,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion` MODIFY `name` varchar(140)
2025-06-02 10:36:53,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabError Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Sync Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Update Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccess Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabView Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabEnergy Point Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `name` varchar(140)
2025-06-02 10:36:55,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue` MODIFY `name` varchar(140)
2025-06-02 10:36:56,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare` MODIFY `name` varchar(140)
2025-06-02 10:36:56,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Follow` MODIFY `name` varchar(140)
2025-06-02 10:36:56,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabConsole Log` MODIFY `name` varchar(140)
2025-06-02 10:37:00,680 WARNING database DDL Query made to DB:
RENAME TABLE `tabMedical Code Standard` TO `tabCode System`
2025-06-02 10:37:01,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode System` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `code_system` varchar(140) unique, ADD COLUMN `description` text, ADD COLUMN `status` varchar(140), ADD COLUMN `version` varchar(140), ADD COLUMN `is_fhir_defined` int(1) not null default 1, ADD COLUMN `oid` varchar(140) unique, ADD COLUMN `experimental` int(1) not null default 1, ADD COLUMN `immutable` int(1) not null default 0, ADD COLUMN `complete` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-06-02 10:37:01,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode System` ADD UNIQUE INDEX IF NOT EXISTS code_system (`code_system`), ADD UNIQUE INDEX IF NOT EXISTS oid (`oid`)
2025-06-02 10:37:01,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode System` ADD INDEX `disabled_index`(`disabled`), ADD INDEX `version_index`(`version`)
2025-06-02 10:37:01,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode System` DROP INDEX `uri`
2025-06-02 10:37:03,212 WARNING database DDL Query made to DB:
RENAME TABLE `tabMedical Code` TO `tabCode Value`
2025-06-02 10:37:03,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode Value` ADD COLUMN `code_system` varchar(140), ADD COLUMN `system_uri` varchar(140), ADD COLUMN `experimental` int(1) not null default 0, ADD COLUMN `immutable` int(1) not null default 0, ADD COLUMN `complete` int(1) not null default 0, ADD COLUMN `code_value` varchar(140), ADD COLUMN `value_set` varchar(140), ADD COLUMN `display` text, ADD COLUMN `status` varchar(140), ADD COLUMN `version` varchar(140), ADD COLUMN `level` int(10) not null default 0, ADD COLUMN `definition` text, ADD COLUMN `official_url` varchar(140), ADD COLUMN `canonical_mapping` varchar(140), ADD COLUMN `custom` int(1) not null default 0
2025-06-02 10:37:03,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabCode Value` ADD INDEX `code_system_index`(`code_system`), ADD INDEX `system_uri_index`(`system_uri`), ADD INDEX `code_value_index`(`code_value`), ADD INDEX `version_index`(`version`)
2025-06-02 10:37:05,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabCodification Table` ADD COLUMN `code_system` varchar(140), ADD COLUMN `is_fhir_defined` int(1) not null default 0, ADD COLUMN `oid` varchar(140), ADD COLUMN `code_value` varchar(140), ADD COLUMN `display` varchar(140), ADD COLUMN `definition` text
2025-06-02 10:37:05,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Care Type` ADD COLUMN `description` text
2025-06-02 10:37:06,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `company_contact_person` varchar(140), ADD COLUMN `subscription` varchar(140)
2025-06-02 10:37:06,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-02 10:37:06,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-06-02 10:37:07,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0, ADD COLUMN `pos_invoice` varchar(140), ADD COLUMN `pos_invoice_item` varchar(140)
2025-06-02 10:37:08,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
2025-06-02 10:37:08,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`), ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `project_index`(`project`), ADD INDEX `creation`(`creation`)
2025-06-02 10:37:09,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `treatment_counselling` varchar(140)
2025-06-02 10:37:09,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-02 10:42:45,352 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 10:46:11,687 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_4e5b33133c0fbbea'@'localhost'
2025-06-02 10:54:44,372 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_4e5b33133c0fbbea`
2025-06-02 10:54:44,375 WARNING database DDL Query made to DB:
CREATE USER '_4e5b33133c0fbbea'@'localhost' IDENTIFIED BY 'CfIdPkiZZJ4qSUSA'
2025-06-02 10:54:44,376 WARNING database DDL Query made to DB:
CREATE DATABASE `_4e5b33133c0fbbea` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-02 10:54:44,770 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:44,780 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published int(1) not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-06-02 10:54:44,794 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:46,067 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140) default 'Blue',
`custom` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:46,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` int(1) not null default 0, ADD COLUMN `is_virtual` int(1) not null default 0, ADD COLUMN `sort_options` int(1) not null default 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-06-02 10:54:47,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `label` varchar(140), MODIFY `fieldtype` varchar(140) default 'Data', MODIFY `width` varchar(10), MODIFY `print_width` varchar(10), MODIFY `depends_on` longtext, MODIFY `read_only_depends_on` longtext, MODIFY `oldfieldname` varchar(140), MODIFY `collapsible_depends_on` longtext, MODIFY `mandatory_depends_on` longtext, MODIFY `oldfieldtype` varchar(140), MODIFY `fieldname` varchar(140), MODIFY `precision` varchar(140)
2025-06-02 10:54:47,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` int(1) not null default 0, ADD COLUMN `select` int(1) not null default 0
2025-06-02 10:54:47,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `role` varchar(140)
2025-06-02 10:54:47,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-06-02 10:54:47,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `is_child_table` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-06-02 10:54:47,371 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ui_tour` int(1) not null default 0,
`is_table_field` int(1) not null default 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) default 'Bottom',
`hide_buttons` int(1) not null default 0,
`popover_element` int(1) not null default 0,
`modal_trigger` int(1) not null default 0,
`offset_x` int(11) not null default 0,
`offset_y` int(11) not null default 0,
`next_on_click` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default '0',
`has_next_condition` int(1) not null default 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:47,462 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) default 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` int(1) not null default 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` int(1) not null default 0,
`track_steps` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`save_on_complete` int(1) not null default 0,
`first_document` int(1) not null default 0,
`include_name_field` int(1) not null default 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:47,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `is_calendar_and_gantt` int(1) not null default 0, ADD COLUMN `quick_entry` int(1) not null default 0, ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `track_views` int(1) not null default 0, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `documentation` varchar(140), ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `allow_events_in_timeline` int(1) not null default 0, ADD COLUMN `allow_auto_repeat` int(1) not null default 0, ADD COLUMN `make_attachments_public` int(1) not null default 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` int(1) not null default 0, ADD COLUMN `show_preview_popup` int(1) not null default 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `index_web_pages_for_search` int(1) not null default 1, ADD COLUMN `row_format` varchar(140) default 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-06-02 10:54:47,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `icon` varchar(140), MODIFY `engine` varchar(140) default 'InnoDB', MODIFY `route` varchar(140), MODIFY `website_search_field` varchar(140), MODIFY `title_field` varchar(140), MODIFY `document_type` varchar(140), MODIFY `is_published_field` varchar(140), MODIFY `timeline_field` varchar(140), MODIFY `image_field` varchar(140), MODIFY `default_print_format` varchar(140), MODIFY `sort_field` varchar(140) default 'modified', MODIFY `search_fields` varchar(140), MODIFY `migration_hash` varchar(140), MODIFY `allow_rename` int(1) not null default 1, MODIFY `module` varchar(140), MODIFY `subject_field` varchar(140), MODIFY `sort_order` varchar(140) default 'DESC', MODIFY `restrict_to_domain` varchar(140), MODIFY `autoname` varchar(140), MODIFY `color` varchar(140), MODIFY `sender_field` varchar(140), MODIFY `_user_tags` text
2025-06-02 10:54:47,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-06-02 10:54:47,903 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_name` varchar(140) unique,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` int(1) not null default 0,
`is_custom` int(1) not null default 0,
`desk_access` int(1) not null default 1,
`two_factor_auth` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,061 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,250 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(140),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int(11) not null default 0,
`link_filters` json,
`fieldtype` varchar(140) default 'Data',
`precision` varchar(140),
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`read_only` int(1) not null default 0,
`ignore_user_permissions` int(1) not null default 0,
`hidden` int(1) not null default 0,
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`no_copy` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`search_index` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`translatable` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`description` text,
`permlevel` int(11) not null default 0,
`width` varchar(140),
`columns` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,374 WARNING database DDL Query made to DB:
create table `tabProperty Setter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`doctype_or_field` varchar(140),
`doc_type` varchar(140),
`field_name` varchar(140),
`row_name` varchar(140),
`module` varchar(140),
`property` varchar(140),
`property_type` varchar(140),
`value` text,
`default_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `doc_type`(`doc_type`),
index `field_name`(`field_name`),
index `property`(`property`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,614 WARNING database DDL Query made to DB:
create table `tabWeb Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`doc_type` varchar(140),
`module` varchar(140),
`is_standard` int(1) not null default 0,
`introduction_text` longtext,
`anonymous` int(1) not null default 0,
`login_required` int(1) not null default 0,
`apply_document_permissions` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`allow_multiple` int(1) not null default 0,
`allow_delete` int(1) not null default 0,
`allow_incomplete` int(1) not null default 0,
`allow_comments` int(1) not null default 0,
`allow_print` int(1) not null default 0,
`print_format` varchar(140),
`max_attachment_size` int(11) not null default 0,
`show_attachments` int(1) not null default 0,
`allowed_embedding_domains` text,
`condition_json` json,
`show_list` int(1) not null default 0,
`list_title` varchar(140),
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`button_label` varchar(140) default 'Save',
`banner_image` text,
`breadcrumbs` longtext,
`success_title` varchar(140),
`success_url` varchar(140),
`success_message` text,
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`client_script` longtext,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,740 WARNING database DDL Query made to DB:
create table `tabWeb Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Section',
`standard` int(1) not null default 0,
`module` varchar(140),
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,865 WARNING database DDL Query made to DB:
create table `tabWeb Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
`allow_read_on_all_link_options` int(1) not null default 0,
`reqd` int(1) not null default 0,
`read_only` int(1) not null default 0,
`show_in_filter` int(1) not null default 0,
`hidden` int(1) not null default 0,
`options` text,
`max_length` int(11) not null default 0,
`max_value` int(11) not null default 0,
`precision` varchar(140),
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`description` text,
`default` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:48,958 WARNING database DDL Query made to DB:
create table `tabPortal Menu Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`enabled` int(1) not null default 0,
`route` varchar(140),
`reference_doctype` varchar(140),
`role` varchar(140),
`target` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,084 WARNING database DDL Query made to DB:
create table `tabNumber Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`label` varchar(140),
`type` varchar(140),
`report_name` varchar(140),
`method` varchar(140),
`function` varchar(140),
`aggregate_function_based_on` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`report_field` varchar(140),
`report_function` varchar(140),
`is_public` int(1) not null default 0,
`currency` varchar(140),
`filters_config` longtext,
`show_percentage_stats` int(1) not null default 1,
`stats_time_interval` varchar(140) default 'Daily',
`filters_json` longtext,
`dynamic_filters_json` longtext,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,289 WARNING database DDL Query made to DB:
create table `tabDashboard Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_name` varchar(140) unique,
`chart_type` varchar(140),
`report_name` varchar(140),
`use_report_chart` int(1) not null default 0,
`x_field` varchar(140),
`source` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`based_on` varchar(140),
`value_based_on` varchar(140),
`group_by_type` varchar(140) default 'Count',
`group_by_based_on` varchar(140),
`aggregate_function_based_on` varchar(140),
`number_of_groups` int(11) not null default 0,
`is_public` int(1) not null default 0,
`heatmap_year` varchar(140),
`timespan` varchar(140),
`from_date` date,
`to_date` date,
`time_interval` varchar(140),
`timeseries` int(1) not null default 0,
`type` varchar(140) default 'Line',
`currency` varchar(140),
`filters_json` longtext,
`dynamic_filters_json` longtext,
`custom_options` longtext,
`color` varchar(140),
`last_synced_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,387 WARNING database DDL Query made to DB:
create table `tabDashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dashboard_name` varchar(140) unique,
`is_default` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_options` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,462 WARNING database DDL Query made to DB:
create table `tabOnboarding Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,603 WARNING database DDL Query made to DB:
create table `tabOnboarding Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`is_complete` int(1) not null default 0,
`is_skipped` int(1) not null default 0,
`description` longtext,
`intro_video_url` varchar(140),
`action` varchar(140),
`action_label` varchar(140),
`reference_document` varchar(140),
`show_full_form` int(1) not null default 0,
`show_form_tour` int(1) not null default 0,
`form_tour` varchar(140),
`is_single` int(1) not null default 0,
`reference_report` varchar(140),
`report_reference_doctype` varchar(140),
`report_type` varchar(140),
`report_description` varchar(140),
`path` varchar(140),
`callback_title` varchar(140),
`callback_message` text,
`validate_action` int(1) not null default 1,
`field` varchar(140),
`value_to_validate` varchar(140),
`video_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,692 WARNING database DDL Query made to DB:
create table `tabOnboarding Step Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`step` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,790 WARNING database DDL Query made to DB:
create table `tabModule Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`module` varchar(140),
`success_message` varchar(140),
`documentation_url` varchar(140),
`is_complete` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,900 WARNING database DDL Query made to DB:
create table `tabWorkspace Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Link',
`label` varchar(140),
`icon` varchar(140),
`description` longtext,
`hidden` int(1) not null default 0,
`link_type` varchar(140),
`link_to` varchar(140),
`report_ref_doctype` varchar(140),
`dependencies` varchar(140),
`only_for` varchar(140),
`onboard` int(1) not null default 0,
`is_query_report` int(1) not null default 0,
`link_count` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:49,977 WARNING database DDL Query made to DB:
create table `tabWorkspace Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,080 WARNING database DDL Query made to DB:
create table `tabWorkspace Shortcut` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`link_to` varchar(140),
`url` varchar(140),
`doc_view` varchar(140),
`kanban_board` varchar(140),
`label` varchar(140),
`icon` varchar(140),
`restrict_to_domain` varchar(140),
`report_ref_doctype` varchar(140),
`stats_filter` longtext,
`color` varchar(140),
`format` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,154 WARNING database DDL Query made to DB:
create table `tabWorkspace Quick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`label` varchar(140),
`quick_list_filter` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,227 WARNING database DDL Query made to DB:
create table `tabWorkspace Number Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_card_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,293 WARNING database DDL Query made to DB:
create table `tabWorkspace Custom Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`custom_block_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,452 WARNING database DDL Query made to DB:
create table `tabWorkspace` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140) unique,
`title` varchar(140),
`sequence_id` decimal(21,9) not null default 0,
`for_user` varchar(140),
`parent_page` varchar(140),
`module` varchar(140),
`icon` varchar(140),
`indicator_color` varchar(140),
`restrict_to_domain` varchar(140),
`hide_custom` int(1) not null default 0,
`public` int(1) not null default 0,
`is_hidden` int(1) not null default 0,
`content` longtext default '[]',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `restrict_to_domain`(`restrict_to_domain`),
index `public`(`public`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,591 WARNING database DDL Query made to DB:
create table `tabPage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_page` int(1) not null default 0,
`page_name` varchar(140) unique,
`title` varchar(140),
`icon` varchar(140),
`module` varchar(140),
`restrict_to_domain` varchar(140),
`standard` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,708 WARNING database DDL Query made to DB:
create table `tabReport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report_name` varchar(140) unique,
`ref_doctype` varchar(140),
`reference_report` varchar(140),
`is_standard` varchar(140),
`module` varchar(140),
`report_type` varchar(140),
`letter_head` varchar(140),
`add_total_row` int(1) not null default 0,
`disabled` int(1) not null default 0,
`prepared_report` int(1) not null default 0,
`add_translate_data` int(1) not null default 0,
`timeout` int(11) not null default 0,
`query` longtext,
`report_script` longtext,
`javascript` longtext,
`json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,800 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140) unique,
`module` varchar(140),
`timeseries` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:50,914 WARNING database DDL Query made to DB:
create table `tabPrint Format` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`doc_type` varchar(140),
`module` varchar(140),
`default_print_language` varchar(140),
`standard` varchar(140) default 'No',
`custom_format` int(1) not null default 0,
`disabled` int(1) not null default 0,
`pdf_generator` varchar(140) default 'wkhtmltopdf',
`print_format_type` varchar(140) default 'Jinja',
`raw_printing` int(1) not null default 0,
`html` longtext,
`raw_commands` longtext,
`margin_top` decimal(21,9) not null default 15.0,
`margin_bottom` decimal(21,9) not null default 15.0,
`margin_left` decimal(21,9) not null default 15.0,
`margin_right` decimal(21,9) not null default 15.0,
`align_labels_right` int(1) not null default 0,
`show_section_headings` int(1) not null default 0,
`line_breaks` int(1) not null default 0,
`absolute_value` int(1) not null default 0,
`font_size` int(11) not null default 14,
`font` varchar(140),
`page_number` varchar(140) default 'Hide',
`css` longtext,
`format_data` longtext,
`print_format_builder` int(1) not null default 0,
`print_format_builder_beta` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,046 WARNING database DDL Query made to DB:
create table `tabWeb Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`dynamic_route` int(1) not null default 0,
`published` int(1) not null default 1,
`module` varchar(140),
`content_type` varchar(140) default 'Page Builder',
`slideshow` varchar(140),
`dynamic_template` int(1) not null default 0,
`main_section` longtext,
`main_section_md` longtext,
`main_section_html` longtext,
`context_script` longtext,
`javascript` longtext,
`insert_style` int(1) not null default 0,
`text_align` varchar(140),
`css` longtext,
`full_width` int(1) not null default 1,
`show_title` int(1) not null default 0,
`start_date` datetime(6),
`end_date` datetime(6),
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`enable_comments` int(1) not null default 0,
`header` longtext,
`breadcrumbs` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,150 WARNING database DDL Query made to DB:
create table `tabWebsite Theme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`theme` varchar(140) unique,
`module` varchar(140) default 'Website',
`custom` int(1) not null default 1,
`google_font` varchar(140),
`font_size` varchar(140),
`font_properties` varchar(140) default 'wght@300;400;500;600;700;800',
`button_rounded_corners` int(1) not null default 1,
`button_shadows` int(1) not null default 0,
`button_gradients` int(1) not null default 0,
`primary_color` varchar(140),
`text_color` varchar(140),
`light_color` varchar(140),
`dark_color` varchar(140),
`background_color` varchar(140),
`custom_overrides` longtext,
`custom_scss` longtext,
`theme_scss` longtext,
`theme_url` varchar(140),
`js` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,260 WARNING database DDL Query made to DB:
create table `tabNotification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`channel` varchar(140) default 'Email',
`slack_webhook_url` varchar(140),
`subject` varchar(140),
`event` varchar(140),
`document_type` varchar(140),
`method` varchar(140),
`date_changed` varchar(140),
`days_in_advance` int(11) not null default 0,
`value_changed` varchar(140),
`sender` varchar(140),
`send_system_notification` int(1) not null default 0,
`sender_email` varchar(140),
`condition` longtext,
`set_property_after_alert` varchar(140),
`property_value` varchar(140),
`send_to_all_assignees` int(1) not null default 0,
`message_type` varchar(140) default 'Markdown',
`message` longtext default 'Add your message here',
`attach_print` int(1) not null default 0,
`print_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `event`(`event`),
index `document_type`(`document_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,340 WARNING database DDL Query made to DB:
create table `tabPrint Style` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_style_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`standard` int(1) not null default 0,
`css` longtext,
`preview` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,408 WARNING database DDL Query made to DB:
create table `tabClient Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`view` varchar(140) default 'Form',
`module` varchar(140),
`enabled` int(1) not null default 0,
`script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,502 WARNING database DDL Query made to DB:
create table `tabServer Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script_type` varchar(140),
`reference_doctype` varchar(140),
`event_frequency` varchar(140),
`cron_format` varchar(140),
`doctype_event` varchar(140),
`api_method` varchar(140),
`allow_guest` int(1) not null default 0,
`module` varchar(140),
`disabled` int(1) not null default 0,
`script` longtext,
`enable_rate_limit` int(1) not null default 0,
`rate_limit_count` int(11) not null default 5,
`rate_limit_seconds` int(11) not null default 86400,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `module`(`module`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue` MODIFY `defkey` varchar(140)
2025-06-02 10:54:51,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue`
				ADD INDEX IF NOT EXISTS `defaultvalue_parent_parenttype_index`(parent, parenttype)
2025-06-02 10:54:51,754 WARNING database DDL Query made to DB:
create table `tabReport Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` varchar(140),
`width` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,827 WARNING database DDL Query made to DB:
create table `tabSuccess Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140) unique,
`first_success_message` varchar(140) default 'Congratulations on first creations',
`message` varchar(140) default 'Successfully created',
`next_actions` varchar(140),
`action_timeout` int(11) not null default 7,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,900 WARNING database DDL Query made to DB:
create table `tabPackage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140),
`readme` longtext,
`license_type` varchar(140),
`license` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:51,987 WARNING database DDL Query made to DB:
create table `tabVersion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`docname` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion`
				ADD INDEX IF NOT EXISTS `ref_doctype_docname_index`(ref_doctype, docname)
2025-06-02 10:54:52,130 WARNING database DDL Query made to DB:
create table `tabActivity Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`content` longtext,
`communication_date` datetime(6),
`ip_address` varchar(140),
`operation` varchar(140),
`status` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reference_owner` varchar(140),
`timeline_doctype` varchar(140),
`timeline_name` varchar(140),
`link_doctype` varchar(140),
`link_name` varchar(140),
`user` varchar(140),
`full_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `reference_doctype_reference_name_index`(reference_doctype, reference_name)
2025-06-02 10:54:52,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log`
				ADD INDEX IF NOT EXISTS `timeline_doctype_timeline_name_index`(timeline_doctype, timeline_name)
2025-06-02 10:54:52,316 WARNING database DDL Query made to DB:
create table `tabModule Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_profile_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,461 WARNING database DDL Query made to DB:
create table `tabNavbar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_label` varchar(140),
`item_type` varchar(140),
`route` varchar(140),
`action` varchar(140),
`hidden` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,631 WARNING database DDL Query made to DB:
create table `tabTranslation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contributed` int(1) not null default 0,
`language` varchar(140),
`source_text` longtext,
`context` varchar(140),
`translated_text` longtext,
`contribution_status` varchar(140),
`contribution_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `language`(`language`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,699 WARNING database DDL Query made to DB:
create table `tabData Import Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`data_import` varchar(140),
`row_indexes` longtext,
`success` int(1) not null default 0,
`docname` varchar(140),
`messages` longtext,
`exception` text,
`log_index` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,789 WARNING database DDL Query made to DB:
create table `tabAccess Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`export_from` varchar(140),
`user` varchar(140),
`reference_document` varchar(140),
`timestamp` datetime(6),
`file_type` varchar(140),
`method` varchar(140),
`report_name` varchar(140),
`filters` longtext,
`page` longtext,
`columns` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,873 WARNING database DDL Query made to DB:
create table `tabUser Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`allow` varchar(140),
`for_value` varchar(140),
`is_default` int(1) not null default 0,
`apply_to_all_doctypes` int(1) not null default 1,
`applicable_for` varchar(140),
`hide_descendants` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:52,960 WARNING database DDL Query made to DB:
create table `tabUser Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`is_custom` int(1) not null default 0,
`read` int(1) not null default 1,
`write` int(1) not null default 0,
`create` int(1) not null default 0,
`submit` int(1) not null default 0,
`cancel` int(1) not null default 0,
`amend` int(1) not null default 0,
`delete` int(1) not null default 0,
`email` int(1) not null default 1,
`share` int(1) not null default 1,
`print` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,035 WARNING database DDL Query made to DB:
create table `tabUser Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_account` varchar(140),
`email_id` varchar(140),
`awaiting_password` int(1) not null default 0,
`used_oauth` int(1) not null default 0,
`enable_outgoing` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,113 WARNING database DDL Query made to DB:
create table `tabDocument Naming Rule Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`field` varchar(140),
`condition` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,231 WARNING database DDL Query made to DB:
create table `tabAmended Document Naming Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`action` varchar(140) default 'Amend Counter',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,321 WARNING database DDL Query made to DB:
create table `tabSubmission Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`job_id` varchar(140),
`ended_at` datetime(6),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`exception` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_docname`(`ref_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,540 WARNING database DDL Query made to DB:
create table `tabRole Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_profile` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 10:54:53,613 WARNING database DDL Query made to DB:
create table `tabPackage Release` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package` varchar(140),
`publish` int(1) not null default 0,
`path` text,
`major` int(11) not null default 0,
`minor` int(11) not null default 0,
`patch` int(11) not null default 0,
`release_notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
