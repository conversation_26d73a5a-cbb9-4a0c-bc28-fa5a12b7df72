2025-06-02 11:06:25,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-06-02 11:06:25,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `old_employee_id` varchar(140), ADD COLUMN `heslb_f4_index_number` varchar(140)
2025-06-02 11:06:25,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:06:25,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) not null default 0
2025-06-02 11:06:25,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD COLUMN `spare_name` varchar(140), ADD COLUMN `quantity` decimal(21,9) not null default 0, ADD COLUMN `invoice` varchar(140)
2025-06-02 11:06:25,909 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 11:06:25,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140), ADD COLUMN `referance_doctype` varchar(140), ADD COLUMN `referance_docname` varchar(140), ADD COLUMN `from_date` date, ADD COLUMN `to_date` date
2025-06-02 11:06:26,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0
2025-06-02 11:06:26,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `posting_date` date, ADD COLUMN `start_date` date, ADD COLUMN `end_date` date
2025-06-02 11:06:26,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-02 11:06:26,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140)
2025-06-02 11:06:26,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0
2025-06-02 11:06:26,346 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-06-02 11:06:26,409 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-06-02 11:06:26,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `posting_date` date
2025-06-02 11:06:26,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-02 11:06:26,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` ADD COLUMN `fully_paid` int(1) not null default 0
2025-06-02 11:06:26,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `total` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0
2025-06-02 11:06:27,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-06-02 11:06:27,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0
2025-06-02 11:06:27,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-06-02 11:06:27,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-02 11:06:27,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-06-02 11:06:27,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-02 11:06:28,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-06-02 11:06:28,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:06:28,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:30,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-06-02 11:06:30,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-06-02 11:06:30,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-06-02 11:06:30,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-02 11:06:30,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-06-02 11:06:30,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0
2025-06-02 11:06:30,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:30,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-06-02 11:06:30,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-02 11:06:30,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:31,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employee_country` varchar(140), ADD COLUMN `employee_country_code` varchar(140), ADD COLUMN `beneficiary_bank_bic` varchar(140), ADD COLUMN `bank_country_code` varchar(140), ADD COLUMN `bank_country` varchar(140), ADD COLUMN `bank_account_name` varchar(140)
2025-06-02 11:06:31,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:06:31,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-06-02 11:06:31,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-02 11:06:31,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:32,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-06-02 11:06:32,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0
2025-06-02 11:06:32,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-06-02 11:06:32,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:06:32,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0
2025-06-02 11:06:32,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-06-02 11:06:32,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-02 11:06:32,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:33,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-06-02 11:06:33,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-06-02 11:06:33,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-06-02 11:06:33,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-06-02 11:06:33,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:06:33,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-06-02 11:06:33,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-02 11:06:33,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-06-02 11:06:33,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `applicable_charges` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:06:34,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-06-02 11:06:34,836 WARNING database DDL Query made to DB:
create table `tabPayroll Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`start_date` date,
`end_date` varchar(140),
`payment_mode` varchar(140),
`payment_date` date,
`payment_type` varchar(140),
`payment_reference_number` varchar(140),
`receipt_number` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:34,943 WARNING database DDL Query made to DB:
create table `tabSalary Slip Overtime` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,042 WARNING database DDL Query made to DB:
create table `tabArea` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`area` varchar(140) unique,
`area_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,147 WARNING database DDL Query made to DB:
create table `tabLoan Repayment Not From Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'LNFS-.#######',
`loan` varchar(140),
`employee_name` varchar(140),
`employee` varchar(140),
`payment_date` date,
`payment_amount` decimal(21,9) not null default 0,
`company` varchar(140),
`description` varchar(140),
`journal_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,242 WARNING database DDL Query made to DB:
create table `tabLoan NFS Repayments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`nfs_loan_repayment` varchar(140),
`payment_date` date,
`payment_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,344 WARNING database DDL Query made to DB:
create table `tabTransaction Fetch Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`count` varchar(140),
`page` varchar(140),
`unique` varchar(140),
`repeated` varchar(140),
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,468 WARNING database DDL Query made to DB:
create table `tabGoal Sheet Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`hr_perspective_1` varchar(140) default 'Financial',
`perspective_weightage_1` decimal(21,9) not null default 0,
`hr_perspective_2` varchar(140) default 'Internal Processes',
`perspective_weightage_2` decimal(21,9) not null default 0,
`hr_perspective_3` varchar(140) default 'Customer Focus',
`perspective_weightage_3` decimal(21,9) not null default 0,
`hr_perspective_4` varchar(140) default 'People & Team',
`perspective_weightage_4` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,604 WARNING database DDL Query made to DB:
create table `tabEmployee OT Components` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,693 WARNING database DDL Query made to DB:
create table `tabEmployee Area` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`area` varchar(140),
`area_code` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:35,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:06:35,922 WARNING database DDL Query made to DB:
create table `tabRoles and Responsibilities` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`roles_and_resp` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,078 WARNING database DDL Query made to DB:
create table `tabGoal Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`goal_sheet_template` varchar(140),
`employee` varchar(140),
`designation` varchar(140),
`employee_full_name` varchar(140),
`hr_perspective_1` varchar(140),
`perspective_weightage_1` decimal(21,9) not null default 0,
`hr_perspective_2` varchar(140),
`perspective_weightage_2` decimal(21,9) not null default 0,
`hr_perspective_3` varchar(140),
`perspective_weightage_3` decimal(21,9) not null default 0,
`hr_perspective_4` varchar(140),
`perspective_weightage_4` decimal(21,9) not null default 0,
`total_employee_rating` decimal(21,9) not null default 0,
`total_manager_rating` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,188 WARNING database DDL Query made to DB:
create table `tabDesignation Objective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,386 WARNING database DDL Query made to DB:
create table `tabSalary Slip OT Components` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`no_of_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,521 WARNING database DDL Query made to DB:
create table `tabTransactions Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`id` varchar(140),
`emp_code` varchar(140),
`punch_time` varchar(140),
`punch_state` varchar(140),
`verify_type` varchar(140),
`work_code` varchar(140),
`terminal_sn` varchar(140),
`terminal_alias` varchar(140),
`area_alias` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`gps_location` varchar(140),
`mobile` varchar(140),
`source` varchar(140),
`purpose` varchar(140),
`crc` varchar(140),
`is_attendance` varchar(140),
`reserved` varchar(140),
`upload_time` varchar(140),
`sync_status` varchar(140),
`sync_time` varchar(140),
`emp` varchar(140),
`terminal` varchar(140),
`transaction_fetch_log` varchar(140),
`employee_checkin` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,624 WARNING database DDL Query made to DB:
create table `tabNSSF Payments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_month` date,
`amount` decimal(21,9) not null default 0,
`payment_method` varchar(140),
`payment_reference_number` varchar(140),
`payment_date` date,
`receipt_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,817 WARNING database DDL Query made to DB:
create table `tabDesignation Education` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`education` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:36,919 WARNING database DDL Query made to DB:
create table `tabPayroll Payments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`payment_type` varchar(140),
`payment_mode` varchar(140),
`payment_date` date,
`payment_reference_number` varchar(140),
`receipt_number` varchar(140),
`start_date` date,
`end_date` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,024 WARNING database DDL Query made to DB:
create table `tabGoal Sheet Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140),
`measure` varchar(140),
`target` varchar(140),
`acheivement` varchar(140),
`objective_weightage` decimal(21,9) not null default 0,
`employee_rating` decimal(21,9) not null default 0,
`manager_rating` decimal(21,9) not null default 0,
`employee_remarks` varchar(140),
`manager_remarks` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,130 WARNING database DDL Query made to DB:
create table `tabHR Perspective` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`perspective` varchar(140) unique,
`weightage` decimal(21,9) not null default 0,
`description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,233 WARNING database DDL Query made to DB:
create table `tabGoal Sheet Template Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140),
`measure` text,
`target` text,
`acheivement` varchar(140),
`objective_weightage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,375 WARNING database DDL Query made to DB:
create table `tabDesignation KPI Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`hr_perspective_1` varchar(140) default 'Financial',
`perspective_weightage_1` decimal(21,9) not null default 0,
`hr_perspective_2` varchar(140) default 'Internal Processes',
`perspective_weightage_2` decimal(21,9) not null default 0,
`hr_perspective_3` varchar(140) default 'Customer Focus',
`perspective_weightage_3` decimal(21,9) not null default 0,
`hr_perspective_4` varchar(140) default 'People & Team',
`perspective_weightage_4` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,496 WARNING database DDL Query made to DB:
create table `tabEmployee KPI` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation_kpi_template` varchar(140),
`employee` varchar(140),
`designation` varchar(140),
`employee_full_name` varchar(140),
`hr_perspective_1` varchar(140),
`perspective_weightage_1` decimal(21,9) not null default 0,
`hr_perspective_2` varchar(140),
`perspective_weightage_2` decimal(21,9) not null default 0,
`hr_perspective_3` varchar(140),
`perspective_weightage_3` decimal(21,9) not null default 0,
`hr_perspective_4` varchar(140),
`perspective_weightage_4` decimal(21,9) not null default 0,
`total_employee_rating` decimal(21,9) not null default 0,
`total_manager_rating` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:37,627 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:06:39,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:06:39,931 WARNING database DDL Query made to DB:
create table `tabLimit Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment_no` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`is_cash_inpatient` int(1) not null default 0,
`previous_cash_limit` decimal(21,9) not null default 0,
`current_total_deposit` decimal(21,9) not null default 0,
`inpatient_record` varchar(140),
`cash_limit` decimal(21,9) not null default 0,
`is_non_nhif_patient` int(1) not null default 0,
`previous_daily_limit` decimal(21,9) not null default 0,
`current_total_cost` decimal(21,9) not null default 0,
`insurance_company` varchar(140),
`daily_limit` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`naming_series` varchar(140),
`approved_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:40,060 WARNING database DDL Query made to DB:
create table `tabModality Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`modality_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:40,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `healthcare_practitioner_type` varchar(140)
2025-06-02 11:06:40,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 11:06:40,402 WARNING database DDL Query made to DB:
create table `tabLab Machine Profile` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`machine_make` varchar(140),
`machine_model` varchar(140),
`msh` int(11) not null default 0,
`obr` int(11) not null default 0,
`obx_nm_start` int(11) not null default 0,
`obx_nm_end` int(11) not null default 0,
`lab_test_prefix` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:40,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `total_service_unit_capacity` int(11) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality_name` varchar(140)
2025-06-02 11:06:40,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 11:06:40,699 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:40,804 WARNING database DDL Query made to DB:
create table `tabHealthcare Return Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:40,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `inpatient_record` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 11:06:41,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:06:41,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140), ADD COLUMN `discharge_date` date
2025-06-02 11:06:41,409 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_category_name` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:41,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `clinical_procedure` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-06-02 11:06:41,695 WARNING database DDL Query made to DB:
create table `tabMedication Class Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:41,819 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Contract` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`default_price_list` varchar(140),
`is_active` int(1) not null default 0,
`start_date` date,
`end_date` date,
`apply_coverage_on_amount_with_tax` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,073 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`mobile` varchar(140),
`company` varchar(140),
`appointment` varchar(140),
`posting_datetime` datetime(6),
`practitioner` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`payment_type` varchar(140) default 'Cash',
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`years_of_insurance` int(11) not null default 0,
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `gender`(`gender`),
index `mobile`(`mobile`),
index `company`(`company`),
index `appointment`(`appointment`),
index `posting_datetime`(`posting_datetime`),
index `practitioner`(`practitioner`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `payment_type`(`payment_type`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `naming_series`(`naming_series`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,198 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Check List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,315 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,461 WARNING database DDL Query made to DB:
create table `tabHealthcare Discharge Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`discharge_type_name` varchar(140) unique,
`alias` varchar(140),
`discharge_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `discharge_type_id`(`discharge_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,589 WARNING database DDL Query made to DB:
create table `tabEncounter Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`encounter_category` varchar(140) unique,
`default_healthcare_service_unit` varchar(140),
`default_healthcare_practitioner` varchar(140),
`default_appointment_type` varchar(140),
`encounter_fee` decimal(21,9) not null default 0,
`encounter_fee_item` varchar(140),
`create_sales_invoice` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,783 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`price_list` varchar(140),
`qty` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`department_hsu` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `price_list`(`price_list`),
index `qty`(`qty`),
index `rate`(`rate`),
index `amount`(`amount`),
index `department_hsu`(`department_hsu`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:42,926 WARNING database DDL Query made to DB:
create table `tabRadiology Examination` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`appointment` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`prescribe` int(1) not null default 0,
`company` varchar(140),
`practitioner` varchar(140),
`radiology_examination_template` varchar(140),
`medical_department` varchar(140),
`service_unit` varchar(140),
`radiology_procedure_prescription` varchar(140),
`modality_type` varchar(140),
`modality` varchar(140),
`source` varchar(140) default 'Direct',
`referring_practitioner` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`start_date` date,
`start_time` time(6),
`notes` text,
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`tc_name` varchar(140),
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:43,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140), ADD COLUMN `clinical_procedure_check_list_template` varchar(140)
2025-06-02 11:06:43,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 11:06:43,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` ADD COLUMN `medication_name` varchar(140), ADD COLUMN `medication_category` varchar(140), ADD COLUMN `is_generic` int(1) not null default 0, ADD COLUMN `item` varchar(140), ADD COLUMN `item_code` varchar(140), ADD COLUMN `item_group` varchar(140), ADD COLUMN `description` text, ADD COLUMN `stock_uom` varchar(140), ADD COLUMN `is_billable` int(1) not null default 0, ADD COLUMN `rate` decimal(21,9) not null default 0, ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `patient_care_type` varchar(140), ADD COLUMN `default_dosage_form` varchar(140), ADD COLUMN `bypass_medication_class_interaction_check` int(1) not null default 1, ADD COLUMN `bypass_allergy_check` int(1) not null default 1, ADD COLUMN `bypass_medical_coding_check` int(1) not null default 1
2025-06-02 11:06:43,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` MODIFY `strength` decimal(21,9) not null default 0
2025-06-02 11:06:43,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication` DROP INDEX `generic_name`
2025-06-02 11:06:43,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 11:06:43,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:06:43,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `triage` varchar(140), ADD COLUMN `color` varchar(140), ADD COLUMN `patient_referral` varchar(140), ADD COLUMN `radiology_examination_template` varchar(140), ADD COLUMN `radiology_procedure_prescription` varchar(140), ADD COLUMN `modality_type` varchar(140), ADD COLUMN `modality` varchar(140), ADD COLUMN `practitioner_availability` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 11:06:43,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:06:44,043 WARNING database DDL Query made to DB:
create table `tabMediciation Override Reason Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason_code` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:44,171 WARNING database DDL Query made to DB:
create table `tabHealthcare Notes Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`disabled` int(1) not null default 0,
`terms` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:44,352 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Subscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`is_active` int(1) not null default 1,
`patient` varchar(140),
`patient_name` varchar(140),
`mobile_number` varchar(140),
`customer` varchar(140),
`gender` varchar(140),
`country` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`insurance_company_customer` varchar(140),
`subscription_end_date` date,
`healthcare_insurance_coverage_plan` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:44,515 WARNING database DDL Query made to DB:
create table `tabInsurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`insurance_coverage_request` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:44,633 WARNING database DDL Query made to DB:
create table `tabReferring Reason` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:44,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `usage_interval` int(1) not null default 0, ADD COLUMN `quantity` int(11) not null default 0, ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `expected_date` date, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `occurrence` datetime(6), ADD COLUMN `occurence_period` decimal(21,9), ADD COLUMN `note` text, ADD COLUMN `drug_prescription_created` int(1) not null default 0
2025-06-02 11:06:45,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 11:06:45,360 WARNING database DDL Query made to DB:
create table `tabInpatient Consultancy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`consultation_item` varchar(140),
`rate` decimal(21,9) not null default 0,
`delivery_note` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:45,475 WARNING database DDL Query made to DB:
create table `tabLab Bundle Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_template` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:45,583 WARNING database DDL Query made to DB:
create table `tabSample Collection Lab Test Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test` varchar(140),
`lab_test_tempate` varchar(140),
`test_abbr` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:45,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `allow_procedures` int(1) not null default 0, ADD COLUMN `is_modality` int(1) not null default 0, ADD COLUMN `modality_type` varchar(140)
2025-06-02 11:06:45,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:06:45,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `note` text
2025-06-02 11:06:46,029 WARNING database DDL Query made to DB:
create table `tabPatient Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`naming_series` varchar(140),
`triage` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Pending',
`date` date,
`time` time(6),
`referring_practitioner` varchar(140),
`priority` varchar(140),
`patient_encounter` varchar(140),
`referring_reason` varchar(140),
`referred_to_practitioner` varchar(140),
`referral_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,134 WARNING database DDL Query made to DB:
create table `tabItem Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`quantity` int(11) not null default 1,
`reason` varchar(140),
`encounter_no` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,237 WARNING database DDL Query made to DB:
create table `tabMedication Condition` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`condition` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,364 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Company` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`customer` varchar(140),
`website` varchar(140),
`billed_but_not_claimed_account` varchar(140),
`approved_claim_receivable_account` varchar(140),
`rejected_claims_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,507 WARNING database DDL Query made to DB:
create table `tabHealthcare Admission Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`admission_type_name` varchar(140) unique,
`alias` varchar(140),
`admission_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `admission_type_id`(`admission_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,798 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_name` varchar(140),
`service_type` varchar(140),
`item_code` varchar(140),
`payor_plan` varchar(140) default 'Cash',
`price_list` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` int(11) not null default 0,
`qty_returned` int(11) not null default 0,
`amount` decimal(21,9) not null default 0,
`payment_type` varchar(140) default 'Cash',
`years_of_insurance` int(11) not null default 0,
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`authorization_number` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`request_id` varchar(140),
`department_hsu` varchar(140),
`sales_invoice_number` varchar(140),
`lrpmt_doc_created` int(1) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_name`(`service_name`),
index `service_type`(`service_type`),
index `item_code`(`item_code`),
index `payor_plan`(`payor_plan`),
index `price_list`(`price_list`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `payment_type`(`payment_type`),
index `years_of_insurance`(`years_of_insurance`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `authorization_number`(`authorization_number`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `request_id`(`request_id`),
index `department_hsu`(`department_hsu`),
index `sales_invoice_number`(`sales_invoice_number`),
index `lrpmt_doc_created`(`lrpmt_doc_created`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:46,921 WARNING database DDL Query made to DB:
create table `tabMedication Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_category` varchar(140) unique,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`is_group` int(1) not null default 0,
`old_parent` varchar(140),
`parent_medication_category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:47,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD COLUMN `healthcare_service_order_category` varchar(140), ADD COLUMN `healthcare_service_unit_type` varchar(140), ADD COLUMN `medical_code` varchar(140), ADD COLUMN `medical_code_standard` varchar(140)
2025-06-02 11:06:47,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 11:06:47,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 11:06:47,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 11:06:47,424 WARNING database DDL Query made to DB:
create table `tabTriage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`triage_description` varchar(140) unique,
`category` varchar(140),
`attend_within` decimal(21,9),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:47,596 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` date,
`from_time` time(6),
`to_date` date,
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` int(1) not null default 0,
`tuesday` int(1) not null default 0,
`wednesday` int(1) not null default 0,
`thursday` int(1) not null default 0,
`friday` int(1) not null default 0,
`saturday` int(1) not null default 0,
`sunday` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:47,731 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`insurance_claim` varchar(140),
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`sales_invoice` varchar(140),
`discount` decimal(21,9) not null default 0,
`claim_coverage` decimal(21,9) not null default 0,
`claim_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:47,956 WARNING database DDL Query made to DB:
create table `tabHealthcare Facility` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_name` varchar(140) unique,
`facility_code` varchar(140),
`facility_level_code` varchar(140),
`certification_no` varchar(140),
`abbreviation_code` varchar(140),
`classification` varchar(140),
`classification_id` varchar(140),
`ward_code` varchar(140),
`postal_address` text,
`owner_code` varchar(140),
`ownership_type_code` varchar(140),
`pay_to_code` varchar(140),
`percent_sent` decimal(21,9) not null default 0,
`certification_application_date` date,
`status` varchar(140),
`has_eclaims` int(1) not null default 0,
`send_amount_to_msd` int(1) not null default 0,
`key_contact` varchar(140),
`email_address` varchar(140),
`telephone_no` varchar(140),
`fax` varchar(140),
`website` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `facility_code`(`facility_code`),
index `facility_level_code`(`facility_level_code`),
index `certification_no`(`certification_no`),
index `abbreviation_code`(`abbreviation_code`),
index `classification`(`classification`),
index `classification_id`(`classification_id`),
index `ward_code`(`ward_code`),
index `owner_code`(`owner_code`),
index `ownership_type_code`(`ownership_type_code`),
index `pay_to_code`(`pay_to_code`),
index `percent_sent`(`percent_sent`),
index `certification_application_date`(`certification_application_date`),
index `status`(`status`),
index `has_eclaims`(`has_eclaims`),
index `send_amount_to_msd`(`send_amount_to_msd`),
index `key_contact`(`key_contact`),
index `email_address`(`email_address`),
index `telephone_no`(`telephone_no`),
index `fax`(`fax`),
index `website`(`website`),
index `longitude`(`longitude`),
index `latitude`(`latitude`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:48,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `medical_code` varchar(140)
2025-06-02 11:06:48,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 11:06:48,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `triage` varchar(140), ADD COLUMN `source` varchar(140), ADD COLUMN `referring_practitioner` varchar(140), ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `insurance_claim` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `claim_status` varchar(140)
2025-06-02 11:06:48,441 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_order_priority` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:48,597 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`mode_of_claim_approval` varchar(140),
`claim_posting_date` date,
`claim_status` varchar(140),
`approval_validity_end_date` date,
`healthcare_service_type` varchar(140),
`service_template` varchar(140),
`service_doctype` varchar(140),
`service_item` varchar(140),
`medical_code` varchar(140),
`price_list_rate` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`coverage` decimal(21,9) not null default 0,
`coverage_amount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`sales_invoice_posting_date` date,
`billing_date` date,
`billing_amount` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:48,717 WARNING database DDL Query made to DB:
create table `tabTherapy Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`sessions_prescribed` int(11) not null default 0,
`sessions_cancelled` int(11) not null default 0,
`sessions_to_cancel` int(11) not null default 0,
`reason` varchar(140),
`encounter_no` varchar(140),
`therapy_plan` varchar(140),
`therapy_session` varchar(140),
`encounter_child_table_id` varchar(140),
`plan_child_table_id` varchar(140),
index `therapy_type`(`therapy_type`),
index `reason`(`reason`),
index `encounter_no`(`encounter_no`),
index `therapy_session`(`therapy_session`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:48,818 WARNING database DDL Query made to DB:
create table `tabDiet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:48,925 WARNING database DDL Query made to DB:
create table `tabSales Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 0,
`quantity_returned` decimal(21,9) not null default 0,
`quantity_serviced` decimal(21,9) not null default 0,
`delivery_note_no` varchar(140),
`dn_detail` varchar(140),
`warehouse` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,031 WARNING database DDL Query made to DB:
create table `tabHealthcare Company Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`service_unit` varchar(140),
`is_not_available` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,154 WARNING database DDL Query made to DB:
create table `tabAllowed Price List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`price_list` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,268 WARNING database DDL Query made to DB:
create table `tabResult Component Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140),
`result_component_option` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,390 WARNING database DDL Query made to DB:
create table `tabMTUHA` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`mtuha` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,523 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`availability_name` varchar(140) unique,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,699 WARNING database DDL Query made to DB:
create table `tabEpisode of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`objective` varchar(140),
`type_of_episode` varchar(140),
`company` varchar(140),
`start_date` date,
`end_date` date,
`status` varchar(140) default 'Planned',
`primary_practitioner` varchar(140),
`care_cordinator` varchar(140),
`initiated_by` varchar(140),
`initiated_via` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:49,986 WARNING database DDL Query made to DB:
create table `tabInsurance Service Frequency` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_frequency_name` varchar(140) unique,
`service_frequency_count` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:50,113 WARNING database DDL Query made to DB:
create table `tabMedication Return` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_name` varchar(140),
`quantity_prescribed` decimal(21,9) not null default 1.0,
`qty_returned` decimal(21,9) not null default 0,
`quantity_to_return` decimal(21,9) not null default 0,
`reason` varchar(140),
`drug_condition` varchar(140),
`encounter_no` varchar(140),
`delivery_note_no` varchar(140),
`status` varchar(140),
`dn_detail` varchar(140),
`child_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:50,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD COLUMN `is_main` int(1) not null default 0, ADD COLUMN `main_department` varchar(140)
2025-06-02 11:06:50,391 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Payment Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`posting_date_type` varchar(140),
`from_date` date,
`to_date` date,
`total_claim_amount` decimal(21,9) not null default 0,
`is_finished` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:50,582 WARNING database DDL Query made to DB:
create table `tabInsurance Coverage Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`insurance_assignment` varchar(140),
`insurance_company` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice__posting_date` date,
`service` varchar(140),
`service_name` varchar(140),
`medical_code` varchar(140),
`mode_of_service__approval` varchar(140),
`service_approval_date` date,
`serviceapproval_reference` text,
`approval_validity_end_date` date,
`requested_quantity` decimal(21,9) not null default 0,
`approved_quantity` decimal(21,9) not null default 0,
`discount` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`claim_posting_date` date,
`insurance_claim_coverage` decimal(21,9) not null default 0,
`insurance_claim_amount` decimal(21,9) not null default 0,
`request_status` varchar(140),
`claim_status` varchar(140),
`approved_amount` decimal(21,9) not null default 0,
`rejected_amount` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:50,709 WARNING database DDL Query made to DB:
create table `tabAllergy Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:51,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `triage` varchar(140), ADD COLUMN `nhif_employername` varchar(140)
2025-06-02 11:06:51,290 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Insurance Coverage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`healthcare_service` varchar(140),
`healthcare_service_template` varchar(140),
`is_active` int(1) not null default 0,
`healthcare_insurance_coverage_plan` varchar(140),
`company` varchar(140),
`has_copayment` int(1) not null default 0,
`approval_mandatory_for_claim` int(1) not null default 0,
`dosage` varchar(140),
`strength` varchar(140),
`maximum_quantity` int(11) not null default 0,
`maximum_quantity_outpatient` int(11) not null default 0,
`maximum_quantity_inpatient` int(11) not null default 0,
`is_auto_generated` int(1) not null default 0,
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `healthcare_service`(`healthcare_service`),
index `healthcare_service_template`(`healthcare_service_template`),
index `is_active`(`is_active`),
index `healthcare_insurance_coverage_plan`(`healthcare_insurance_coverage_plan`),
index `company`(`company`),
index `has_copayment`(`has_copayment`),
index `dosage`(`dosage`),
index `strength`(`strength`),
index `maximum_quantity`(`maximum_quantity`),
index `maximum_quantity_outpatient`(`maximum_quantity_outpatient`),
index `maximum_quantity_inpatient`(`maximum_quantity_inpatient`),
index `is_auto_generated`(`is_auto_generated`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:51,497 WARNING database DDL Query made to DB:
create table `tabHealthcare Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`task` varchar(140),
`task_order` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`service_unit` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`blood_group` varchar(140),
`gender` varchar(140),
`birth_date` date,
`description` text,
`date` date,
`time` time(6),
`inpatient_record` varchar(140),
`remarks` text,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:51,758 WARNING database DDL Query made to DB:
create table `tabHealthcare Insurance Coverage Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`coverage_plan_name` varchar(140) unique,
`insurance_company` varchar(140),
`insurance_company_name` varchar(140),
`company` varchar(140),
`is_active` int(1) not null default 0,
`hms_tz_has_nhif_coverage` int(1) not null default 0,
`is_exclusions` int(1) not null default 0,
`has_followup_charges` int(1) not null default 0,
`has_fasttrack_charges` int(1) not null default 0,
`price_list` varchar(140),
`secondary_price_list` varchar(140),
`nhif_scheme_id` varchar(140),
`nhif_employername` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `has_followup_charges`(`has_followup_charges`),
index `has_fasttrack_charges`(`has_fasttrack_charges`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:51,969 WARNING database DDL Query made to DB:
create table `tabHealthcare Ward Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ward_type_name` varchar(140) unique,
`ward_type_id` int(11) not null default 0,
`item_code` varchar(140),
`notification_required_after` int(11) not null default 0,
`alias` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ward_type_id`(`ward_type_id`),
index `item_code`(`item_code`),
index `notification_required_after`(`notification_required_after`),
index `alias`(`alias`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
