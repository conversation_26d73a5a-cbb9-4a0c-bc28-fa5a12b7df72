2025-06-02 11:17:28,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `nhif_2c_form` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `hms_tz_is_out_of_stock` int(1) not null default 0, ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_discount_percent` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `quantity_returned` int(11) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `invoiced` int(1) not null default 0, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `dn_detail` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `delivery_note` varchar(140)
2025-06-02 11:17:28,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `occurence_period` decimal(21,9)
2025-06-02 11:17:28,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `ward_type` varchar(140), ADD COLUMN `is_service_chargeable` int(1) not null default 0, ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0
2025-06-02 11:17:28,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:17:30,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `vehicle` varchar(140), ADD COLUMN `patient` varchar(140), ADD COLUMN `patient_name` varchar(140), ADD COLUMN `hms_tz_phone_no` varchar(140), ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `hms_tz_appointment_no` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_name` varchar(140), ADD COLUMN `authorization_number` varchar(140), ADD COLUMN `hms_tz_all_items_out_of_stock` int(1) not null default 0, ADD COLUMN `shmh_organogram_department` varchar(140), ADD COLUMN `hms_tz_comment` text, ADD COLUMN `department` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 11:17:30,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-06-02 11:17:31,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `insurance_subscription` varchar(140), ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `mtuha` varchar(140), ADD COLUMN `healthcare_notes_template` varchar(140), ADD COLUMN `procedure_notes` longtext, ADD COLUMN `pre_operative_notes_template` varchar(140), ADD COLUMN `pre_operative_note` longtext, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 11:17:31,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 11:17:33,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `approval_date` date, ADD COLUMN `hms_tz_is_out_of_stock` int(1) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `department` varchar(140), ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140), ADD COLUMN `reference_doctype` varchar(140), ADD COLUMN `reference_name` varchar(140), ADD COLUMN `original_item` varchar(140), ADD COLUMN `original_stock_uom_qty` decimal(21,9) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `last_date_prescribed` date, ADD COLUMN `last_qty_prescribed` decimal(21,9) not null default 0, ADD COLUMN `recommended_qty` decimal(21,9) not null default 0
2025-06-02 11:17:33,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-06-02 11:17:34,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `card_no` varchar(140), ADD COLUMN `insurance_card_detail` text, ADD COLUMN `product_code` varchar(140), ADD COLUMN `membership_no` varchar(140), ADD COLUMN `scheme_id` varchar(140), ADD COLUMN `national_id` varchar(140) unique, ADD COLUMN `insurance_provider` varchar(140), ADD COLUMN `common_occupation` varchar(140), ADD COLUMN `ethnicity` varchar(140), ADD COLUMN `nida_card_number` varchar(140), ADD COLUMN `area` varchar(140), ADD COLUMN `demography` varchar(140), ADD COLUMN `how_did_you_hear_about_us` varchar(140), ADD COLUMN `referred_from` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `patient_signature` longtext, ADD COLUMN `cash_limit` decimal(21,9) not null default 0, ADD COLUMN `next_to_kin_name` varchar(140), ADD COLUMN `next_to_kin_mobile_no` varchar(140), ADD COLUMN `next_to_kin_relationship` varchar(140), ADD COLUMN `patient_details_with_formatting` longtext
2025-06-02 11:17:34,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD UNIQUE INDEX IF NOT EXISTS national_id (`national_id`)
2025-06-02 11:17:35,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 11:17:36,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `mobile` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `ref_vital_signs` varchar(140), ADD COLUMN `ref_patient_encounter` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `has_no_consultation_charges` int(1) not null default 0, ADD COLUMN `healthcare_referrer_type` varchar(140), ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `referral_no` varchar(140), ADD COLUMN `remarks` text, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `coverage_plan_card_number` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `nhif_employer_name` varchar(140), ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `nhif_patient_claim` varchar(140), ADD COLUMN `jubilee_patient_claim` varchar(140), ADD COLUMN `authorization_number` varchar(140), ADD COLUMN `years_of_insurance` int(11) not null default 0, ADD COLUMN `apply_fasttrack_charge` int(1) not null default 0, ADD COLUMN `insurance_company_name` varchar(140), ADD COLUMN `require_fingerprint` int(1) not null default 0, ADD COLUMN `require_facial_recognation` int(1) not null default 0, ADD COLUMN `biometric_method` varchar(140), ADD COLUMN `fpcode` varchar(140), ADD COLUMN `payment_reference` varchar(140), ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `old_hms_number` varchar(140), ADD COLUMN `patient_image2` text, ADD COLUMN `sms_sent` int(1) not null default 0
2025-06-02 11:17:37,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:17:37,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `override_insurance_subscription` int(1) not null default 0, ADD COLUMN `hso_payment_method` varchar(140), ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
2025-06-02 11:17:41,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `insurance_coverage_plan` varchar(140), ADD COLUMN `image` text, ADD COLUMN `blood_group` varchar(140), ADD COLUMN `old_hms_registration_no` varchar(140), ADD COLUMN `healthcare_package_order` varchar(140), ADD COLUMN `admission_service_unit_type` varchar(140), ADD COLUMN `encounter_category` varchar(140), ADD COLUMN `encounter_mode_of_payment` varchar(140), ADD COLUMN `sales_invoice` varchar(140), ADD COLUMN `price_list` varchar(140), ADD COLUMN `abbr` varchar(140), ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `follow_up` int(1) not null default 0, ADD COLUMN `healthcare_referrer` varchar(140), ADD COLUMN `hms_tz_previous_examination_detail` longtext, ADD COLUMN `examination_detail` longtext, ADD COLUMN `encounter_type` varchar(140), ADD COLUMN `finalized` int(1) not null default 0, ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `lab_bundle` varchar(140), ADD COLUMN `default_healthcare_service_unit` varchar(140), ADD COLUMN `ed_reason_for_absence` varchar(140), ADD COLUMN `ed_addressed_to` text, ADD COLUMN `ed_no_of_days` int(11) not null default 0, ADD COLUMN `patient_signature` longtext, ADD COLUMN `healthcare_practitioner_signature` longtext, ADD COLUMN `previous_total` decimal(21,9) not null default 0, ADD COLUMN `current_total` decimal(21,9) not null default 0, ADD COLUMN `is_not_billable` int(1) not null default 0, ADD COLUMN `duplicated` int(1) not null default 0, ADD COLUMN `reference_encounter` varchar(140), ADD COLUMN `from_encounter` varchar(140), ADD COLUMN `has_preapproval` int(1) not null default 0
2025-06-02 11:17:42,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140)
2025-06-02 11:17:42,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:17:43,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` ADD COLUMN `image` text, ADD COLUMN `mode_of_payment` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `shm_coverage_plan_name` varchar(140), ADD COLUMN `patient_progress` varchar(140), ADD COLUMN `oxygen_saturation_spo2` varchar(140), ADD COLUMN `rbg` decimal(21,9) not null default 0, ADD COLUMN `visual_acuity_re` varchar(140), ADD COLUMN `visual_acuity_le` varchar(140), ADD COLUMN `intraocular_pressure_re` varchar(140), ADD COLUMN `intraocular_pressure_le` varchar(140), ADD COLUMN `eye_opening` varchar(140), ADD COLUMN `verbal_response` varchar(140), ADD COLUMN `motor_response` varchar(140), ADD COLUMN `height_in_cm` int(11) not null default 0
2025-06-02 11:17:43,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `height` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-06-02 11:17:44,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `hms_tz_patient_age` varchar(140), ADD COLUMN `hms_tz_patient_sex` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `healthcare_practitioner_name` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `body_part` varchar(140), ADD COLUMN `radiology_report` varchar(140), ADD COLUMN `radiology_report_details` longtext, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `hms_tz_submitted_date` date
2025-06-02 11:17:44,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD COLUMN `is_non_specific` int(1) not null default 0
2025-06-02 11:17:44,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `hms_tz_appointment` varchar(140), ADD COLUMN `inpatient_record` varchar(140), ADD COLUMN `hms_tz_patient_age` varchar(140), ADD COLUMN `hms_tz_patient_sex` varchar(140), ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `total_sessions_cancelled` int(11) not null default 0, ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140)
2025-06-02 11:17:45,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `room_type` varchar(140), ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0, ADD COLUMN `is_service_chargeable` int(1) not null default 0
2025-06-02 11:17:46,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `has_no_consultation_charges_for_cash` int(1) not null default 0, ADD COLUMN `description` text, ADD COLUMN `visit_type_id` varchar(140), ADD COLUMN `visit_type_name_alias` varchar(140), ADD COLUMN `super_specialist_fasttrack_item` varchar(140), ADD COLUMN `gp_fasttrack_item` varchar(140), ADD COLUMN `assistant_md_followup_item` varchar(140), ADD COLUMN `required_input` varchar(140), ADD COLUMN `specialist_followup_item` varchar(140), ADD COLUMN `assistant_md_fasttrack_item` varchar(140), ADD COLUMN `super_specialist_followup_item` varchar(140), ADD COLUMN `gp_followup_item` varchar(140), ADD COLUMN `specialist_fasttrack_item` varchar(140), ADD COLUMN `has_no_consultation_charges_for_insurance` int(1) not null default 0, ADD COLUMN `source` varchar(140), ADD COLUMN `requires_remarks` int(1) not null default 0, ADD COLUMN `has_followup_charges` int(1) not null default 0, ADD COLUMN `requires_referral_no` int(1) not null default 0, ADD COLUMN `has_fasttrack_charges` int(1) not null default 0, ADD COLUMN `maximum_visit_per_month` varchar(140)
2025-06-02 11:17:47,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `sessions_cancelled` int(11) not null default 0, ADD COLUMN `comment` text, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `invoiced` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `therapy_plan_created` int(1) not null default 0
2025-06-02 11:17:47,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `title_and_qualification` varchar(140), ADD COLUMN `abbreviation` varchar(140), ADD COLUMN `hms_tz_company` varchar(140), ADD COLUMN `tz_mct_code` varchar(140), ADD COLUMN `nhif_physician_qualification` varchar(140), ADD COLUMN `bypass_vitals` int(1) not null default 0, ADD COLUMN `doctors_signature` longtext, ADD COLUMN `national_id` varchar(140), ADD COLUMN `date_loggedin_to_nhif` date, ADD COLUMN `default_medication_healthcare_service_unit` varchar(140), ADD COLUMN `shm_doctor_organogram_department` varchar(140)
2025-06-02 11:17:47,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 11:17:48,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `dosage_info` text, ADD COLUMN `hms_tz_is_lrp_item_created` int(1) not null default 0, ADD COLUMN `service_request` varchar(140), ADD COLUMN `healthcare_service_unit` varchar(140), ADD COLUMN `healthcare_practitioner` varchar(140), ADD COLUMN `department` varchar(140), ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:17:48,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-06-02 11:17:49,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `title` varchar(140), ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `approval_number` varchar(140), ADD COLUMN `approval_type` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140), ADD COLUMN `appointment` varchar(140), ADD COLUMN `service_comment` text, ADD COLUMN `hms_tz_insurance_coverage_plan` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `hms_tz_ref_childname` varchar(140), ADD COLUMN `hms_tz_submitted_by` varchar(140), ADD COLUMN `hms_tz_user_id` varchar(140), ADD COLUMN `workflow_state` varchar(140)
2025-06-02 11:17:49,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` ADD COLUMN `daily_limit` decimal(21,9) not null default 0, ADD COLUMN `coverage_plan_name` varchar(140), ADD COLUMN `coverage_plan_card_number` varchar(140), ADD COLUMN `company` varchar(140), ADD COLUMN `verifier_id` varchar(140), ADD COLUMN `card_type_id` varchar(140), ADD COLUMN `card_type_name` varchar(140), ADD COLUMN `hms_tz_product_code` varchar(140), ADD COLUMN `hms_tz_product_name` varchar(140), ADD COLUMN `hms_tz_scheme_id` varchar(140), ADD COLUMN `hms_tz_scheme_name` varchar(140), ADD COLUMN `national_id` varchar(140)
2025-06-02 11:28:14,989 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_4e5b33133c0fbbea'@'localhost'
2025-06-02 11:28:25,367 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_4e5b33133c0fbbea`
2025-06-02 11:28:25,372 WARNING database DDL Query made to DB:
CREATE USER '_4e5b33133c0fbbea'@'localhost' IDENTIFIED BY 'CfIdPkiZZJ4qSUSA'
2025-06-02 11:28:25,374 WARNING database DDL Query made to DB:
CREATE DATABASE `_4e5b33133c0fbbea` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-02 11:29:47,690 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 11:29:47,715 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:33,948 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 11:30:37,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion` MODIFY `name` varchar(140)
2025-06-02 11:30:37,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabError Log` MODIFY `name` varchar(140)
2025-06-02 11:30:39,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Log` MODIFY `name` varchar(140)
2025-06-02 11:30:39,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Sync Log` MODIFY `name` varchar(140)
2025-06-02 11:30:39,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Update Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccess Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabView Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabActivity Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabEnergy Point Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `name` varchar(140)
2025-06-02 11:30:40,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue` MODIFY `name` varchar(140)
2025-06-02 11:30:41,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocShare` MODIFY `name` varchar(140)
2025-06-02 11:30:41,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Follow` MODIFY `name` varchar(140)
2025-06-02 11:30:41,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabConsole Log` MODIFY `name` varchar(140)
2025-06-02 11:30:44,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `link_filters` json, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `placeholder` varchar(140)
2025-06-02 11:30:45,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `documentation_url` varchar(140)
2025-06-02 11:30:46,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `row_format` varchar(140) default 'Dynamic'
2025-06-02 11:30:46,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom Field` ADD COLUMN `placeholder` varchar(140), ADD COLUMN `link_filters` json, ADD COLUMN `show_dashboard` int(1) not null default 0
2025-06-02 11:30:46,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` ADD COLUMN `allowed_embedding_domains` text
2025-06-02 11:30:46,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `condition_json` json
2025-06-02 11:30:47,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form Field` ADD COLUMN `precision` varchar(140)
2025-06-02 11:30:47,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `currency` varchar(140)
2025-06-02 11:30:47,501 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `currency` varchar(140)
2025-06-02 11:30:47,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Link` ADD COLUMN `description` longtext, ADD COLUMN `report_ref_doctype` varchar(140)
2025-06-02 11:30:48,125 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` ADD COLUMN `report_ref_doctype` varchar(140)
2025-06-02 11:30:48,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace` ADD COLUMN `indicator_color` varchar(140)
2025-06-02 11:30:48,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace` MODIFY `sequence_id` decimal(21,9) not null default 0
2025-06-02 11:30:48,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabPage` ADD UNIQUE INDEX IF NOT EXISTS page_name (`page_name`)
2025-06-02 11:30:48,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport` ADD COLUMN `add_translate_data` int(1) not null default 0, ADD COLUMN `timeout` int(11) not null default 0
2025-06-02 11:30:49,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `pdf_generator` varchar(140) default 'wkhtmltopdf'
2025-06-02 11:30:49,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page` MODIFY `idx` int(11) not null default 0
2025-06-02 11:30:49,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Style` ADD UNIQUE INDEX IF NOT EXISTS print_style_name (`print_style_name`)
2025-06-02 11:30:50,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabServer Script` ADD COLUMN `enable_rate_limit` int(1) not null default 0, ADD COLUMN `rate_limit_count` int(11) not null default 5, ADD COLUMN `rate_limit_seconds` int(11) not null default 86400
2025-06-02 11:30:50,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabServer Script` ADD INDEX `reference_doctype_index`(`reference_doctype`), ADD INDEX `module_index`(`module`)
2025-06-02 11:30:50,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuccess Action` ADD UNIQUE INDEX IF NOT EXISTS ref_doctype (`ref_doctype`)
2025-06-02 11:30:51,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabNavbar Item` ADD COLUMN `condition` longtext
2025-06-02 11:30:51,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabData Import Log` ADD INDEX `creation`(`creation`)
2025-06-02 11:30:51,483 WARNING database DDL Query made to DB:
create table `tabAmended Document Naming Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`action` varchar(140) default 'Amend Counter',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:51,597 WARNING database DDL Query made to DB:
create table `tabSubmission Queue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`job_id` varchar(140),
`ended_at` datetime(6),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`exception` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ref_docname`(`ref_docname`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:52,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Log` ADD COLUMN `debug_log` longtext
2025-06-02 11:30:52,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabError Log` ADD COLUMN `trace_id` varchar(140)
2025-06-02 11:30:53,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabScheduled Job Type` ADD COLUMN `scheduler_event` varchar(140)
2025-06-02 11:30:53,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrepared Report` ADD COLUMN `peak_memory_usage` int(11) not null default 0
2025-06-02 11:30:53,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrepared Report` ADD INDEX `status_index`(`status`), ADD INDEX `report_name_index`(`report_name`)
2025-06-02 11:30:53,897 WARNING database DDL Query made to DB:
create table `tabScheduler Event` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheduled_against` varchar(140),
`method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:54,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-06-02 11:30:54,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication` ADD COLUMN `send_after` datetime(6)
2025-06-02 11:30:54,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `search_bar` int(1) not null default 1, ADD COLUMN `notifications` int(1) not null default 1, ADD COLUMN `list_sidebar` int(1) not null default 1, ADD COLUMN `bulk_actions` int(1) not null default 1, ADD COLUMN `view_switcher` int(1) not null default 1, ADD COLUMN `form_sidebar` int(1) not null default 1, ADD COLUMN `timeline` int(1) not null default 1, ADD COLUMN `dashboard` int(1) not null default 1, ADD COLUMN `default_workspace` varchar(140), ADD COLUMN `default_app` varchar(140)
2025-06-02 11:30:54,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD INDEX `last_active_index`(`last_active`)
2025-06-02 11:30:55,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabView Log` DROP INDEX `viewed_by`, DROP INDEX `reference_doctype`
2025-06-02 11:30:55,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` MODIFY `value` varchar(255)
2025-06-02 11:30:56,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlog Category` ADD COLUMN `description` text, ADD COLUMN `preview_image` text
2025-06-02 11:30:56,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD COLUMN `source` varchar(140), ADD COLUMN `campaign` varchar(140), ADD COLUMN `medium` varchar(140), ADD COLUMN `visitor_id` varchar(140)
2025-06-02 11:30:57,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD INDEX `path_index`(`path`), ADD INDEX `visitor_id_index`(`visitor_id`)
2025-06-02 11:30:57,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Route Redirect` ADD COLUMN `redirect_http_status` varchar(140) default '301'
2025-06-02 11:30:57,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Sidebar` ADD UNIQUE INDEX IF NOT EXISTS title (`title`)
2025-06-02 11:30:58,128 WARNING database DDL Query made to DB:
create table `tabMarketing Campaign` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`campaign_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:30:58,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlog Post` MODIFY `content_type` varchar(140) default 'Markdown'
2025-06-02 11:30:58,668 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1, ADD COLUMN `workflow_builder_id` varchar(140)
2025-06-02 11:30:58,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` MODIFY `doc_status` varchar(140) default '0'
2025-06-02 11:30:58,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` ADD COLUMN `send_email_to_creator` int(1) not null default 0, ADD COLUMN `workflow_builder_id` varchar(140)
2025-06-02 11:30:59,276 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow` ADD COLUMN `workflow_data` json
2025-06-02 11:30:59,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Domain` ADD COLUMN `sent_folder_name` varchar(140) default 'Sent'
2025-06-02 11:31:00,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Account` ADD COLUMN `backend_app_flow` int(1) not null default 0, ADD COLUMN `sent_folder_name` varchar(140), ADD COLUMN `always_bcc` varchar(140)
2025-06-02 11:31:00,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient`
				ADD INDEX IF NOT EXISTS `modified_index`(modified)
2025-06-02 11:31:00,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabNewsletter` ADD COLUMN `total_views` int(11) not null default 0, ADD COLUMN `campaign` varchar(140)
2025-06-02 11:31:01,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuto Email Report` ADD COLUMN `use_first_day_of_period` int(1) not null default 0, ADD COLUMN `sender` varchar(140)
2025-06-02 11:31:01,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomize Form Field` ADD COLUMN `link_filters` json, ADD COLUMN `placeholder` varchar(140)
2025-06-02 11:31:01,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency` MODIFY `smallest_currency_fraction_value` decimal(21,9) not null default 0
2025-06-02 11:31:02,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabConsole Log` ADD COLUMN `type` varchar(140), ADD COLUMN `committed` int(1) not null default 0
2025-06-02 11:31:03,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-06-02 11:31:03,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` ADD COLUMN `link` varchar(140)
2025-06-02 11:31:03,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesktop Icon` MODIFY `idx` int(11) not null default 0
2025-06-02 11:31:03,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabList View Settings` ADD COLUMN `disable_comment_count` int(1) not null default 0, ADD COLUMN `allow_edit` int(1) not null default 0, ADD COLUMN `disable_automatic_recency_filters` int(1) not null default 0
2025-06-02 11:31:04,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_calendar_event_id` varchar(320), MODIFY `google_meet_link` text
2025-06-02 11:31:04,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Header` MODIFY `value` text, MODIFY `key` text
2025-06-02 11:31:04,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook` ADD COLUMN `is_dynamic_url` int(1) not null default 0, ADD COLUMN `background_jobs_queue` varchar(140)
2025-06-02 11:31:05,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabSocial Login Key` ADD COLUMN `sign_ups` varchar(140)
2025-06-02 11:31:05,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoogle Calendar` ADD COLUMN `sync_as_public` int(1) not null default 0
2025-06-02 11:31:06,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Request Log` MODIFY `url` text
2025-06-02 11:31:06,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabLetter Head` MODIFY `image_width` decimal(21,9) not null default 0, MODIFY `footer_image_width` decimal(21,9) not null default 0, MODIFY `footer_image_height` decimal(21,9) not null default 0, MODIFY `image_height` decimal(21,9) not null default 0
2025-06-02 11:31:06,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress Template` ADD UNIQUE INDEX IF NOT EXISTS country (`country`)
2025-06-02 11:31:07,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `full_name` varchar(140)
2025-06-02 11:31:07,938 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:08,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry` MODIFY `total_quantity` decimal(21,9) not null default 0
2025-06-02 11:31:08,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` ADD COLUMN `voucher_subtype` text, ADD COLUMN `transaction_currency` varchar(140), ADD COLUMN `transaction_exchange_rate` decimal(21,9) not null default 0, ADD COLUMN `debit_in_transaction_currency` decimal(21,9) not null default 0, ADD COLUMN `credit_in_transaction_currency` decimal(21,9) not null default 0
2025-06-02 11:31:08,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` MODIFY `credit` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0
2025-06-02 11:31:08,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry` ADD INDEX `creation`(`creation`)
2025-06-02 11:31:08,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `posting_date_company_index`(posting_date, company)
2025-06-02 11:31:08,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabGL Entry`
				ADD INDEX IF NOT EXISTS `party_type_party_index`(party_type, party)
2025-06-02 11:31:09,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)', ADD COLUMN `ignore_cr_dr_notes` int(1) not null default 0, ADD COLUMN `show_remarks` int(1) not null default 0
2025-06-02 11:31:09,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `sales_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `rejected_serial_and_batch_bundle` varchar(140), ADD COLUMN `material_request` varchar(140), ADD COLUMN `material_request_item` varchar(140)
2025-06-02 11:31:09,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:09,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`), ADD INDEX `batch_no_index`(`batch_no`), ADD INDEX `material_request_index`(`material_request`), ADD INDEX `material_request_item_index`(`material_request_item`), ADD INDEX `project_index`(`project`)
2025-06-02 11:31:09,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `book_advance_payments_in_separate_party_account` int(1) not null default 0, ADD COLUMN `reconcile_on_advance_payment_date` int(1) not null default 0, ADD COLUMN `base_in_words` text, ADD COLUMN `is_opening` varchar(140) default 'No', ADD COLUMN `in_words` text
2025-06-02 11:31:10,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0
2025-06-02 11:31:10,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD INDEX `is_opening_index`(`is_opening`)
2025-06-02 11:31:10,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-06-02 11:31:10,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD INDEX `account_type_index`(`account_type`)
2025-06-02 11:31:10,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `payment_term_outstanding` decimal(21,9) not null default 0, ADD COLUMN `account_type` varchar(140), ADD COLUMN `payment_type` varchar(140), ADD COLUMN `reconcile_effect_on` date, ADD COLUMN `account` varchar(140), ADD COLUMN `payment_request` varchar(140)
2025-06-02 11:31:10,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-02 11:31:11,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` ADD COLUMN `company_contact_person` varchar(140)
2025-06-02 11:31:11,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-06-02 11:31:11,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` ADD COLUMN `is_exchange_gain_loss` int(1) not null default 0
2025-06-02 11:31:11,672 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:11,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabPeriod Closing Voucher` ADD COLUMN `period_start_date` date, ADD COLUMN `period_end_date` date
2025-06-02 11:31:12,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Taxes` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:12,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:12,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0, ADD COLUMN `pos_invoice` varchar(140), ADD COLUMN `pos_invoice_item` varchar(140)
2025-06-02 11:31:12,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-02 11:31:12,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`), ADD INDEX `pos_invoice_index`(`pos_invoice`), ADD INDEX `project_index`(`project`), ADD INDEX `creation`(`creation`)
2025-06-02 11:31:12,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension` ADD INDEX `document_type_index`(`document_type`)
2025-06-02 11:31:12,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Statement Import` ADD COLUMN `custom_delimiters` int(1) not null default 0, ADD COLUMN `delimiter_options` varchar(140) default ',;\\t|'
2025-06-02 11:31:13,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension Filter` ADD COLUMN `apply_restriction_on_values` int(1) not null default 1
2025-06-02 11:31:13,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0
2025-06-02 11:31:13,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Type` ADD COLUMN `is_default` int(1) not null default 0, ADD COLUMN `company` varchar(140), ADD COLUMN `income_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-02 11:31:13,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Type` MODIFY `dunning_fee` decimal(21,9) not null default 0, MODIFY `rate_of_interest` decimal(21,9) not null default 0
2025-06-02 11:31:13,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabMonthly Distribution` ADD UNIQUE INDEX IF NOT EXISTS distribution_id (`distribution_id`)
2025-06-02 11:31:14,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` ADD COLUMN `gain_loss_posting_date` date
2025-06-02 11:31:14,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` MODIFY `unreconciled_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:14,476 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation` ADD COLUMN `default_advance_account` varchar(140)
2025-06-02 11:31:14,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` ADD COLUMN `difference_posting_date` date
2025-06-02 11:31:14,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `ref_exchange_rate` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-02 11:31:14,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Plan` MODIFY `cost` decimal(21,9) not null default 0
2025-06-02 11:31:14,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry` MODIFY `amount_in_account_currency` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:31:15,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription` ADD COLUMN `generate_invoice_at` varchar(140) default 'End of the current subscription period', ADD COLUMN `number_of_days` int(11) not null default 0
2025-06-02 11:31:15,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription` MODIFY `additional_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-06-02 11:31:15,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning` ADD COLUMN `total_interest` decimal(21,9) not null default 0, ADD COLUMN `base_dunning_amount` decimal(21,9) not null default 0, ADD COLUMN `spacer` varchar(140), ADD COLUMN `total_outstanding` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `customer_address` varchar(140), ADD COLUMN `contact_person` varchar(140), ADD COLUMN `company_address` varchar(140)
2025-06-02 11:31:15,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning` MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-02 11:31:16,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-06-02 11:31:16,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-02 11:31:16,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Price Discount` ADD COLUMN `for_price_list` varchar(140)
2025-06-02 11:31:16,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Price Discount` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:16,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-02 11:31:16,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD COLUMN `base_outstanding` decimal(21,9) not null default 0, ADD COLUMN `base_paid_amount` decimal(21,9) not null default 0
2025-06-02 11:31:16,984 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` MODIFY `invoice_portion` decimal(21,9) not null default 0, MODIFY `base_payment_amount` decimal(21,9) not null default 0, MODIFY `outstanding` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `payment_amount` decimal(21,9) not null default 0, MODIFY `discount` decimal(21,9) not null default 0
2025-06-02 11:31:17,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` ADD INDEX `creation`(`creation`)
2025-06-02 11:31:17,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Product Discount` ADD COLUMN `round_free_qty` int(1) not null default 0, ADD COLUMN `recurse_for` decimal(21,9) not null default 0, ADD COLUMN `apply_recursion_over` decimal(21,9) not null default 0
2025-06-02 11:31:17,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Product Discount` MODIFY `free_qty` decimal(21,9) not null default 0, MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0
2025-06-02 11:31:17,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext, ADD COLUMN `subscription` varchar(140), ADD COLUMN `supplier_group` varchar(140)
2025-06-02 11:31:17,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-02 11:31:18,004 WARNING database DDL Query made to DB:
create table `tabProcess Statement Of Accounts CC` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cc` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:18,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `print_receipt_on_order_complete` int(1) not null default 0, ADD COLUMN `disable_grand_total_to_default_mop` int(1) not null default 0, ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-06-02 11:31:18,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-06-02 11:31:18,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` MODIFY `taxable_amount` decimal(21,9) not null default 0
2025-06-02 11:31:18,807 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` ADD COLUMN `dont_enforce_free_item_qty` int(1) not null default 0
2025-06-02 11:31:18,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `threshold_percentage` decimal(21,9) not null default 0, MODIFY `recurse_for` decimal(21,9) not null default 0, MODIFY `min_qty` decimal(21,9) not null default 0, MODIFY `free_item_rate` decimal(21,9) not null default 0, MODIFY `max_qty` decimal(21,9) not null default 0
2025-06-02 11:31:19,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` ADD COLUMN `is_tax_withholding_account` int(1) not null default 0
2025-06-02 11:31:19,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-02 11:31:19,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` ADD COLUMN `difference_posting_date` date
2025-06-02 11:31:19,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `ref_exchange_rate` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0
2025-06-02 11:31:19,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Payment` ADD COLUMN `reference_no` varchar(140)
2025-06-02 11:31:19,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Payment` MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-02 11:31:19,683 WARNING database DDL Query made to DB:
create table `tabOverdue Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sales_invoice` varchar(140),
`payment_schedule` varchar(140),
`dunning_level` int(11) not null default 1,
`payment_term` varchar(140),
`description` text,
`due_date` date,
`overdue_days` varchar(140),
`mode_of_payment` varchar(140),
`invoice_portion` decimal(21,9) not null default 0,
`payment_amount` decimal(21,9) not null default 0,
`outstanding` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`discounted_amount` decimal(21,9) not null default 0,
`interest` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:19,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Account` ADD COLUMN `advance_account` varchar(140)
2025-06-02 11:31:20,053 WARNING database DDL Query made to DB:
create table `tabProcess Subscription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`subscription` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:20,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `company_contact_person` varchar(140), ADD COLUMN `subscription` varchar(140)
2025-06-02 11:31:20,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-06-02 11:31:20,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD INDEX `project_index`(`project`)
2025-06-02 11:31:20,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0
2025-06-02 11:31:20,915 WARNING database DDL Query made to DB:
create table `tabAdvance Payment Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`against_voucher_type` varchar(140),
`against_voucher_no` varchar(140),
`amount` decimal(21,9) not null default 0,
`currency` varchar(140),
`event` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:21,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `company` varchar(140), ADD COLUMN `party_name` varchar(140), ADD COLUMN `outstanding_amount` decimal(21,9) not null default 0, ADD COLUMN `party_account_currency` varchar(140), ADD COLUMN `phone_number` varchar(140)
2025-06-02 11:31:21,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `payment_url` varchar(500)
2025-06-02 11:31:21,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD INDEX `reference_name_index`(`reference_name`)
2025-06-02 11:31:22,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Transfer` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:31:23,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-02 11:31:23,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-02 11:31:23,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect` ADD INDEX `creation`(`creation`)
2025-06-02 11:31:23,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-02 11:31:23,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-06-02 11:31:24,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 11:31:24,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` ADD COLUMN `subcontracted_quantity` decimal(21,9) not null default 0, ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-02 11:31:24,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-02 11:31:24,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-02 11:31:24,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-06-02 11:31:25,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:31:25,625 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-02 11:31:25,650 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-02 11:31:26,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-02 11:31:26,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-06-02 11:31:26,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` ADD COLUMN `hide_timesheets` int(1) not null default 0
2025-06-02 11:31:26,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0
2025-06-02 11:31:27,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Update` ADD INDEX `date_index`(`date`)
2025-06-02 11:31:29,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-06-02 11:31:29,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD INDEX `collect_progress_index`(`collect_progress`)
2025-06-02 11:31:30,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `reserve_stock` int(1) not null default 0, ADD COLUMN `company_contact_person` varchar(140)
2025-06-02 11:31:30,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0
2025-06-02 11:31:30,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD INDEX `project_index`(`project`)
2025-06-02 11:31:30,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `prospect_name` varchar(140)
2025-06-02 11:31:30,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-02 11:31:31,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` ADD COLUMN `company_total_stock` decimal(21,9) not null default 0, ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-02 11:31:31,293 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
2025-06-02 11:31:31,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `disable_rounded_total` int(1) not null default 0, ADD COLUMN `company_contact_person` varchar(140)
2025-06-02 11:31:31,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-06-02 11:31:31,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `is_stock_item` int(1) not null default 0, ADD COLUMN `reserve_stock` int(1) not null default 1, ADD COLUMN `stock_reserved_qty` decimal(21,9) not null default 0, ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `project` varchar(140)
2025-06-02 11:31:31,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-06-02 11:31:31,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD INDEX `project_index`(`project`)
2025-06-02 11:31:32,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-02 11:31:32,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-02 11:31:32,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 11:31:33,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-06-02 11:31:33,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:31:34,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `round_off_for_opening` varchar(140), ADD COLUMN `book_advance_payments_in_separate_party_account` int(1) not null default 0, ADD COLUMN `reconcile_on_advance_payment_date` int(1) not null default 0, ADD COLUMN `reconciliation_takes_effect_on` varchar(140) default 'Oldest Of Invoice Or Advance', ADD COLUMN `default_advance_received_account` varchar(140), ADD COLUMN `default_advance_paid_account` varchar(140), ADD COLUMN `expenses_included_in_asset_valuation` varchar(140), ADD COLUMN `expenses_included_in_valuation` varchar(140), ADD COLUMN `default_operating_cost_account` varchar(140)
2025-06-02 11:31:34,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:31:34,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuthorization Rule` MODIFY `value` decimal(21,9) not null default 0
2025-06-02 11:31:34,976 WARNING database DDL Query made to DB:
create table `tabPlant Floor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`floor_name` varchar(140) unique,
`company` varchar(140),
`warehouse` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:31:35,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` ADD COLUMN `plant_floor` varchar(140), ADD COLUMN `warehouse` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `on_status_image` text, ADD COLUMN `off_status_image` text, ADD COLUMN `total_working_hours` decimal(21,9) not null default 0
2025-06-02 11:31:35,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `hour_rate_consumable` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate_electricity` decimal(21,9) not null default 0, MODIFY `hour_rate_rent` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0
2025-06-02 11:31:35,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` MODIFY `planned_qty` decimal(21,9) not null default 0
2025-06-02 11:31:35,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `cost_per_unit` decimal(21,9) not null default 0, MODIFY `base_cost_per_unit` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0
2025-06-02 11:31:35,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` ADD COLUMN `stock_uom` varchar(140)
