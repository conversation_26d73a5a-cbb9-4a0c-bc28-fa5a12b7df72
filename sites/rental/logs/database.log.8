2025-06-02 11:06:52,101 WARNING database DDL Query made to DB:
create table `tabResult Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`result_component` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:52,276 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-02 11:06:52,587 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`encounter` varchar(140),
`referral_type` varchar(140),
`posting_date` datetime(6),
`appointment` varchar(140),
`patient` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`authorization_no` varchar(140),
`dob` date,
`attendance_date` date,
`patient_type_code` varchar(140),
`mobile_no` varchar(140),
`gender` varchar(140),
`insurance_company` varchar(140),
`source_facility` varchar(140),
`source_facility_code` varchar(140),
`referrer_facility` varchar(140),
`referrer_facility_code` varchar(140),
`referral_status` varchar(140),
`referral_date` datetime(6),
`practitioner` varchar(140),
`practitioner_no` varchar(140),
`reason_for_referral` longtext,
`referral_no` varchar(140),
`referral_id` varchar(140),
`naming_series` varchar(140),
`referral_submitted_by` varchar(140),
`referral_updated_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `encounter`(`encounter`),
index `referral_type`(`referral_type`),
index `posting_date`(`posting_date`),
index `appointment`(`appointment`),
index `patient`(`patient`),
index `first_name`(`first_name`),
index `last_name`(`last_name`),
index `patient_name`(`patient_name`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `authorization_no`(`authorization_no`),
index `dob`(`dob`),
index `attendance_date`(`attendance_date`),
index `patient_type_code`(`patient_type_code`),
index `mobile_no`(`mobile_no`),
index `insurance_company`(`insurance_company`),
index `source_facility`(`source_facility`),
index `source_facility_code`(`source_facility_code`),
index `referrer_facility`(`referrer_facility`),
index `referrer_facility_code`(`referrer_facility_code`),
index `referral_status`(`referral_status`),
index `referral_date`(`referral_date`),
index `practitioner`(`practitioner`),
index `practitioner_no`(`practitioner_no`),
index `referral_no`(`referral_no`),
index `referral_id`(`referral_id`),
index `referral_submitted_by`(`referral_submitted_by`),
index `referral_updated_by`(`referral_updated_by`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:52,732 WARNING database DDL Query made to DB:
create table `tabHealthcare Room Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`room_type_name` varchar(140) unique,
`alias` varchar(140),
`room_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `alias`(`alias`),
index `room_type_id`(`room_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:52,890 WARNING database DDL Query made to DB:
create table `tabRadiology Examination Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`procedure_name` varchar(140) unique,
`abbr` varchar(140) unique,
`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`description` text,
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`modality_type` varchar(140),
`healthcare_service_unit_type` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`accession_number` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,028 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`qty` int(11) not null default 0,
`approval_ref_no` varchar(140),
`notes` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `qty`(`qty`),
index `approval_ref_no`(`approval_ref_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,140 WARNING database DDL Query made to DB:
create table `tabAllergy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`allergy` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,249 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Nursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`check_list` varchar(140),
`task` varchar(140),
`expected_time` int(11) not null default 0,
`nursing_task_reference` varchar(140),
`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,372 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`disease_code` varchar(140),
`description` text,
index `status`(`status`),
index `disease_code`(`disease_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,492 WARNING database DDL Query made to DB:
create table `tabLab Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_bundle_name` varchar(140) unique,
`public` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:53,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication Class` ADD COLUMN `prescribed_after` int(11) not null default 0
2025-06-02 11:06:53,766 WARNING database DDL Query made to DB:
create table `tabRadiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:54,100 WARNING database DDL Query made to DB:
create table `tabHospital Revenue Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`appointment` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`posting_date` varchar(140),
`company` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`price_list` varchar(140),
`currency` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`qty_returned` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`is_cancelled` int(1) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`sales_invoice` varchar(140),
`healthcare_practitioner` varchar(140),
`healthcare_service_unit` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`created_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `customer`(`customer`),
index `appointment`(`appointment`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `posting_date`(`posting_date`),
index `company`(`company`),
index `payment_type`(`payment_type`),
index `mode_of_payment`(`mode_of_payment`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `price_list`(`price_list`),
index `currency`(`currency`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `is_cancelled`(`is_cancelled`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `sales_invoice`(`sales_invoice`),
index `healthcare_practitioner`(`healthcare_practitioner`),
index `healthcare_service_unit`(`healthcare_service_unit`),
index `department`(`department`),
index `cost_center`(`cost_center`),
index `created_by`(`created_by`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:54,217 WARNING database DDL Query made to DB:
create table `tabLab Machine Message` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date_and_time` datetime(6),
`machine_make` varchar(140),
`machine_model` varchar(140),
`lab_test_name` varchar(140),
`lab_test` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:54,362 WARNING database DDL Query made to DB:
create table `tabLRPMT Returns` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`company` varchar(140),
`inpatient_record` varchar(140),
`status` varchar(140),
`admitted_datetime` datetime(6),
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:54,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `lab_test` varchar(140), ADD COLUMN `department_hsu` varchar(140), ADD COLUMN `sequence` int(11) not null default 0, ADD COLUMN `as_needed` int(1) not null default 0, ADD COLUMN `patient_instruction` text, ADD COLUMN `replaces` varchar(140), ADD COLUMN `note` text
2025-06-02 11:06:54,686 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`company` varchar(140),
`healthcare_service_order_category` varchar(140),
`patient_care_type` varchar(140),
`status` varchar(140) default 'Draft',
`order_date` date,
`ordered_by` varchar(140),
`order_group` varchar(140),
`replaces` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`triage` varchar(140),
`gender` varchar(140),
`birth_date` date,
`age` int(11) not null default 0,
`blood_group` varchar(140),
`marital_status` varchar(140),
`occupation` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`insurance_subscription` varchar(140),
`insurance_claim` varchar(140),
`insurance_company` varchar(140),
`claim_status` varchar(140),
`order_doctype` varchar(140),
`order` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`order_description` text,
`intent` varchar(140),
`priority` varchar(140),
`reason` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`body_part` varchar(140),
`staff_role` varchar(140),
`healthcare_service_unit_type` varchar(140),
`note` text,
`patient_instruction` text,
`source` varchar(140),
`referring_practitioner` varchar(140),
`medical_code_standard` varchar(140),
`medical_code` varchar(140),
`order_reference_doctype` varchar(140),
`order_reference_name` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:54,824 WARNING database DDL Query made to DB:
create table `tabDrug Interaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:55,787 WARNING database DDL Query made to DB:
create table `tabNHIF Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`product_id` varchar(140) unique,
`product_name` varchar(140),
`healthcare_insurance_coverage_plan` varchar(140),
`nhif_product_code` varchar(140),
`schemeid` varchar(140),
`productdescription` varchar(140),
`highestorderwithoutreferral` int(11) not null default 0,
`company` varchar(140),
`maximumadmissiondays` varchar(140),
`requiresnationalid` int(1) not null default 0,
`usespolicy` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `product_name`(`product_name`),
index `healthcare_insurance_coverage_plan`(`healthcare_insurance_coverage_plan`),
index `nhif_product_code`(`nhif_product_code`),
index `schemeid`(`schemeid`),
index `productdescription`(`productdescription`),
index `highestorderwithoutreferral`(`highestorderwithoutreferral`),
index `company`(`company`),
index `maximumadmissiondays`(`maximumadmissiondays`),
index `requiresnationalid`(`requiresnationalid`),
index `usespolicy`(`usespolicy`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,044 WARNING database DDL Query made to DB:
create table `tabHealthcare Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`package_image` text,
`description` longtext,
`price_list` varchar(140),
`total_actual_item_price` decimal(21,9) not null default 0,
`total_price_of_services` decimal(21,9) not null default 0,
`price_of_package` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,210 WARNING database DDL Query made to DB:
create table `tabPrevious Radiology Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`radiology_examination_template` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`radiology_procedure_name` varchar(140),
`invoiced` int(1) not null default 0,
`radiology_test_comment` text,
`radiology_examination_created` int(1) not null default 0,
`appointment_booked` int(1) not null default 0,
`radiology_examination` varchar(140),
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `radiology_examination_created`(`radiology_examination_created`),
index `appointment_booked`(`appointment_booked`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,331 WARNING database DDL Query made to DB:
create table `tabCompany NHIF Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`username` varchar(140),
`password` text,
`facility_code` varchar(140),
`enable` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhifservice_url` varchar(140),
`nhifservice_token` text,
`nhifservice_expiry` datetime(6),
`claimsserver_url` varchar(140),
`claimsserver_token` text,
`claimsserver_expiry` datetime(6),
`nhifform_url` varchar(140),
`nhifform_expiry` datetime(6),
`nhifform_token` text,
`update_patient_history` int(1) not null default 1,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,437 WARNING database DDL Query made to DB:
create table `tabNHIF Facility Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_code` varchar(140) unique,
`facility_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,569 WARNING database DDL Query made to DB:
create table `tabNHIF Co-Payment Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`itemcode` varchar(140),
`scheduleitemid` int(11) not null default 0,
`schemeid` varchar(140),
`yearno` int(11) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `itemcode`(`itemcode`),
index `scheduleitemid`(`scheduleitemid`),
index `schemeid`(`schemeid`),
index `yearno`(`yearno`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,673 WARNING database DDL Query made to DB:
create table `tabVC Cash Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,810 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`billno` varchar(140),
`foliono` varchar(140),
`datesubmitted` datetime(6),
`cardno` varchar(140),
`authorizationno` varchar(140),
`amountclaimed` decimal(21,9) not null default 0,
`submissionid` varchar(140),
`submissionno` varchar(140),
`remarks` text,
index `billno`(`billno`),
index `foliono`(`foliono`),
index `cardno`(`cardno`),
index `authorizationno`(`authorizationno`),
index `amountclaimed`(`amountclaimed`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:56,930 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card_type_id` varchar(140),
`card_type_name` varchar(140),
index `card_type_id`(`card_type_id`),
index `card_type_name`(`card_type_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:57,033 WARNING database DDL Query made to DB:
create table `tabVC Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healathcare_practitioner` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:57,398 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient_appointment` varchar(140),
`company` varchar(140),
`posting_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`first_name` varchar(140),
`last_name` varchar(140),
`telephone_no` varchar(140),
`date_of_birth` date,
`gender` varchar(140),
`cardno` varchar(140),
`authorization_no` varchar(140),
`coverage_plan_name` varchar(140),
`inpatient_record` varchar(140),
`allow_changes` int(1) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`patient_signature` longtext,
`is_ready_for_auto_submission` int(1) not null default 0,
`reviewed_by` varchar(140),
`confirmation_code_sent` int(1) not null default 0,
`confirmation_code` varchar(140),
`receipt_no` varchar(140),
`facility_code` varchar(140),
`claim_year` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`serial_no` varchar(140),
`practitioner_name` varchar(140),
`practitioner_no` text,
`patient_file_no` varchar(140),
`patient_type_code` varchar(140),
`attendance_date` date,
`attendance_time` time(6),
`item_crt_by` varchar(140),
`date_admitted` date,
`admitted_time` time(6),
`date_discharge` date,
`discharge_time` time(6),
`delayreason` text,
`patient_file` longtext,
`claim_file` longtext,
`clinical_notes` longtext,
`submission_id` varchar(140),
`hashcode` varchar(140),
`naming_series` varchar(140),
`submission_no` varchar(140),
`submission_channel` varchar(140),
`amended_from` varchar(140),
`date_submitted` datetime(6),
`submission_remarks` text,
`hms_tz_claim_appointment_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_appointment`(`patient_appointment`),
index `company`(`company`),
index `posting_date`(`posting_date`),
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `first_name`(`first_name`),
index `last_name`(`last_name`),
index `telephone_no`(`telephone_no`),
index `date_of_birth`(`date_of_birth`),
index `gender`(`gender`),
index `cardno`(`cardno`),
index `authorization_no`(`authorization_no`),
index `coverage_plan_name`(`coverage_plan_name`),
index `inpatient_record`(`inpatient_record`),
index `allow_changes`(`allow_changes`),
index `total_amount`(`total_amount`),
index `is_ready_for_auto_submission`(`is_ready_for_auto_submission`),
index `reviewed_by`(`reviewed_by`),
index `confirmation_code_sent`(`confirmation_code_sent`),
index `confirmation_code`(`confirmation_code`),
index `receipt_no`(`receipt_no`),
index `facility_code`(`facility_code`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `folio_no`(`folio_no`),
index `serial_no`(`serial_no`),
index `practitioner_name`(`practitioner_name`),
index `patient_type_code`(`patient_type_code`),
index `attendance_date`(`attendance_date`),
index `attendance_time`(`attendance_time`),
index `item_crt_by`(`item_crt_by`),
index `date_admitted`(`date_admitted`),
index `admitted_time`(`admitted_time`),
index `date_discharge`(`date_discharge`),
index `discharge_time`(`discharge_time`),
index `submission_id`(`submission_id`),
index `hashcode`(`hashcode`),
index `naming_series`(`naming_series`),
index `submission_no`(`submission_no`),
index `submission_channel`(`submission_channel`),
index `date_submitted`(`date_submitted`),
index `hms_tz_claim_appointment_list`(`hms_tz_claim_appointment_list`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:57,751 WARNING database DDL Query made to DB:
create table `tabOriginal Delivery Note Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`hms_tz_is_out_of_stock` int(1) not null default 0,
`barcode` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`customer_item_code` varchar(140),
`description` longtext,
`brand` varchar(140),
`item_group` varchar(140),
`image` text,
`qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`price_list_rate` decimal(21,9) not null default 0,
`base_price_list_rate` decimal(21,9) not null default 0,
`margin_type` varchar(140),
`margin_rate_or_amount` decimal(21,9) not null default 0,
`rate_with_margin` decimal(21,9) not null default 0,
`discount_percentage` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`base_rate_with_margin` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`pricing_rules` text,
`stock_uom_rate` decimal(21,9) not null default 0,
`is_free_item` int(1) not null default 0,
`grant_commission` int(1) not null default 0,
`net_rate` decimal(21,9) not null default 0,
`net_amount` decimal(21,9) not null default 0,
`item_tax_template` varchar(140),
`base_net_rate` decimal(21,9) not null default 0,
`base_net_amount` decimal(21,9) not null default 0,
`billed_amt` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`weight_per_unit` decimal(21,9) not null default 0,
`total_weight` decimal(21,9) not null default 0,
`weight_uom` varchar(140),
`warehouse` varchar(140),
`target_warehouse` varchar(140),
`quality_inspection` varchar(140),
`against_sales_order` varchar(140),
`so_detail` varchar(140),
`against_sales_invoice` varchar(140),
`si_detail` varchar(140),
`batch_no` varchar(140),
`serial_no` text,
`actual_batch_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
`installed_qty` decimal(21,9) not null default 0,
`item_tax_rate` text,
`expense_account` varchar(140),
`allow_zero_valuation_rate` int(1) not null default 0,
`healthcare_service_unit` varchar(140),
`healthcare_practitioner` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`original_item` varchar(140),
`original_stock_uom_qty` decimal(21,9) not null default 0,
`is_restricted` int(1) not null default 0,
`approval_number` varchar(140),
`approval_type` varchar(140),
`last_date_prescribed` date,
`last_qty_prescribed` decimal(21,9) not null default 0,
`recommended_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `so_detail`(`so_detail`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:57,920 WARNING database DDL Query made to DB:
create table `tabNHIF Custom Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`time_stamp` datetime(6),
`facilitycode` varchar(140),
`title` varchar(140),
`item` varchar(140),
`itemcode` varchar(140),
`excludedforscheme` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,080 WARNING database DDL Query made to DB:
create table `tabPrevious Lab Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`lab_test_name` varchar(140),
`invoiced` int(1) not null default 0,
`lab_test` varchar(140),
`lab_test_comment` text,
`lab_test_created` int(1) not null default 0,
`department_hsu` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `invoiced`(`invoiced`),
index `lab_test_created`(`lab_test_created`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,256 WARNING database DDL Query made to DB:
create table `tabNHIF Monthly Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` datetime(6),
`folio_submitted` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`acknowledgement_no` varchar(140),
`date_submitted` datetime(6),
`submitted_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `status`(`status`),
index `posting_date`(`posting_date`),
index `folio_submitted`(`folio_submitted`),
index `total_amount_claimed`(`total_amount_claimed`),
index `acknowledgement_no`(`acknowledgement_no`),
index `date_submitted`(`date_submitted`),
index `submitted_by`(`submitted_by`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,376 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_type` varchar(140),
`healthcare_service` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
`quantity` decimal(21,9) not null default 0,
`dosage` varchar(140),
`period` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,496 WARNING database DDL Query made to DB:
create table `tabHealthcare Points of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`point_of_care_name` varchar(140) unique,
`point_of_care_id` varchar(140),
`point_of_care_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `point_of_care_id`(`point_of_care_id`),
index `point_of_care_code`(`point_of_care_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,620 WARNING database DDL Query made to DB:
create table `tabNHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facilitycode` varchar(140),
`log_name` varchar(140),
`time_stamp` datetime(6),
`company` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,772 WARNING database DDL Query made to DB:
create table `tabNHIF Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type_name` varchar(140) unique,
`service_type_id` varchar(140),
`require_nhif_number` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type_id`(`service_type_id`),
index `require_nhif_number`(`require_nhif_number`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:58,895 WARNING database DDL Query made to DB:
create table `tabNHIF Scheme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scheme_id` varchar(140) unique,
`scheme_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `scheme_name`(`scheme_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,000 WARNING database DDL Query made to DB:
create table `tabVC Insurance Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`coverage_plan` varchar(140),
`document_type` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,097 WARNING database DDL Query made to DB:
create table `tabVC Excluded Service Rate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`healthcare_service` varchar(140),
`vc_rate` decimal(21,9) not null default 0,
`company_rate` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,289 WARNING database DDL Query made to DB:
create table `tabNHIF Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`itemcode` varchar(140),
`itemtypeid` int(11) not null default 0,
`itemname` varchar(140),
`subgroup` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`isactive` int(1) not null default 0,
`isrestricted` int(1) not null default 0,
`calculatedperday` varchar(140),
`servicetypeid` int(11) not null default 0,
`serviceinterval` varchar(140),
`typeofinterval` varchar(140),
`waitingperiod` int(11) not null default 0,
`typeofperiod` varchar(140),
`eligibility` varchar(140),
`commonprice` decimal(21,9) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`availableinlevels` varchar(140),
`practitionerqualifications` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `itemname`(`itemname`),
index `subgroup`(`subgroup`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `isactive`(`isactive`),
index `isrestricted`(`isrestricted`),
index `calculatedperday`(`calculatedperday`),
index `servicetypeid`(`servicetypeid`),
index `serviceinterval`(`serviceinterval`),
index `typeofinterval`(`typeofinterval`),
index `waitingperiod`(`waitingperiod`),
index `typeofperiod`(`typeofperiod`),
index `eligibility`(`eligibility`),
index `commonprice`(`commonprice`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,415 WARNING database DDL Query made to DB:
create table `tabNHIF Physician Qualification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`id` varchar(140) unique,
`qualification` varchar(140),
`physicianqualificationid` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,574 WARNING database DDL Query made to DB:
create table `tabVisiting Comission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`valid_from` date,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,798 WARNING database DDL Query made to DB:
create table `tabNHIF Update` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`timestamp` datetime(6),
`current_log` varchar(140),
`previous_log` varchar(140),
`company` varchar(140),
`user_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `timestamp`(`timestamp`),
index `current_log`(`current_log`),
index `company`(`company`),
index `user_id`(`user_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:06:59,922 WARNING database DDL Query made to DB:
create table `tabPatient Discount Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_category` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`actual_price` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`amount_after_discount` decimal(21,9) not null default 0,
`sales_invoice` varchar(140),
`si_detail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,038 WARNING database DDL Query made to DB:
create table `tabOrgan System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,209 WARNING database DDL Query made to DB:
create table `tabPatient Discount Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`insurance_subscription` varchar(140),
`insurance_coverage_plan` varchar(140),
`insurance_company` varchar(140),
`payment_type` varchar(140),
`apply_discount_on` varchar(140),
`item_category` varchar(140),
`appointment` varchar(140),
`sales_invoice` varchar(140),
`discount_criteria` varchar(140),
`discount_percent` decimal(21,9) not null default 0,
`discount_amount` decimal(21,9) not null default 0,
`total_actual_amount` decimal(21,9) not null default 0,
`total_discounted_amount` decimal(21,9) not null default 0,
`total_amount_after_discount` decimal(21,9) not null default 0,
`requested_by` varchar(140),
`approved_by` varchar(140),
`naming_series` varchar(140),
`amended_from` varchar(140),
`discount_reason` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,350 WARNING database DDL Query made to DB:
create table `tabHMS TZ Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`facility_code` varchar(140),
`nhif_user` varchar(140),
`nhif_client_secret` text,
`nhif_grant_type` varchar(140),
`nhif_scope` varchar(140),
`enable_nhif_api` int(1) not null default 0,
`check_patient_info_on_his` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhif_claim_url` varchar(140),
`nhifservice_url` varchar(140),
`nhif_token_url` varchar(140),
`nhif_token_expiry` datetime(6),
`nhif_token` text,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,462 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`price_list` varchar(140),
`actual_item_price` decimal(21,9) not null default 0,
`service_price` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,578 WARNING database DDL Query made to DB:
create table `tabPrevious Diet Recommendation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diet_plan` varchar(140),
`medical_code` varchar(140),
`occurance` int(11) not null default 0,
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,770 WARNING database DDL Query made to DB:
create table `tabNHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_stamp` datetime(6),
`log_name` varchar(140),
`company` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`itemtypeid` int(11) not null default 0,
`pricecode` varchar(140),
`itemname` text,
`schemeid` varchar(140),
`packageid` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`unitprice` decimal(21,9) not null default 0,
`maximumquantity` varchar(140),
`maximumquantityoutpatient` varchar(140),
`maximumquantityinpatient` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `pricecode`(`pricecode`),
index `schemeid`(`schemeid`),
index `packageid`(`packageid`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `unitprice`(`unitprice`),
index `maximumquantity`(`maximumquantity`),
index `maximumquantityoutpatient`(`maximumquantityoutpatient`),
index `maximumquantityinpatient`(`maximumquantityinpatient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:00,901 WARNING database DDL Query made to DB:
create table `tabPractitioner Availability Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner_availability` varchar(140),
`availability_type` varchar(140),
`availability` varchar(140),
`practitioner` varchar(140),
`healthcare_practitioner_name` varchar(140),
`from_date` datetime(6),
`from_time` time(6),
`to_date` datetime(6),
`to_time` time(6),
`repeat_this_event` int(1) not null default 0,
`present` int(1) not null default 0,
`appointment_type` varchar(140),
`duration` int(11) not null default 0,
`service_unit` varchar(140),
`total_service_unit_capacity` int(11) not null default 0,
`color` varchar(140),
`out_patient_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,028 WARNING database DDL Query made to DB:
create table `tabNHIF Folio Counter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` int(11) not null default 0,
`folio_no` int(11) not null default 0,
`posting_date` datetime(6),
`claim_month` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,171 WARNING database DDL Query made to DB:
create table `tabNHIF Tracking Claim Change` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`quantity` int(11) not null default 0,
`claim_month` int(11) not null default 0,
`claim_year` int(11) not null default 0,
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`status` varchar(140),
`previous_amount` decimal(21,9) not null default 0,
`current_amount` decimal(21,9) not null default 0,
`amount_changed` decimal(21,9) not null default 0,
`nhif_patient_claim` varchar(140),
`lrpmt_return` varchar(140),
`patient_appointment` varchar(140),
`medication_change_request` varchar(140),
`patient_encounter` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`user_email` varchar(140),
`edited_by` varchar(140),
`comment` text,
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,278 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`mct_code` varchar(140),
index `practitioner`(`practitioner`),
index `mct_code`(`mct_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,388 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`date_created` datetime(6),
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,511 WARNING database DDL Query made to DB:
create table `tabOriginal NHIF Patient Claim Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`item_name` varchar(140),
`item_code` varchar(140),
`item_quantity` int(11) not null default 0,
`unit_price` decimal(21,9) not null default 0,
`amount_claimed` decimal(21,9) not null default 0,
`item_crt_by` varchar(140) default 'None',
`status` varchar(140),
`patient_encounter` text,
`ref_docname` text,
`approval_ref_no` varchar(140),
`date_created` datetime(6),
`claim_status` varchar(140),
`claim_closed` int(1) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`claim_status_modification_notes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,690 WARNING database DDL Query made to DB:
create table `tabNHIF Response Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140),
`timestamp` datetime(6),
`company` varchar(140),
`naming_series` varchar(140),
`request_type` varchar(140),
`request_url` varchar(1000),
`status_code` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`card_no` varchar(140),
`authorization_no` varchar(140),
`request_header` text,
`request_body` longtext,
`response_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user_id`(`user_id`),
index `timestamp`(`timestamp`),
index `request_type`(`request_type`),
index `request_url`(`request_url`),
index `status_code`(`status_code`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `card_no`(`card_no`),
index `authorization_no`(`authorization_no`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,856 WARNING database DDL Query made to DB:
create table `tabNHIF Claim Reconciliation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` date,
`number_of_submitted_claims` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`erp_number_of_submitted_claims` int(11) not null default 0,
`erp_total_amount_claimed` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `status`(`status`),
index `posting_date`(`posting_date`),
index `number_of_submitted_claims`(`number_of_submitted_claims`),
index `total_amount_claimed`(`total_amount_claimed`),
index `erp_number_of_submitted_claims`(`erp_number_of_submitted_claims`),
index `erp_total_amount_claimed`(`erp_total_amount_claimed`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:01,985 WARNING database DDL Query made to DB:
create table `tabPrevious Therapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`no_of_sessions` int(11) not null default 0,
`sessions_completed` int(11) not null default 0,
`department_hsu` varchar(140),
`comment` text,
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 1.0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,163 WARNING database DDL Query made to DB:
create table `tabPrevious Procedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`clinical_procedure` varchar(140),
`department_hsu` varchar(140),
`override_insurance_subscription` int(1) not null default 0,
`hso_payment_method` varchar(140),
`intent` varchar(140),
`sequence` int(11) not null default 0,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`body_part` varchar(140),
`note` text,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,288 WARNING database DDL Query made to DB:
create table `tabChronic Medications` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,420 WARNING database DDL Query made to DB:
create table `tabPrevious Drug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug_code` varchar(140),
`medical_code` varchar(140),
`override_subscription` int(1) not null default 0,
`prescribe` int(1) not null default 0,
`is_not_available_inhouse` int(1) not null default 0,
`drug_name` varchar(140),
`dosage` varchar(140),
`period` varchar(140),
`dosage_form` varchar(140),
`healthcare_service_unit` varchar(140),
`is_restricted` int(1) not null default 0,
`comment` text,
`usage_interval` int(1) not null default 0,
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`update_schedule` int(1) not null default 1,
`intent` varchar(140),
`quantity` int(11) not null default 0,
`sequence` int(11) not null default 0,
`expected_date` date,
`as_needed` int(1) not null default 0,
`patient_instruction` text,
`replaces` varchar(140),
`priority` varchar(140),
`occurrence` datetime(6),
`occurence_period` decimal(21,9),
`note` text,
`drug_prescription_created` int(1) not null default 0,
`delivered_quantity` decimal(21,9) not null default 0,
`sales_invoice_number` varchar(140),
`reference_journal_entry` varchar(140),
`cancelled` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,549 WARNING database DDL Query made to DB:
create table `tabMedication Change Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`appointment` varchar(140),
`hms_tz_comment` text,
`company` varchar(140),
`patient_encounter` varchar(140),
`delivery_note` varchar(140),
`sales_order` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`medical_department` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `delivery_note`(`delivery_note`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,681 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order Consultation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`consultation_item` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`consultation_fee` decimal(21,9) not null default 0,
`appointment` varchar(140),
`encounter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:02,890 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Price Package` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`service_type` varchar(140),
`service_name` varchar(140),
`itemname` varchar(140),
`itemcode` varchar(140),
`itemtypeid` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`schemeid` varchar(140),
`packageid` varchar(140),
`pricecode` varchar(140),
`unitprice` decimal(21,9) not null default 0,
`isrestricted` int(1) not null default 0,
`hascopayment` int(1) not null default 0,
`maximumquantity` varchar(140),
`maximumquantityoutpatient` varchar(140),
`maximumquantityinpatient` varchar(140),
`fields_changed` longtext,
`previous_item` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `schemeid`(`schemeid`),
index `packageid`(`packageid`),
index `pricecode`(`pricecode`),
index `unitprice`(`unitprice`),
index `isrestricted`(`isrestricted`),
index `hascopayment`(`hascopayment`),
index `maximumquantity`(`maximumquantity`),
index `maximumquantityoutpatient`(`maximumquantityoutpatient`),
index `maximumquantityinpatient`(`maximumquantityinpatient`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,006 WARNING database DDL Query made to DB:
create table `tabStaging NHIF Excluded Services` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`facilitycode` varchar(140),
`itemcode` varchar(140),
`schemeid` varchar(140),
`schemename` varchar(140),
`excludedforproducts` text,
`record` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,125 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Disease` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis_type` varchar(140),
`status` varchar(140),
`medical_code` varchar(140),
`disease_code` varchar(140),
`description` varchar(140),
`patient_encounter` varchar(140),
`codification_table` varchar(140),
`item_crt_by` varchar(140),
`date_created` datetime(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,257 WARNING database DDL Query made to DB:
create table `tabNHIF Item Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_type_id` int(11) not null default 0 unique,
`type_name` varchar(140),
`alias` varchar(140),
`item_group` varchar(140),
`display_item` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `type_name`(`type_name`),
index `alias`(`alias`),
index `item_group`(`item_group`),
index `display_item`(`display_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Machine Message` ADD COLUMN `sample_collection` varchar(140), ADD COLUMN `machine_id` varchar(140)
2025-06-02 11:07:03,574 WARNING database DDL Query made to DB:
create table `tabHealthcare Package Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_package` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`patient` varchar(140),
`patient_name` varchar(140),
`non_consultation_appointment` varchar(140),
`non_consultation_encounter` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`authorization_number` varchar(140),
`total_price` decimal(21,9) not null default 0,
`paid` int(1) not null default 0,
`sales_invoice` varchar(140),
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,732 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`verifier_name` varchar(140) unique,
`verifier_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `verifier_id`(`verifier_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:03,861 WARNING database DDL Query made to DB:
create table `tabVC LRPMT Submitter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_field` varchar(140),
`wtax` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:05,468 WARNING database DDL Query made to DB:
create table `tabPayment Gateway` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway` varchar(140) unique,
`gateway_settings` varchar(140),
`gateway_controller` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:05,599 WARNING database DDL Query made to DB:
create table `tabBraintree Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`merchant_id` varchar(140),
`public_key` varchar(140),
`private_key` text,
`use_sandbox` int(1) not null default 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:05,841 WARNING database DDL Query made to DB:
create table `tabGoCardless Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140) unique,
`access_token` varchar(140),
`webhooks_secret` varchar(140),
`use_sandbox` int(1) not null default 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:06,007 WARNING database DDL Query made to DB:
create table `tabMpesa Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_gateway_name` varchar(140) unique,
`consumer_key` varchar(140),
`consumer_secret` text,
`initiator_name` varchar(140),
`till_number` varchar(140),
`transaction_limit` decimal(21,9) not null default 150000.0,
`sandbox` int(1) not null default 0,
`business_shortcode` varchar(140),
`online_passkey` text,
`security_credential` text,
`account_balance` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:06,242 WARNING database DDL Query made to DB:
create table `tabGoCardless Mandate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`mandate` varchar(140) unique,
`gocardless_customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:06,368 WARNING database DDL Query made to DB:
create table `tabStripe Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`publishable_key` varchar(140),
`secret_key` text,
`header_img` text,
`redirect_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:07:07,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` ADD COLUMN `currency` varchar(140), ADD COLUMN `amount_field` varchar(140), ADD COLUMN `payment_button_help` text, ADD COLUMN `amount_based_on_field` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `payment_gateway` varchar(140), ADD COLUMN `payment_button_label` varchar(140) default 'Buy Now', ADD COLUMN `accept_payment` int(1) not null default 0
2025-06-02 11:07:07,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` MODIFY `condition_json` json
2025-06-02 11:07:07,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Mandate` ADD COLUMN `customer` varchar(140)
2025-06-02 11:16:41,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabVital Signs` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `height` decimal(21,9) not null default 0
2025-06-02 11:16:41,468 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-06-02 11:16:41,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Schedule Time Slot` MODIFY `duration` decimal(21,9) not null default 0
2025-06-02 11:16:41,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabNursing Task` MODIFY `task_duration` decimal(21,9), MODIFY `duration` decimal(21,9)
2025-06-02 11:16:42,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-06-02 11:16:42,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order Entry` MODIFY `dosage` decimal(21,9) not null default 0
2025-06-02 11:16:42,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:16:42,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `discharge_ordered_datetime` datetime(6), ADD COLUMN `reason_for_cancellation` text
2025-06-02 11:16:43,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template` MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-02 11:16:43,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:16:43,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order` MODIFY `total_orders` decimal(21,9) not null default 0, MODIFY `completed_orders` decimal(21,9) not null default 0
2025-06-02 11:16:43,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:16:43,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:16:44,452 WARNING database DDL Query made to DB:
create table `tabMedical Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medical_code_standard` varchar(140),
`code` varchar(140) unique,
`uri` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:16:44,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 11:16:45,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:16:45,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabDosage Strength` MODIFY `strength` decimal(21,9) not null default 0
2025-06-02 11:16:45,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type Service Item` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 11:16:46,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-06-02 11:16:46,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_code`, DROP INDEX `lab_test_name_index`
2025-06-02 11:16:47,274 WARNING database DDL Query made to DB:
create table `tabMedical Code Standard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medical_code` varchar(140) unique,
`uri` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:16:47,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:16:47,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Template` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:16:48,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` DROP INDEX `department`
2025-06-02 11:16:48,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabCodification Table` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `medical_code_standard` varchar(140), ADD COLUMN `description` text
2025-06-02 11:16:48,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Activity` MODIFY `activity_duration` decimal(21,9)
2025-06-02 11:16:49,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-02 11:16:49,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Group Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:16:50,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabNursing Checklist Template Task` MODIFY `task_duration` decimal(21,9), MODIFY `time_offset` decimal(21,9)
2025-06-02 11:16:50,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Entry Detail` MODIFY `dosage` decimal(21,9) not null default 0, MODIFY `available_qty` decimal(21,9) not null default 0
2025-06-02 11:16:50,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Result` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:17:09,262 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-02 11:17:10,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-02 11:17:10,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-02 11:17:11,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:17:11,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-02 11:17:12,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 11:17:12,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-06-02 11:17:12,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:17:13,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:17:13,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-02 11:17:13,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-02 11:17:14,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:17:14,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:17:14,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0
2025-06-02 11:17:14,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 11:17:15,144 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:17:15,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-06-02 11:17:15,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:17:16,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0
2025-06-02 11:17:16,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `occurence_period` decimal(21,9)
2025-06-02 11:17:16,672 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:17:17,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 11:17:17,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 11:17:17,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 11:17:17,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 11:17:17,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD UNIQUE INDEX IF NOT EXISTS department (`department`)
2025-06-02 11:17:18,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:17:19,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:17:24,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-06-02 11:17:24,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-02 11:17:24,672 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-06-02 11:17:24,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-06-02 11:17:24,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-06-02 11:17:24,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-02 11:17:24,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-06-02 11:17:24,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:17:26,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `insurance_coverage_plan` varchar(140), ADD COLUMN `cash_limit` decimal(21,9) not null default 0, ADD COLUMN `patient_appointment` varchar(140), ADD COLUMN `practitioner_name` varchar(140), ADD COLUMN `admission_type` varchar(140), ADD COLUMN `primary_practitioner_name` varchar(140), ADD COLUMN `secondary_practitioner_name` varchar(140), ADD COLUMN `inpatient_record_type` varchar(140), ADD COLUMN `duplicate` int(1) not null default 0, ADD COLUMN `reference_inpatient_record` varchar(140), ADD COLUMN `duplicated_from` varchar(140), ADD COLUMN `history` text, ADD COLUMN `medication` text, ADD COLUMN `when_to_obtain_urgent_care` text, ADD COLUMN `on_examination` text, ADD COLUMN `surgical_procedure` text, ADD COLUMN `review` text
2025-06-02 11:17:27,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` ADD COLUMN `medical_code` varchar(140), ADD COLUMN `override_subscription` int(1) not null default 0, ADD COLUMN `prescribe` int(1) not null default 0, ADD COLUMN `is_not_available_inhouse` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `hms_tz_is_discount_applied` int(1) not null default 0, ADD COLUMN `delivered_quantity` decimal(21,9) not null default 0, ADD COLUMN `rejection_details` text, ADD COLUMN `sales_invoice_number` varchar(140), ADD COLUMN `reference_journal_entry` varchar(140), ADD COLUMN `cancelled` int(1) not null default 0, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_cancelled` int(1) not null default 0, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `hms_tz_is_limit_exceeded` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140)
