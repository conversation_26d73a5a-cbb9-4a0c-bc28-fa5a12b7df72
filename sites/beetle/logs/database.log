2025-06-12 14:30:59,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `rating` decimal(3,2)
2025-06-12 14:30:59,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:00,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-06-12 14:31:00,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:00,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 14:31:00,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:01,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 14:31:01,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:01,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 14:31:01,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:02,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:02,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 14:31:02,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:02,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `total_actual_amount` decimal(21,9) not null default 0, MODIFY `exemption_amount` decimal(21,9) not null default 0
2025-06-12 14:31:02,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:03,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:03,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-12 14:31:03,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:03,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 14:31:03,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:03,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `custom_long_term_service` decimal(21,9) not null default 0, MODIFY `custom_food_allowance` decimal(21,9) not null default 0, MODIFY `custom_bank_allowance` decimal(21,9) not null default 0, MODIFY `custom_health_and_safety_allowance` decimal(21,9) not null default 0, MODIFY `base` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `allowance` decimal(21,9) not null default 0
2025-06-12 14:31:04,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:04,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-06-12 14:31:04,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:04,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `gross_pay2` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `custom_actual_gross_pay` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-06-12 14:31:04,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:05,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-06-12 14:31:05,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:05,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-06-12 14:31:05,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:05,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:06,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:06,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:06,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 14:31:06,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:06,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 14:31:07,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:07,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_declared_amount` decimal(21,9) not null default 0
2025-06-12 14:31:07,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:07,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-06-12 14:31:07,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:07,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-12 14:31:07,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:08,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `max_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0, MODIFY `min_taxable_income` decimal(21,9) not null default 0
2025-06-12 14:31:08,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:08,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0
2025-06-12 14:31:08,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:09,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:09,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:09,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0
2025-06-12 14:31:09,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:09,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0
2025-06-12 14:31:09,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:10,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-12 14:31:10,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:10,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 14:31:10,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:10,737 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-06-12 14:31:10,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0
2025-06-12 14:31:11,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `creation`(`creation`)
2025-06-12 14:31:12,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 14:31:12,441 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:31:12,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 14:31:12,947 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:31:14,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 14:31:14,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 14:31:15,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 14:31:16,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 14:31:16,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaff Loan Application` MODIFY `total_payable_amount` decimal(21,9) not null default 0, MODIFY `rate_of_interest` decimal(21,9) not null default 0, MODIFY `maximum_loan_amount` decimal(21,9) not null default 0, MODIFY `repayment_amount` decimal(21,9) not null default 0, MODIFY `total_payable_interest` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0
2025-06-12 14:31:16,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaff Loan Application` ADD INDEX `amended_from_index`(`amended_from`)
2025-06-12 14:31:17,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaff Loan Type` MODIFY `maximum_loan_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `penalty_interest_rate` decimal(21,9) not null default 0, MODIFY `is_term_loan` int(1) not null default 1
2025-06-12 14:31:17,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaff Loan` MODIFY `monthly_repayment_amount` decimal(21,9) not null default 0, MODIFY `applicant_type` varchar(140) default 'Employee', MODIFY `maximum_loan_amount` decimal(21,9) not null default 0, MODIFY `written_off_amount` decimal(21,9) not null default 0, MODIFY `debit_adjustment_amount` decimal(21,9) not null default 0, MODIFY `credit_adjustment_amount` decimal(21,9) not null default 0, MODIFY `rate_of_interest` decimal(21,9) not null default 0, MODIFY `total_amount_paid` decimal(21,9) not null default 0, MODIFY `refund_amount` decimal(21,9) not null default 0, MODIFY `loan_amount` decimal(21,9) not null default 0, MODIFY `disbursed_amount` decimal(21,9) not null default 0
2025-06-12 14:31:19,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 14:31:20,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-06-12 14:31:20,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-12 14:31:27,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-12 14:31:27,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 14:31:28,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 14:31:28,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-12 14:31:28,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 14:31:28,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-12 14:31:28,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0
2025-06-12 14:31:30,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:28:43,487 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:28:44,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` int(1) not null default 0, ADD COLUMN `is_setup_complete` int(1) not null default 0
2025-07-13 11:28:44,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-07-13 11:28:45,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-07-13 11:28:45,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:28:45,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD COLUMN `company` varchar(140)
2025-07-13 11:28:45,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD INDEX `creation`(`creation`)
2025-07-13 11:28:46,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-07-13 11:28:46,745 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-13 11:28:47,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0
2025-07-13 11:28:47,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:28:48,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:28:48,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-13 11:28:48,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0
2025-07-13 11:28:48,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:28:49,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-07-13 11:28:49,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-13 11:28:49,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-07-13 11:28:49,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-13 11:28:49,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-13 11:28:50,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-13 11:28:50,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-07-13 11:28:50,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-13 11:28:50,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-07-13 11:28:51,138 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-13 11:28:51,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-07-13 11:28:51,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `price_list` varchar(140)
2025-07-13 11:28:51,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-07-13 11:28:51,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-13 11:28:52,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-07-13 11:28:52,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-13 11:28:52,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:28:52,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0
2025-07-13 11:28:53,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:28:53,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:28:53,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:28:54,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:28:54,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:28:54,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:28:55,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:28:56,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:28:56,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-13 11:28:56,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0
2025-07-13 11:28:59,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0
2025-07-13 11:28:59,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0
2025-07-13 11:34:13,806 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:14,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:34:16,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:34:17,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:17,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:18,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:18,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:19,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:20,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:20,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:20,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:22,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:34:22,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0
2025-07-13 11:34:23,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0
2025-07-13 11:34:25,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
