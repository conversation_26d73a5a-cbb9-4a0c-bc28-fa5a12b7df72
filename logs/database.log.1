2025-07-13 11:32:32,107 WARNING database DDL Query made to DB:
ALTE<PERSON> TABLE `tabOpportunity` MODIFY `annual_revenue` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0
2025-07-13 11:32:32,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-13 11:32:32,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIF<PERSON> `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-13 11:32:33,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:32:34,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0
2025-07-13 11:32:34,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-13 11:32:34,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0
2025-07-13 11:32:34,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-13 11:32:34,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-13 11:32:35,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-13 11:32:35,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-07-13 11:32:35,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-13 11:32:36,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-07-13 11:32:36,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-13 11:32:36,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0
2025-07-13 11:32:37,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `price_list` varchar(140)
2025-07-13 11:32:37,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-13 11:32:37,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-13 11:32:38,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-07-13 11:32:38,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-13 11:32:39,834 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection - old` MODIFY `customer_signature` longtext default 'Customer Signature'
2025-07-13 11:32:40,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:32:40,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:32:41,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:32:42,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-07-13 11:32:42,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:32:47,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `custom_employment_status` varchar(140)
2025-07-13 11:32:47,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-07-13 11:32:47,560 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:32:49,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:32:50,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD COLUMN `company` varchar(140)
2025-07-13 11:32:50,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD INDEX `creation`(`creation`)
2025-07-13 11:32:51,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-07-13 11:32:52,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:32:53,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `annual_revenue` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-07-13 11:32:53,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-13 11:32:53,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-13 11:32:54,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:32:54,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-13 11:32:54,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0
2025-07-13 11:32:55,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-13 11:32:55,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-13 11:32:55,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-13 11:32:55,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-13 11:32:55,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-13 11:32:56,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-07-13 11:32:56,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-13 11:32:56,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-07-13 11:32:57,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `price_list` varchar(140)
2025-07-13 11:32:57,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-13 11:32:57,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-13 11:32:58,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-07-13 11:32:58,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-13 11:32:59,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `journal_entry` varchar(140)
2025-07-13 11:32:59,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-07-13 11:32:59,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `journal_entry` varchar(140)
2025-07-13 11:32:59,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-13 11:33:00,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:33:00,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:01,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-07-13 11:33:01,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0
2025-07-13 11:33:01,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0
2025-07-13 11:33:02,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:02,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:02,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-07-13 11:33:02,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `item_balance` decimal(21,9) not null default 0
2025-07-13 11:33:03,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:33:03,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-07-13 11:33:03,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:04,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-07-13 11:33:04,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-07-13 11:33:04,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:04,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-07-13 11:33:05,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:05,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0
2025-07-13 11:33:05,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0
2025-07-13 11:33:06,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-07-13 11:33:06,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-07-13 11:33:06,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:33:06,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:09,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:09,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-07-13 11:33:09,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-07-13 11:33:10,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:10,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-07-13 11:33:10,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-07-13 11:33:11,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:33:11,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-07-13 11:33:12,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-07-13 11:33:14,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:33:15,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:15,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-07-13 11:33:17,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:17,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:18,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `planned_amount` decimal(21,9) not null default 0, MODIFY `actual_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0
2025-07-13 11:33:20,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:20,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:20,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `billable_hours` decimal(21,9) not null default 0, MODIFY `rate_per_hour` decimal(21,9) not null default 0
2025-07-13 11:33:20,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-07-13 11:33:25,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-13 11:33:25,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-07-13 11:34:13,806 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:14,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:34:16,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:34:17,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:17,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:18,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:18,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:19,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:20,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:20,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:20,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:22,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:34:22,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0
2025-07-13 11:34:23,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0
2025-07-13 11:34:25,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
2025-07-13 11:34:26,043 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:28,030 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:30,141 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:31,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:34:33,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:34:34,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:35,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:35,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:36,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:37,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-07-13 11:34:37,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `gross_weight_index`, DROP INDEX `container_no_index`
2025-07-13 11:34:37,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:34:38,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:34:38,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:39,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:39,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-07-13 11:34:39,666 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-07-13 11:34:40,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date
2025-07-13 11:34:43,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-07-13 11:34:43,883 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:45,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:34:46,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:34:48,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:34:49,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:49,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:50,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:51,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:34:51,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:34:52,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:52,487 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:34:53,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-07-13 11:34:56,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-07-13 11:34:56,602 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:34:57,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:34:59,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:35:01,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:35:01,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:35:03,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:35:03,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:35:07,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-07-13 11:35:07,704 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:35:09,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:35:10,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:35:12,710 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:35:13,135 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:35:14,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-07-13 11:35:14,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` DROP INDEX `healthcare_service_unit_name`
2025-07-13 11:35:14,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:15,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-07-13 11:35:15,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-07-13 11:35:15,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-07-13 11:35:15,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:16,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `ref_vital_signs` varchar(140), MODIFY `appointment_type` varchar(140)
2025-07-13 11:35:16,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-07-13 11:35:17,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:17,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:35:17,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_name (`lab_test_name`)
2025-07-13 11:35:17,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_code`, DROP INDEX `lab_test_name_index`
2025-07-13 11:35:32,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `price_list` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140), MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0
2025-07-13 11:35:32,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` DROP INDEX `department`
2025-07-13 11:35:33,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-07-13 11:35:33,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:35:33,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-07-13 11:35:34,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:35:34,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:35:35,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:35:35,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:35:36,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:35:36,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:35:37,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0
2025-07-13 11:35:37,676 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-07-13 11:35:37,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:38,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-07-13 11:35:38,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-07-13 11:35:38,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `sample_qty` decimal(21,9) not null default 0
2025-07-13 11:35:39,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:39,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `ref_vital_signs` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0
2025-07-13 11:35:39,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-07-13 11:35:40,495 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-07-13 11:35:40,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:35:41,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-07-13 11:35:41,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-07-13 11:35:41,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-07-13 11:35:41,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-07-13 11:35:55,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `patient` varchar(140), MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `price_list` varchar(140), MODIFY `encounter_mode_of_payment` varchar(140)
2025-07-13 11:35:55,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD UNIQUE INDEX IF NOT EXISTS department (`department`)
2025-07-13 11:35:55,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` MODIFY `patient_details_with_formatting` longtext
2025-07-13 11:35:56,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-07-13 11:35:56,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:35:59,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-07-13 11:36:00,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-07-13 11:36:00,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
2025-07-13 11:36:00,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
2025-07-13 11:36:00,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue Materials Detail` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:36:00,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-07-13 11:36:00,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
2025-07-13 11:36:01,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9)
2025-07-13 11:36:01,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-07-13 11:36:01,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9)
2025-07-13 11:36:01,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-13 11:36:01,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9)
2025-07-13 11:36:01,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9)
2025-07-13 11:36:02,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `first_response_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-07-13 11:36:02,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-07-13 11:36:02,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `total_hold_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9)
2025-07-13 11:36:02,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-07-13 11:36:02,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `resolution_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9)
2025-07-13 11:36:02,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:36:02,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `recommended_qty` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0
2025-07-13 11:36:03,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-07-13 11:36:03,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-13 11:36:03,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-13 11:36:03,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-07-13 11:36:04,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-07-13 11:36:04,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-07-13 11:36:04,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0
2025-07-13 11:36:04,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-13 11:36:04,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0
2025-07-13 11:36:06,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` MODIFY `price_list` varchar(140)
2025-07-13 11:36:06,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-13 11:36:06,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-07-13 11:36:07,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-07-13 11:36:07,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-07-13 11:36:07,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0
2025-07-13 11:36:07,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-07-13 11:36:08,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-07-13 11:36:08,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-07-13 11:36:08,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `vfd_cust_id` varchar(140), MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
