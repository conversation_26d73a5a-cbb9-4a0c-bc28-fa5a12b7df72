2025-06-12 13:03:26,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` DROP INDEX `modified`
2025-06-12 13:03:26,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` DROP INDEX `modified`
2025-06-12 13:03:26,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` DROP INDEX `modified`
2025-06-12 13:03:26,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` DROP INDEX `modified`
2025-06-12 13:03:26,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` DROP INDEX `modified`
2025-06-12 13:03:26,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` DROP INDEX `modified`
2025-06-12 13:03:26,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` DROP INDEX `modified`
2025-06-12 13:03:26,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` DROP INDEX `modified`
2025-06-12 13:03:26,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` DROP INDEX `modified`
2025-06-12 13:03:26,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` DROP INDEX `modified`
2025-06-12 13:03:26,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` DROP INDEX `modified`
2025-06-12 13:03:26,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` DROP INDEX `modified`
2025-06-12 13:03:26,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` DROP INDEX `modified`
2025-06-12 13:03:26,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` DROP INDEX `modified`
2025-06-12 13:03:26,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` DROP INDEX `modified`
2025-06-12 13:03:26,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` DROP INDEX `modified`
2025-06-12 13:03:26,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` DROP INDEX `modified`
2025-06-12 13:03:27,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` DROP INDEX `modified`
2025-06-12 13:03:27,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` DROP INDEX `modified`
2025-06-12 13:03:27,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` DROP INDEX `modified`
2025-06-12 13:03:27,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` DROP INDEX `modified`
2025-06-12 13:03:27,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` DROP INDEX `modified`
2025-06-12 13:03:27,211 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` DROP INDEX `modified`
2025-06-12 13:03:27,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` DROP INDEX `modified`
2025-06-12 13:03:27,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `modified`
2025-06-12 13:03:27,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabNavbar Item` DROP INDEX `modified`
2025-06-12 13:03:27,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` DROP INDEX `modified`
2025-06-12 13:03:27,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` DROP INDEX `modified`
2025-06-12 13:03:27,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` DROP INDEX `modified`
2025-06-12 13:03:27,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomize Form Field` DROP INDEX `modified`
2025-06-12 13:03:27,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Link` DROP INDEX `modified`
2025-06-12 13:03:27,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Price Discount` DROP INDEX `modified`
2025-06-12 13:03:27,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Product Discount` DROP INDEX `modified`
2025-06-12 13:03:27,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` DROP INDEX `modified`
2025-06-12 13:03:27,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `modified`
2025-06-12 13:03:27,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `modified`
2025-06-12 13:03:27,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Document Type` DROP INDEX `modified`
2025-06-12 13:03:27,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` DROP INDEX `modified`
2025-06-12 13:03:27,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `modified`
2025-06-12 13:03:27,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` DROP INDEX `modified`
2025-06-12 13:03:27,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` DROP INDEX `modified`
2025-06-12 13:03:27,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` DROP INDEX `modified`
2025-06-12 13:03:27,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request Costing` DROP INDEX `modified`
2025-06-12 13:03:27,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` DROP INDEX `modified`
2025-06-12 13:03:27,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabPSOA Cost Center` DROP INDEX `modified`
2025-06-12 13:03:27,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabOAuth Client Role` DROP INDEX `modified`
2025-06-12 13:03:28,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` DROP INDEX `modified`
2025-06-12 13:03:28,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` DROP INDEX `modified`
2025-06-12 13:03:28,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` DROP INDEX `modified`
2025-06-12 13:03:28,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` DROP INDEX `modified`
2025-06-12 13:03:28,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` DROP INDEX `modified`
2025-06-12 13:03:28,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` DROP INDEX `modified`
2025-06-12 13:03:28,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` DROP INDEX `modified`
2025-06-12 13:03:28,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` DROP INDEX `modified`
2025-06-12 13:03:28,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` DROP INDEX `modified`
2025-06-12 13:03:28,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` DROP INDEX `modified`
2025-06-12 13:03:28,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` DROP INDEX `modified`
2025-06-12 13:03:28,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` DROP INDEX `modified`
2025-06-12 13:03:28,405 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` DROP INDEX `modified`
2025-06-12 13:03:28,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` DROP INDEX `modified`
2025-06-12 13:03:28,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` DROP INDEX `modified`
2025-06-12 13:03:28,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` DROP INDEX `modified`
2025-06-12 13:03:28,533 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` DROP INDEX `modified`
2025-06-12 13:03:28,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` DROP INDEX `modified`
2025-06-12 13:03:28,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` DROP INDEX `modified`
2025-06-12 13:03:28,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` DROP INDEX `modified`
2025-06-12 13:03:28,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` DROP INDEX `modified`
2025-06-12 13:03:28,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` DROP INDEX `modified`
2025-06-12 13:03:28,735 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` DROP INDEX `modified`
2025-06-12 13:03:28,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` DROP INDEX `modified`
2025-06-12 13:03:28,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` DROP INDEX `modified`
2025-06-12 13:03:28,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` DROP INDEX `modified`
2025-06-12 13:03:28,873 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` DROP INDEX `modified`
2025-06-12 13:03:28,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` DROP INDEX `modified`
2025-06-12 13:03:28,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` DROP INDEX `modified`
2025-06-12 13:03:28,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` DROP INDEX `modified`
2025-06-12 13:03:29,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` DROP INDEX `modified`
2025-06-12 13:03:29,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` DROP INDEX `modified`
2025-06-12 13:03:29,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` DROP INDEX `modified`
2025-06-12 13:03:29,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` DROP INDEX `modified`
2025-06-12 13:03:29,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` DROP INDEX `modified`
2025-06-12 13:03:29,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` DROP INDEX `modified`
2025-06-12 13:03:29,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` DROP INDEX `modified`
2025-06-12 13:03:29,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` DROP INDEX `modified`
2025-06-12 13:03:29,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` DROP INDEX `modified`
2025-06-12 13:03:29,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` DROP INDEX `modified`
2025-06-12 13:03:29,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Health Monitor Company` DROP INDEX `modified`
2025-06-12 13:03:29,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Parcel` DROP INDEX `modified`
2025-06-12 13:03:29,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Service Item` DROP INDEX `modified`
2025-06-12 13:03:29,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabBudget Account` DROP INDEX `modified`
2025-06-12 13:03:29,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` DROP INDEX `modified`
2025-06-12 13:03:29,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` DROP INDEX `modified`
2025-06-12 13:03:29,531 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` DROP INDEX `modified`
2025-06-12 13:03:29,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlanket Order Item` DROP INDEX `modified`
2025-06-12 13:03:29,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Plan Item` DROP INDEX `modified`
2025-06-12 13:03:29,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record Details` DROP INDEX `modified`
2025-06-12 13:03:29,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record Item` DROP INDEX `modified`
2025-06-12 13:03:29,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Payment` DROP INDEX `modified`
2025-06-12 13:03:29,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` DROP INDEX `modified`
2025-06-12 13:03:29,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `modified`
2025-06-12 13:03:29,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `modified`
2025-06-12 13:03:29,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabTop Bar Item` DROP INDEX `modified`
2025-06-12 13:03:29,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Visit Purpose` DROP INDEX `modified`
2025-06-12 13:03:29,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllowed To Transact With` DROP INDEX `modified`
2025-06-12 13:03:29,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Explosion Item` DROP INDEX `modified`
2025-06-12 13:03:29,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Route Redirect` DROP INDEX `modified`
2025-06-12 13:03:30,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Item` DROP INDEX `modified`
2025-06-12 13:03:30,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Header` DROP INDEX `modified`
2025-06-12 13:03:30,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabReview Level` DROP INDEX `modified`
2025-06-12 13:03:30,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `modified`
2025-06-12 13:03:30,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `modified`
2025-06-12 13:03:30,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity Item` DROP INDEX `modified`
2025-06-12 13:03:30,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Allowed Types` DROP INDEX `modified`
2025-06-12 13:03:30,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Working Hour` DROP INDEX `modified`
2025-06-12 13:03:30,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask Depends On` DROP INDEX `modified`
2025-06-12 13:03:30,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Stop` DROP INDEX `modified`
2025-06-12 13:03:30,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Task` DROP INDEX `modified`
2025-06-12 13:03:30,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension Detail` DROP INDEX `modified`
2025-06-12 13:03:30,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabUnreconcile Payment Entries` DROP INDEX `modified`
2025-06-12 13:03:30,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Review Objective` DROP INDEX `modified`
2025-06-12 13:03:30,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Goal Objective` DROP INDEX `modified`
2025-06-12 13:03:30,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Accounting Ledger Items` DROP INDEX `modified`
2025-06-12 13:03:30,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabExchange Rate Revaluation Account` DROP INDEX `modified`
2025-06-12 13:03:30,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Reorder` DROP INDEX `modified`
2025-06-12 13:03:30,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport Filter` DROP INDEX `modified`
2025-06-12 13:03:30,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Account` DROP INDEX `modified`
2025-06-12 13:03:30,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabForm Tour Step` DROP INDEX `modified`
2025-06-12 13:03:30,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Custom Block` DROP INDEX `modified`
2025-06-12 13:03:30,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking Slip Item` DROP INDEX `modified`
2025-06-12 13:03:30,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts Customer` DROP INDEX `modified`
2025-06-12 13:03:30,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabNote Seen By` DROP INDEX `modified`
2025-06-12 13:03:30,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item Reference` DROP INDEX `modified`
2025-06-12 13:03:30,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Item` DROP INDEX `modified`
2025-06-12 13:03:30,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` DROP INDEX `modified`
2025-06-12 13:03:30,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Sub-Contractors And Visitors` DROP INDEX `modified`
2025-06-12 13:03:31,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Number Card` DROP INDEX `modified`
2025-06-12 13:03:31,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Subcontractors and Visitors` DROP INDEX `modified`
2025-06-12 13:03:31,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Scrap Item` DROP INDEX `modified`
2025-06-12 13:03:31,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Credit Limit` DROP INDEX `modified`
2025-06-12 13:03:31,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Payment Ledger Items` DROP INDEX `modified`
2025-06-12 13:03:31,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Supplier` DROP INDEX `modified`
2025-06-12 13:03:31,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient` DROP INDEX `modified`
2025-06-12 13:03:31,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Supplied Item` DROP INDEX `modified`
2025-06-12 13:03:31,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form List Column` DROP INDEX `modified`
2025-06-12 13:03:31,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `modified`
2025-06-12 13:03:31,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabHas Role` DROP INDEX `modified`
2025-06-12 13:03:31,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Social Login` DROP INDEX `modified`
2025-06-12 13:03:31,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabHas Domain` DROP INDEX `modified`
2025-06-12 13:03:31,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlock Module` DROP INDEX `modified`
2025-06-12 13:03:31,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Data` DROP INDEX `modified`
2025-06-12 13:03:31,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany History` DROP INDEX `modified`
2025-06-12 13:03:31,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabAbout Us Team Member` DROP INDEX `modified`
2025-06-12 13:03:31,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabPortal Menu Item` DROP INDEX `modified`
2025-06-12 13:03:31,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Sidebar Item` DROP INDEX `modified`
2025-06-12 13:03:31,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Meta Tag` DROP INDEX `modified`
2025-06-12 13:03:31,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabLDAP Group Mapping` DROP INDEX `modified`
2025-06-12 13:03:31,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabDriving License Category` DROP INDEX `modified`
2025-06-12 13:03:31,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduct Bundle Item` DROP INDEX `modified`
2025-06-12 13:03:31,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page Block` DROP INDEX `modified`
2025-06-12 13:03:31,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabLogs To Clear` DROP INDEX `modified`
2025-06-12 13:03:31,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Update Batch` DROP INDEX `modified`
2025-06-12 13:03:31,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Note` DROP INDEX `modified`
2025-06-12 13:03:31,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Email` DROP INDEX `modified`
2025-06-12 13:03:31,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `modified`
2025-06-12 13:03:31,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `modified`
2025-06-12 13:03:32,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `modified`
2025-06-12 13:03:32,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Quick List` DROP INDEX `modified`
2025-06-12 13:03:32,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `modified`
2025-06-12 13:03:32,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `modified`
2025-06-12 13:03:32,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `modified`
2025-06-12 13:03:32,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect Lead` DROP INDEX `modified`
2025-06-12 13:03:32,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect Opportunity` DROP INDEX `modified`
2025-06-12 13:03:32,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Filter Field` DROP INDEX `modified`
2025-06-12 13:03:32,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabVariant Field` DROP INDEX `modified`
2025-06-12 13:03:32,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Reference` DROP INDEX `modified`
2025-06-12 13:03:32,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpening Invoice Creation Tool Item` DROP INDEX `modified`
2025-06-12 13:03:32,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Action Permitted Role` DROP INDEX `modified`
2025-06-12 13:03:32,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabCash Flow Mapping Template Details` DROP INDEX `modified`
2025-06-12 13:03:32,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet Detail` DROP INDEX `modified`
2025-06-12 13:03:32,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Group Table` DROP INDEX `modified`
2025-06-12 13:03:32,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabCost Center Allocation Percentage` DROP INDEX `modified`
2025-06-12 13:03:32,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Rate` DROP INDEX `modified`
2025-06-12 13:03:32,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `modified`
2025-06-12 13:03:32,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType State` DROP INDEX `modified`
2025-06-12 13:03:32,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabKanban Board Column` DROP INDEX `modified`
2025-06-12 13:03:32,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Merge Accounts` DROP INDEX `modified`
2025-06-12 13:03:32,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabNewsletter Email Group` DROP INDEX `modified`
2025-06-12 13:03:32,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabNewsletter Attachment` DROP INDEX `modified`
2025-06-12 13:03:32,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Tax` DROP INDEX `modified`
2025-06-12 13:03:32,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Team` DROP INDEX `modified`
2025-06-12 13:03:32,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency Exchange Settings Details` DROP INDEX `modified`
2025-06-12 13:03:32,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency Exchange Settings Result` DROP INDEX `modified`
2025-06-12 13:03:32,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompetitor Detail` DROP INDEX `modified`
2025-06-12 13:03:32,918 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Timesheet` DROP INDEX `modified`
2025-06-12 13:03:32,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year Company` DROP INDEX `modified`
2025-06-12 13:03:32,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabIMAP Folder` DROP INDEX `modified`
2025-06-12 13:03:33,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `modified`
2025-06-12 13:03:33,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `modified`
2025-06-12 13:03:33,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Detail` DROP INDEX `modified`
2025-06-12 13:03:33,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` DROP INDEX `modified`
2025-06-12 13:03:33,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Asset Item` DROP INDEX `modified`
2025-06-12 13:03:33,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Quality Inspection Parameter` DROP INDEX `modified`
2025-06-12 13:03:33,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` DROP INDEX `modified`
2025-06-12 13:03:33,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `modified`
2025-06-12 13:03:33,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabSub Operation` DROP INDEX `modified`
2025-06-12 13:03:33,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabSouth Africa VAT Account` DROP INDEX `modified`
2025-06-12 13:03:33,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `modified`
2025-06-12 13:03:33,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Level Priority` DROP INDEX `modified`
2025-06-12 13:03:33,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item Supplied` DROP INDEX `modified`
2025-06-12 13:03:33,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item Supplied` DROP INDEX `modified`
2025-06-12 13:03:33,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax` DROP INDEX `modified`
2025-06-12 13:03:33,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabSLA Fulfilled On Status` DROP INDEX `modified`
2025-06-12 13:03:33,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Detail` DROP INDEX `modified`
2025-06-12 13:03:33,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Layout Field` DROP INDEX `modified`
2025-06-12 13:03:33,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Item` DROP INDEX `modified`
2025-06-12 13:03:33,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Group Item` DROP INDEX `modified`
2025-06-12 13:03:33,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabCampaign Item` DROP INDEX `modified`
2025-06-12 13:03:33,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Partner Item` DROP INDEX `modified`
2025-06-12 13:03:33,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerritory Item` DROP INDEX `modified`
2025-06-12 13:03:33,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Group Item` DROP INDEX `modified`
2025-06-12 13:03:33,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `modified`
2025-06-12 13:03:33,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `modified`
2025-06-12 13:03:33,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabPersonal Data Deletion Step` DROP INDEX `modified`
2025-06-12 13:03:34,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Item` DROP INDEX `modified`
2025-06-12 13:03:34,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Search Fields` DROP INDEX `modified`
2025-06-12 13:03:34,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Group Member` DROP INDEX `modified`
2025-06-12 13:03:34,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Operation` DROP INDEX `modified`
2025-06-12 13:03:34,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Template Task` DROP INDEX `modified`
2025-06-12 13:03:34,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Terms Template Detail` DROP INDEX `modified`
2025-06-12 13:03:34,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Attribute` DROP INDEX `modified`
2025-06-12 13:03:34,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabHomepage Featured Product` DROP INDEX `modified`
2025-06-12 13:03:34,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `modified`
2025-06-12 13:03:34,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Invoice` DROP INDEX `modified`
2025-06-12 13:03:34,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection Reading` DROP INDEX `modified`
2025-06-12 13:03:34,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` DROP INDEX `modified`
2025-06-12 13:03:34,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Type Module` DROP INDEX `modified`
2025-06-12 13:03:34,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `modified`
2025-06-12 13:03:34,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `modified`
2025-06-12 13:03:34,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Select Document Type` DROP INDEX `modified`
2025-06-12 13:03:34,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabPledge` DROP INDEX `modified`
2025-06-12 13:03:34,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabUnpledge` DROP INDEX `modified`
2025-06-12 13:03:34,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabProposed Pledge` DROP INDEX `modified`
2025-06-12 13:03:34,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Chart` DROP INDEX `modified`
2025-06-12 13:03:34,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Delivery Note` DROP INDEX `modified`
2025-06-12 13:03:34,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `modified`
2025-06-12 13:03:34,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `modified`
2025-06-12 13:03:34,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllowed Dimension` DROP INDEX `modified`
2025-06-12 13:03:34,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabApplicable On Account` DROP INDEX `modified`
2025-06-12 13:03:34,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncoming Call Handling Schedule` DROP INDEX `modified`
2025-06-12 13:03:34,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuery Parameters` DROP INDEX `modified`
2025-06-12 13:03:34,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `modified`
2025-06-12 13:03:34,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuto Repeat Day` DROP INDEX `modified`
2025-06-12 13:03:34,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Supplier` DROP INDEX `modified`
2025-06-12 13:03:35,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `modified`
2025-06-12 13:03:35,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Feedback Parameter` DROP INDEX `modified`
2025-06-12 13:03:35,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Procedure Process` DROP INDEX `modified`
2025-06-12 13:03:35,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Template Field` DROP INDEX `modified`
2025-06-12 13:03:35,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Material Request Warehouse` DROP INDEX `modified`
2025-06-12 13:03:35,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment Detail` DROP INDEX `modified`
2025-06-12 13:03:35,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Action Resolution` DROP INDEX `modified`
2025-06-12 13:03:35,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Payment Method` DROP INDEX `modified`
2025-06-12 13:03:35,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `modified`
2025-06-12 13:03:35,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabLog Setting User` DROP INDEX `modified`
2025-06-12 13:03:35,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabAssignment Rule User` DROP INDEX `modified`
2025-06-12 13:03:35,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabUAE VAT Account` DROP INDEX `modified`
2025-06-12 13:03:35,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` DROP INDEX `modified`
2025-06-12 13:03:35,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Theme Ignore App` DROP INDEX `modified`
2025-06-12 13:03:35,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabDependent Task` DROP INDEX `modified`
2025-06-12 13:03:35,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Slideshow Item` DROP INDEX `modified`
2025-06-12 13:03:35,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Naming Rule Condition` DROP INDEX `modified`
2025-06-12 13:03:35,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Order Reference` DROP INDEX `modified`
2025-06-12 13:03:35,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport Column` DROP INDEX `modified`
2025-06-12 13:03:35,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Recipient` DROP INDEX `modified`
2025-06-12 13:03:35,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Digest Recipient` DROP INDEX `modified`
2025-06-12 13:03:35,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabPSOA Project` DROP INDEX `modified`
2025-06-12 13:03:35,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Lost Reason Detail` DROP INDEX `modified`
2025-06-12 13:03:35,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity Lost Reason Detail` DROP INDEX `modified`
2025-06-12 13:03:35,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabOAuth Scope` DROP INDEX `modified`
2025-06-12 13:03:35,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Letter Text` DROP INDEX `modified`
2025-06-12 13:03:35,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Day` DROP INDEX `modified`
2025-06-12 13:03:35,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `modified`
2025-06-12 13:03:35,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `modified`
2025-06-12 13:03:35,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Plan Detail` DROP INDEX `modified`
2025-06-12 13:03:36,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabPause SLA On Status` DROP INDEX `modified`
2025-06-12 13:03:36,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Opening Entry Detail` DROP INDEX `modified`
2025-06-12 13:03:36,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile User` DROP INDEX `modified`
2025-06-12 13:03:36,128 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` DROP INDEX `modified`
2025-06-12 13:03:36,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabSocial Link Settings` DROP INDEX `modified`
2025-06-12 13:03:36,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabOnboarding Permission` DROP INDEX `modified`
2025-06-12 13:03:36,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabOnboarding Step Map` DROP INDEX `modified`
2025-06-12 13:03:36,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Template Account` DROP INDEX `modified`
2025-06-12 13:03:36,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card Link` DROP INDEX `modified`
2025-06-12 13:03:36,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabHoliday` DROP INDEX `modified`
2025-06-12 13:03:36,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact Phone` DROP INDEX `modified`
2025-06-12 13:03:36,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `modified`
2025-06-12 13:03:36,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart Link` DROP INDEX `modified`
2025-06-12 13:03:36,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `modified`
2025-06-12 13:03:36,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart Field` DROP INDEX `modified`
2025-06-12 13:03:36,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` DROP INDEX `modified`
2025-06-12 13:03:36,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiscounted Invoice` DROP INDEX `modified`
2025-06-12 13:03:36,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Payments` DROP INDEX `modified`
2025-06-12 13:03:36,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `modified`
2025-06-12 13:03:36,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `modified`
2025-06-12 13:03:36,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Booking Slots` DROP INDEX `modified`
2025-06-12 13:03:36,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabDynamic Link` DROP INDEX `modified`
2025-06-12 13:03:36,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Subscribed Document` DROP INDEX `modified`
2025-06-12 13:03:36,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement Item` DROP INDEX `modified`
2025-06-12 13:03:36,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `modified`
2025-06-12 13:03:36,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact Email` DROP INDEX `modified`
2025-06-12 13:03:36,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabAssignment Rule Day` DROP INDEX `modified`
2025-06-12 13:03:36,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabGlobal Search DocType` DROP INDEX `modified`
2025-06-12 13:03:37,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `modified`
2025-06-12 13:03:37,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoute Steps Table` DROP INDEX `modified`
2025-06-12 13:03:37,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepayment Schedule` DROP INDEX `modified`
2025-06-12 13:03:37,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabAvailability Of Slots` DROP INDEX `modified`
2025-06-12 13:03:37,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin List` DROP INDEX `modified`
2025-06-12 13:03:37,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Field` DROP INDEX `modified`
2025-06-12 13:03:37,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `modified`
2025-06-12 13:03:37,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Details` DROP INDEX `modified`
2025-06-12 13:03:37,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Details` DROP INDEX `modified`
2025-06-12 13:03:37,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Details` DROP INDEX `modified`
2025-06-12 13:03:37,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Details` DROP INDEX `modified`
2025-06-12 13:03:37,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Details` DROP INDEX `modified`
2025-06-12 13:03:37,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Details` DROP INDEX `modified`
2025-06-12 13:03:37,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Details` DROP INDEX `modified`
2025-06-12 13:03:37,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `modified`
2025-06-12 13:03:37,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Checklist` DROP INDEX `modified`
2025-06-12 13:03:37,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Details` DROP INDEX `modified`
2025-06-12 13:03:37,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist Details` DROP INDEX `modified`
2025-06-12 13:03:37,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `modified`
2025-06-12 13:03:37,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake System Details` DROP INDEX `modified`
2025-06-12 13:03:37,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Checklist` DROP INDEX `modified`
2025-06-12 13:03:38,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabSession Default` DROP INDEX `modified`
2025-06-12 13:03:38,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `modified`
2025-06-12 13:03:38,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabCampaign Email Schedule` DROP INDEX `modified`
2025-06-12 13:03:38,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Medium Timeslot` DROP INDEX `modified`
2025-06-12 13:03:38,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Meeting Minutes` DROP INDEX `modified`
2025-06-12 13:03:38,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Feedback Template Parameter` DROP INDEX `modified`
2025-06-12 13:03:38,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Meeting Agenda` DROP INDEX `modified`
2025-06-12 13:03:38,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Link` DROP INDEX `modified`
2025-06-12 13:03:38,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Item Code` DROP INDEX `modified`
2025-06-12 13:03:38,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Brand` DROP INDEX `modified`
2025-06-12 13:03:38,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Item Group` DROP INDEX `modified`
2025-06-12 13:03:38,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Detail` DROP INDEX `modified`
2025-06-12 13:03:38,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Detail` DROP INDEX `modified`
2025-06-12 13:03:38,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabCashier Closing Payments` DROP INDEX `modified`
2025-06-12 13:03:38,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabHomepage Section Card` DROP INDEX `modified`
2025-06-12 13:03:38,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Criteria` DROP INDEX `modified`
2025-06-12 13:03:38,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Variable` DROP INDEX `modified`
2025-06-12 13:03:38,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Standing` DROP INDEX `modified`
2025-06-12 13:03:38,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `modified`
2025-06-12 13:03:38,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Detail` DROP INDEX `modified`
2025-06-12 13:03:38,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabLost Reason Detail` DROP INDEX `modified`
2025-06-12 13:03:38,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax Template Detail` DROP INDEX `modified`
2025-06-12 13:03:38,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Mapping` DROP INDEX `modified`
2025-06-12 13:03:38,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `modified`
2025-06-12 13:03:38,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `modified`
2025-06-12 13:03:38,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `modified`
2025-06-12 13:03:38,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `modified`
2025-06-12 13:03:38,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabLinked Location` DROP INDEX `modified`
2025-06-12 13:03:38,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `modified`
2025-06-12 13:03:38,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `modified`
2025-06-12 13:03:39,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupport Search Source` DROP INDEX `modified`
2025-06-12 13:03:39,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Team Member` DROP INDEX `modified`
2025-06-12 13:03:39,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category Account` DROP INDEX `modified`
2025-06-12 13:03:39,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Fulfilment Checklist` DROP INDEX `modified`
2025-06-12 13:03:39,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Template Fulfilment Terms` DROP INDEX `modified`
2025-06-12 13:03:39,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabClosed Document` DROP INDEX `modified`
2025-06-12 13:03:39,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Point Entry Redemption` DROP INDEX `modified`
2025-06-12 13:03:39,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Customer Detail` DROP INDEX `modified`
2025-06-12 13:03:39,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `modified`
2025-06-12 13:03:39,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment Account` DROP INDEX `modified`
2025-06-12 13:03:39,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `modified`
2025-06-12 13:03:39,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabCash Flow Mapping Accounts` DROP INDEX `modified`
2025-06-12 13:03:39,391 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `modified`
2025-06-12 13:03:39,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `modified`
2025-06-12 13:03:39,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program Collection` DROP INDEX `modified`
2025-06-12 13:03:39,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Balance` DROP INDEX `modified`
2025-06-12 13:03:39,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `modified`
2025-06-12 13:03:39,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Material Request` DROP INDEX `modified`
2025-06-12 13:03:39,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sales Order` DROP INDEX `modified`
2025-06-12 13:03:39,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `modified`
2025-06-12 13:03:39,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `modified`
2025-06-12 13:03:39,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `modified`
2025-06-12 13:03:39,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `modified`
2025-06-12 13:03:39,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `modified`
2025-06-12 13:03:39,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `modified`
2025-06-12 13:03:39,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `modified`
2025-06-12 13:03:39,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `modified`
2025-06-12 13:03:39,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `modified`
2025-06-12 13:03:39,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `modified`
2025-06-12 13:03:39,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `modified`
2025-06-12 13:03:39,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `modified`
2025-06-12 13:03:40,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `modified`
2025-06-12 13:03:40,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `modified`
2025-06-12 13:03:40,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `modified`
2025-06-12 13:03:40,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Operation` DROP INDEX `modified`
2025-06-12 13:03:40,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Item` DROP INDEX `modified`
2025-06-12 13:03:40,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Item Group` DROP INDEX `modified`
2025-06-12 13:03:40,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Customer Group` DROP INDEX `modified`
2025-06-12 13:03:40,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Item Group` DROP INDEX `modified`
2025-06-12 13:03:40,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM Conversion Detail` DROP INDEX `modified`
2025-06-12 13:03:40,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Country` DROP INDEX `modified`
2025-06-12 13:03:40,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Condition` DROP INDEX `modified`
2025-06-12 13:03:40,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice List Country` DROP INDEX `modified`
2025-06-12 13:03:40,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabMonthly Distribution Percentage` DROP INDEX `modified`
2025-06-12 13:03:40,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Website Specification` DROP INDEX `modified`
2025-06-12 13:03:40,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant` DROP INDEX `modified`
2025-06-12 13:03:40,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute Value` DROP INDEX `modified`
2025-06-12 13:03:40,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Internal Work History` DROP INDEX `modified`
2025-06-12 13:03:40,547 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee External Work History` DROP INDEX `modified`
2025-06-12 13:03:40,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Education` DROP INDEX `modified`
2025-06-12 13:03:49,324 WARNING database DDL Query made to DB:
truncate `tabAccount Closing Balance`
2025-06-12 13:03:51,948 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-06-12 13:03:52,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-12 13:03:53,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-06-12 13:03:54,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-06-12 13:03:54,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-06-12 13:03:54,666 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-06-12 13:03:54,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-12 13:03:54,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-06-12 13:03:54,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0
2025-06-12 13:03:54,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-06-12 13:03:54,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-06-12 13:03:55,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-12 13:04:04,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-06-12 13:04:04,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-12 13:04:04,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-06-12 13:04:04,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0
2025-06-12 13:04:04,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-12 13:04:04,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 13:04:05,323 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 13:04:05,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-12 13:04:05,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-12 13:04:05,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0
2025-06-12 13:04:05,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `custom_employment_status` varchar(140)
2025-06-12 13:04:05,765 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-12 13:04:06,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `custom_employment_status` varchar(140), MODIFY `ctc` decimal(21,9) not null default 0
2025-06-12 13:04:06,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0
2025-06-12 13:04:06,504 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-06-12 14:19:57,073 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_ccc05202a3e8abfd'@'localhost'
2025-06-12 14:19:57,074 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_ccc05202a3e8abfd`
2025-06-12 14:19:57,077 WARNING database DDL Query made to DB:
CREATE USER '_ccc05202a3e8abfd'@'localhost' IDENTIFIED BY 'RbLiMvF95mYBpnM5'
2025-06-12 14:19:57,078 WARNING database DDL Query made to DB:
CREATE DATABASE `_ccc05202a3e8abfd` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-12 14:19:57,538 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:57,554 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published int(1) not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-06-12 14:19:57,586 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:57,920 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140) default 'Blue',
`custom` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:58,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` int(1) not null default 0, ADD COLUMN `is_virtual` int(1) not null default 0, ADD COLUMN `sort_options` int(1) not null default 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-06-12 14:19:58,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `width` varchar(10), MODIFY `label` varchar(140), MODIFY `fieldtype` varchar(140) default 'Data', MODIFY `mandatory_depends_on` longtext, MODIFY `print_width` varchar(10), MODIFY `oldfieldname` varchar(140), MODIFY `fieldname` varchar(140), MODIFY `depends_on` longtext, MODIFY `read_only_depends_on` longtext, MODIFY `collapsible_depends_on` longtext, MODIFY `oldfieldtype` varchar(140), MODIFY `precision` varchar(140)
2025-06-12 14:19:58,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` int(1) not null default 0, ADD COLUMN `select` int(1) not null default 0
2025-06-12 14:19:58,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `role` varchar(140)
2025-06-12 14:19:58,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-06-12 14:19:58,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `is_child_table` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-06-12 14:19:58,528 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ui_tour` int(1) not null default 0,
`is_table_field` int(1) not null default 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) default 'Bottom',
`hide_buttons` int(1) not null default 0,
`popover_element` int(1) not null default 0,
`modal_trigger` int(1) not null default 0,
`offset_x` int(11) not null default 0,
`offset_y` int(11) not null default 0,
`next_on_click` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default '0',
`has_next_condition` int(1) not null default 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:58,602 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) default 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` int(1) not null default 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` int(1) not null default 0,
`track_steps` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`save_on_complete` int(1) not null default 0,
`first_document` int(1) not null default 0,
`include_name_field` int(1) not null default 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:58,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `is_calendar_and_gantt` int(1) not null default 0, ADD COLUMN `quick_entry` int(1) not null default 0, ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `track_views` int(1) not null default 0, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `documentation` varchar(140), ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `allow_events_in_timeline` int(1) not null default 0, ADD COLUMN `allow_auto_repeat` int(1) not null default 0, ADD COLUMN `make_attachments_public` int(1) not null default 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` int(1) not null default 0, ADD COLUMN `show_preview_popup` int(1) not null default 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `index_web_pages_for_search` int(1) not null default 1, ADD COLUMN `row_format` varchar(140) default 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-06-12 14:19:58,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `timeline_field` varchar(140), MODIFY `sort_field` varchar(140) default 'modified', MODIFY `module` varchar(140), MODIFY `route` varchar(140), MODIFY `allow_rename` int(1) not null default 1, MODIFY `restrict_to_domain` varchar(140), MODIFY `_user_tags` text, MODIFY `subject_field` varchar(140), MODIFY `document_type` varchar(140), MODIFY `icon` varchar(140), MODIFY `sender_field` varchar(140), MODIFY `title_field` varchar(140), MODIFY `sort_order` varchar(140) default 'DESC', MODIFY `migration_hash` varchar(140), MODIFY `default_print_format` varchar(140), MODIFY `engine` varchar(140) default 'InnoDB', MODIFY `search_fields` varchar(140), MODIFY `is_published_field` varchar(140), MODIFY `color` varchar(140), MODIFY `image_field` varchar(140), MODIFY `autoname` varchar(140), MODIFY `website_search_field` varchar(140)
2025-06-12 14:19:58,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-06-12 14:19:59,027 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_name` varchar(140) unique,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` int(1) not null default 0,
`is_custom` int(1) not null default 0,
`desk_access` int(1) not null default 1,
`two_factor_auth` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,125 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,229 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(140),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int(11) not null default 0,
`link_filters` json,
`fieldtype` varchar(140) default 'Data',
`precision` varchar(140),
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`read_only` int(1) not null default 0,
`ignore_user_permissions` int(1) not null default 0,
`hidden` int(1) not null default 0,
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`no_copy` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`search_index` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`translatable` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`description` text,
`permlevel` int(11) not null default 0,
`width` varchar(140),
`columns` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,320 WARNING database DDL Query made to DB:
create table `tabProperty Setter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`doctype_or_field` varchar(140),
`doc_type` varchar(140),
`field_name` varchar(140),
`row_name` varchar(140),
`module` varchar(140),
`property` varchar(140),
`property_type` varchar(140),
`value` text,
`default_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `doc_type`(`doc_type`),
index `field_name`(`field_name`),
index `property`(`property`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,462 WARNING database DDL Query made to DB:
create table `tabWeb Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`doc_type` varchar(140),
`module` varchar(140),
`is_standard` int(1) not null default 0,
`introduction_text` longtext,
`anonymous` int(1) not null default 0,
`login_required` int(1) not null default 0,
`apply_document_permissions` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`allow_multiple` int(1) not null default 0,
`allow_delete` int(1) not null default 0,
`allow_incomplete` int(1) not null default 0,
`allow_comments` int(1) not null default 0,
`allow_print` int(1) not null default 0,
`print_format` varchar(140),
`max_attachment_size` int(11) not null default 0,
`show_attachments` int(1) not null default 0,
`allowed_embedding_domains` text,
`condition_json` json,
`show_list` int(1) not null default 0,
`list_title` varchar(140),
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`button_label` varchar(140) default 'Save',
`banner_image` text,
`breadcrumbs` longtext,
`success_title` varchar(140),
`success_url` varchar(140),
`success_message` text,
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`client_script` longtext,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,528 WARNING database DDL Query made to DB:
create table `tabWeb Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Section',
`standard` int(1) not null default 0,
`module` varchar(140),
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,606 WARNING database DDL Query made to DB:
create table `tabWeb Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
`allow_read_on_all_link_options` int(1) not null default 0,
`reqd` int(1) not null default 0,
`read_only` int(1) not null default 0,
`show_in_filter` int(1) not null default 0,
`hidden` int(1) not null default 0,
`options` text,
`max_length` int(11) not null default 0,
`max_value` int(11) not null default 0,
`precision` varchar(140),
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`description` text,
`default` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,661 WARNING database DDL Query made to DB:
create table `tabPortal Menu Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`enabled` int(1) not null default 0,
`route` varchar(140),
`reference_doctype` varchar(140),
`role` varchar(140),
`target` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,733 WARNING database DDL Query made to DB:
create table `tabNumber Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`label` varchar(140),
`type` varchar(140),
`report_name` varchar(140),
`method` varchar(140),
`function` varchar(140),
`aggregate_function_based_on` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`report_field` varchar(140),
`report_function` varchar(140),
`is_public` int(1) not null default 0,
`currency` varchar(140),
`filters_config` longtext,
`show_percentage_stats` int(1) not null default 1,
`stats_time_interval` varchar(140) default 'Daily',
`filters_json` longtext,
`dynamic_filters_json` longtext,
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,841 WARNING database DDL Query made to DB:
create table `tabDashboard Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_name` varchar(140) unique,
`chart_type` varchar(140),
`report_name` varchar(140),
`use_report_chart` int(1) not null default 0,
`x_field` varchar(140),
`source` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`based_on` varchar(140),
`value_based_on` varchar(140),
`group_by_type` varchar(140) default 'Count',
`group_by_based_on` varchar(140),
`aggregate_function_based_on` varchar(140),
`number_of_groups` int(11) not null default 0,
`is_public` int(1) not null default 0,
`heatmap_year` varchar(140),
`timespan` varchar(140),
`from_date` date,
`to_date` date,
`time_interval` varchar(140),
`timeseries` int(1) not null default 0,
`type` varchar(140) default 'Line',
`currency` varchar(140),
`filters_json` longtext,
`dynamic_filters_json` longtext,
`custom_options` longtext,
`color` varchar(140),
`last_synced_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,907 WARNING database DDL Query made to DB:
create table `tabDashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dashboard_name` varchar(140) unique,
`is_default` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_options` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:19:59,956 WARNING database DDL Query made to DB:
create table `tabOnboarding Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,045 WARNING database DDL Query made to DB:
create table `tabOnboarding Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`is_complete` int(1) not null default 0,
`is_skipped` int(1) not null default 0,
`description` longtext,
`intro_video_url` varchar(140),
`action` varchar(140),
`action_label` varchar(140),
`reference_document` varchar(140),
`show_full_form` int(1) not null default 0,
`show_form_tour` int(1) not null default 0,
`form_tour` varchar(140),
`is_single` int(1) not null default 0,
`reference_report` varchar(140),
`report_reference_doctype` varchar(140),
`report_type` varchar(140),
`report_description` varchar(140),
`path` varchar(140),
`callback_title` varchar(140),
`callback_message` text,
`validate_action` int(1) not null default 1,
`field` varchar(140),
`value_to_validate` varchar(140),
`video_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,116 WARNING database DDL Query made to DB:
create table `tabOnboarding Step Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`step` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,178 WARNING database DDL Query made to DB:
create table `tabModule Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`module` varchar(140),
`success_message` varchar(140),
`documentation_url` varchar(140),
`is_complete` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,255 WARNING database DDL Query made to DB:
create table `tabWorkspace Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Link',
`label` varchar(140),
`icon` varchar(140),
`description` longtext,
`hidden` int(1) not null default 0,
`link_type` varchar(140),
`link_to` varchar(140),
`report_ref_doctype` varchar(140),
`dependencies` varchar(140),
`only_for` varchar(140),
`onboard` int(1) not null default 0,
`is_query_report` int(1) not null default 0,
`link_count` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,312 WARNING database DDL Query made to DB:
create table `tabWorkspace Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,385 WARNING database DDL Query made to DB:
create table `tabWorkspace Shortcut` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`link_to` varchar(140),
`url` varchar(140),
`doc_view` varchar(140),
`kanban_board` varchar(140),
`label` varchar(140),
`icon` varchar(140),
`restrict_to_domain` varchar(140),
`report_ref_doctype` varchar(140),
`stats_filter` longtext,
`color` varchar(140),
`format` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,449 WARNING database DDL Query made to DB:
create table `tabWorkspace Quick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`label` varchar(140),
`quick_list_filter` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,499 WARNING database DDL Query made to DB:
create table `tabWorkspace Number Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_card_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,553 WARNING database DDL Query made to DB:
create table `tabWorkspace Custom Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`custom_block_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,643 WARNING database DDL Query made to DB:
create table `tabWorkspace` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140) unique,
`title` varchar(140),
`sequence_id` decimal(21,9) not null default 0,
`for_user` varchar(140),
`parent_page` varchar(140),
`module` varchar(140),
`icon` varchar(140),
`indicator_color` varchar(140),
`restrict_to_domain` varchar(140),
`hide_custom` int(1) not null default 0,
`public` int(1) not null default 0,
`is_hidden` int(1) not null default 0,
`content` longtext default '[]',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `restrict_to_domain`(`restrict_to_domain`),
index `public`(`public`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,712 WARNING database DDL Query made to DB:
create table `tabPage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_page` int(1) not null default 0,
`page_name` varchar(140) unique,
`title` varchar(140),
`icon` varchar(140),
`module` varchar(140),
`restrict_to_domain` varchar(140),
`standard` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 14:20:00,798 WARNING database DDL Query made to DB:
create table `tabReport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report_name` varchar(140) unique,
`ref_doctype` varchar(140),
`reference_report` varchar(140),
`is_standard` varchar(140),
`module` varchar(140),
`report_type` varchar(140),
`letter_head` varchar(140),
`add_total_row` int(1) not null default 0,
`disabled` int(1) not null default 0,
`prepared_report` int(1) not null default 0,
`add_translate_data` int(1) not null default 0,
`timeout` int(11) not null default 0,
`query` longtext,
`report_script` longtext,
`javascript` longtext,
`json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
